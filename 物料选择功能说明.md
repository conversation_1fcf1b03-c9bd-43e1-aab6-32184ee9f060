# 物料明细弹窗选择物料信息功能说明

## 功能概述

本功能为采购收集系统的物料明细表单添加了物料选择功能，用户可以通过弹窗选择物料，系统会自动填充相关信息，提高数据录入的准确性和效率。

## 主要特性

### 1. 物料选择弹窗
- 集成了现有的 `MaterialSearch` 组件
- 提供物料搜索功能（支持料号、中文品名搜索）
- 支持分页浏览物料列表
- 双击或点击确定按钮选择物料

### 2. 自动填充功能
选择物料后，系统会自动填充以下字段：
- **料号** (`materialNo`) ← `material.matrlno`
- **品别代码** (`categoryCode`) ← `material.bigTypeCode + midTypeCode + leafTypeCode`
- **中文品名** (`materialNameCn`) ← `material.cnmdesc`
- **型号规格** (`modelSpec`) ← `material.nmspec`
- **物料描述** (`materialDesc`) ← `material.cnmdesc`
- **材质** (`materialType`) ← `material.quality`
- **图号** (`drawingNo`) ← `material.picno`
- **图档** (`drawingFile`) ← `material.picno`
- **库存单位** (`stockUnit`) ← `material.unitinv`

### 3. 数据一致性保障
- 所有物料相关字段设为只读状态
- 防止用户手动修改，确保数据准确性
- 只能通过物料选择功能来填充数据

### 4. 表单验证
- 选择物料后自动触发相关字段的表单验证
- 确保必填字段得到正确填充

## 文件修改说明

### 主要修改文件
`src/views/pms/procurementcollection/components/ProcurementCollectionItemForm.vue`

### 模板修改
```vue
<!-- 在料号输入框添加选择按钮 -->
<el-form-item label="料号" prop="materialNo">
  <el-input v-model="form.materialNo" placeholder="请输入料号" readonly>
    <el-button slot="append" icon="el-icon-search" @click="openMaterialSelect">选择物料</el-button>
  </el-input>
</el-form-item>

<!-- 其他字段设为只读 -->
<el-input v-model="form.materialNameCn" placeholder="请输入中文品名" readonly />

<!-- 添加物料选择弹窗 -->
<material-search ref="materialSearch" @success="handleMaterialSelect" />
```

### 脚本修改
```javascript
// 导入物料选择组件
import MaterialSearch from "@/views/pms/pomain/components/MaterialSearch";

export default {
  components: {
    MaterialSearch
  },
  methods: {
    /** 打开物料选择弹窗 */
    openMaterialSelect() {
      this.$refs.materialSearch.open('M'); // 'M' 表示物料类型
    },
    
    /** 处理物料选择结果 */
    handleMaterialSelect(material) {
      if (material) {
        // 将选中的物料信息填充到表单中
        this.form.materialNo = material.matrlno;
        this.form.categoryCode = material.bigTypeCode + material.midTypeCode + material.leafTypeCode;
        this.form.materialNameCn = material.cnmdesc;
        this.form.modelSpec = material.nmspec;
        this.form.materialDesc = material.cnmdesc;
        this.form.materialType = material.quality;
        this.form.drawingNo = material.picno;
        this.form.drawingFile = material.picno;
        this.form.stockUnit = material.unitinv;
        
        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.form.validateField(['materialNo', 'categoryCode', 'materialNameCn', 'stockUnit']);
        });
      }
    }
  }
}
```

## 使用流程

1. **打开物料明细表单**
   - 在采购收集管理页面选择一条记录
   - 点击"新增物料"按钮

2. **选择物料**
   - 在物料明细表单中，点击料号输入框右侧的"选择物料"按钮
   - 系统打开物料选择弹窗

3. **搜索和选择**
   - 在弹窗中可以通过料号或中文品名搜索物料
   - 点击目标物料行或双击选择物料
   - 点击"确定"按钮确认选择

4. **自动填充**
   - 系统自动将选中物料的信息填充到表单各字段
   - 用户可以修改状况码和排序等非物料基础信息字段

5. **保存**
   - 确认信息无误后，点击"确定"保存物料明细

## 技术优势

### 1. 复用性
- 利用现有的 `MaterialSearch` 组件，无需重复开发
- 保持系统架构的一致性

### 2. 数据准确性
- 通过只读字段确保物料基础信息的准确性
- 避免手动输入错误

### 3. 用户体验
- 简化用户操作流程
- 提供直观的物料选择界面
- 减少数据录入时间

### 4. 维护性
- 代码结构清晰，易于理解
- 组件化设计，便于维护和扩展

## 测试建议

1. **功能测试**
   - 测试物料选择弹窗的打开和关闭
   - 测试物料搜索功能
   - 测试物料选择和信息填充
   - 测试表单验证

2. **边界测试**
   - 测试未选择物料直接保存的情况
   - 测试选择物料后取消操作
   - 测试网络异常情况下的处理

3. **兼容性测试**
   - 测试不同浏览器的兼容性
   - 测试不同屏幕分辨率下的显示效果

## 后续扩展建议

1. **批量选择**
   - 支持一次选择多个物料
   - 批量添加物料明细

2. **物料预览**
   - 在选择前提供物料详细信息预览
   - 显示物料图片等更多信息

3. **历史记录**
   - 记录用户最近选择的物料
   - 提供快速选择功能

4. **权限控制**
   - 根据用户权限控制可选择的物料范围
   - 支持物料分类权限管理
