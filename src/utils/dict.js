/**
 * Created by 芋道源码
 *
 * 数据字典工具类
 */
import store from '@/store'

export const DICT_TYPE = {
  USER_TYPE: 'user_type',
  COMMON_STATUS: 'common_status',
  TERMINAL: 'terminal',

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX: 'system_user_sex',
  SYSTEM_MENU_TYPE: 'system_menu_type',
  SYSTEM_ROLE_TYPE: 'system_role_type',
  SYSTEM_DATA_SCOPE: 'system_data_scope',
  SYSTEM_NOTICE_TYPE: 'system_notice_type',
  SYSTEM_LOGIN_TYPE: 'system_login_type',
  SYSTEM_LOGIN_RESULT: 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE: 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE: 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS: 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS: 'system_sms_receive_status',
  SYSTEM_ERROR_CODE_TYPE: 'system_error_code_type',
  SYSTEM_OAUTH2_GRANT_TYPE: 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS: 'system_mail_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE: 'system_notify_template_type',
  SYSTEM_RATE_TYPE: 'system_rate_type',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING: 'infra_boolean_string',
  INFRA_REDIS_TIMEOUT_TYPE: 'infra_redis_timeout_type',
  INFRA_JOB_STATUS: 'infra_job_status',
  INFRA_JOB_LOG_STATUS: 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS: 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE: 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE: 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE: 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE: 'infra_codegen_scene',
  INFRA_FILE_STORAGE: 'infra_file_storage',
  INFRA_OPERATE_TYPE: 'infra_operate_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_CATEGORY: 'bpm_model_category',
  BPM_MODEL_FORM_TYPE: 'bpm_model_form_type',
  BPM_TASK_ASSIGN_RULE_TYPE: 'bpm_task_assign_rule_type',
  BPM_PROCESS_INSTANCE_STATUS: 'bpm_process_instance_status',
  BPM_PROCESS_INSTANCE_RESULT: 'bpm_process_instance_result',
  BPM_TASK_ASSIGN_SCRIPT: 'bpm_task_assign_script',
  BPM_OA_LEAVE_TYPE: 'bpm_oa_leave_type',
  BPM_TASK_STATUS: 'bpm_task_status',

  // ========== PAY 模块 ==========
  PAY_CHANNEL_WECHAT_VERSION: 'pay_channel_wechat_version', // 微信渠道版本

  PAY_CHANNEL_CODE: 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS: 'pay_order_status', // 商户支付订单状态
  PAY_REFUND_STATUS: 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS: 'pay_notify_status', // 商户支付回调状态
  PAY_NOTIFY_TYPE: 'pay_notify_type', // 商户支付回调状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH: 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE: 'mp_message_type', // 消息类型

  // ========== MALL - PRODUCT 模块 ==========
  PRODUCT_SPU_STATUS: 'product_spu_status', // 商品 SPU 状态

  // ========== MALL - ORDER 模块 ==========
  TRADE_AFTER_SALE_STATUS: 'trade_after_sale_status', // 售后 - 状态
  TRADE_AFTER_SALE_WAY: 'trade_after_sale_way', // 售后 - 方式
  TRADE_AFTER_SALE_TYPE: 'trade_after_sale_type', // 售后 - 类型
  TRADE_ORDER_TYPE: 'trade_order_type', // 订单 - 类型
  TRADE_ORDER_STATUS: 'trade_order_status', // 订单 - 状态
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS: 'trade_order_item_after_sale_status', // 订单项 - 售后状态

  // ========== MALL - PROMOTION 模块 ==========
  PROMOTION_DISCOUNT_TYPE: 'promotion_discount_type', // 优惠类型
  PROMOTION_PRODUCT_SCOPE: 'promotion_product_scope', // 营销的商品范围
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE: 'promotion_coupon_template_validity_type', // 优惠劵模板的有限期类型
  PROMOTION_COUPON_STATUS: 'promotion_coupon_status', // 优惠劵的状态
  PROMOTION_COUPON_TAKE_TYPE: 'promotion_coupon_take_type', // 优惠劵的领取方式
  PROMOTION_ACTIVITY_STATUS: 'promotion_activity_status', // 优惠活动的状态
  PROMOTION_CONDITION_TYPE: 'promotion_condition_type', // 营销的条件类型枚举

  // ========== PMS  模块 ==========
  PMS_INQ_STATUS: 'PMS_INQ_STATUS',
  PMS_INQ_TYPE: 'PMS_INQ_TYPE',
  PMS_INQ_BUSI_TYPE: 'PMS_INQ_BUSI_TYPE',
  BILL_TYPE: 'BILL_TYPE',
  PAYSTUS: 'PAYSTUS',
  AP_PAY_TYPE: 'AP_PAY_TYPE',
  NCC_PAY_TYPE: 'NCC_PAY_TYPE',
  IF_CHECK_TAX: 'IF_CHECK_TAX',
  BILL_DOC_TYPE: 'BILL_DOC_TYPE',
  PAY_OTHER_TYPE: 'PAY_OTHER_TYPE',
  AP_PAY_NATURE: 'AP_PAY_NATURE',
  PRE_PAY_TYPE: 'PRE_PAY_TYPE',
  PAY_TERM: 'pay_term', // 付款条件
  PAY_TYPE: 'pay_type', // 付款方式
  GM_PAY_TYPE: 'gm_pay_type',
  ORDER_TYPE: 'order_type', // 订购性质
  CURRENCY_TYPE: 'currency_type', // 币别
  PUR_CODE: 'pur_code', // 采购案别
  RECEIVE_DEPT: 'receive_dept', // 收货单位
  PMS_PROCESS_STATUS: 'pms_process_status',
  YES_NO: 'yes_no',
  PUR_TYPE: 'pur_type',
  PURPOSEID: 'purposeid', // 用途别
  PMS_AUDIT_TYPE: 'pms_audit_type',
  PMS_DOMFRN: 'pms_domfrn',
  PMS_TRADETERMS: 'pms_tradeterms',
  PMS_PACK_RETURN_TYPE: 'pms_pack_return_type',
  PMS_PACK_LEVEL: 'pms_pack_level',
  PMS_PARAM_LEVEL: 'pms_param_level',
  PMS_INSP_LEVEL: 'pms_insp_level',
  PMS_PARAM_DATE: 'PMS_PARAM_DATE',
  PMS_DELIVER_METHOD: 'pms_deliver_method',
  PMS_TRANS_TYPE: 'PMS_TRANS_TYPE',
  PMS_SETTLE_TYPE: 'PMS_SETTLE_TYPE',
  PMS_PAY_TYPE: 'PMS_PAY_TYPE',
  PMS_FLOW_STUS: 'pms_flow_stus',
  PMS_CONFIRM_STUS: 'pms_confirm_stus',
  PMS_EP_TASK_TYPE: 'pms_ep_task_type',
  PMS_EP_STOCK_AFFECTS: 'pms_ep_stock_affects',
  PMS_AUDIT_BUSINESS_TYPE: 'pms_audit_business_type',
  PMS_TAXCONFIG: 'pms_taxConfig',
  PMS_STORE_IN_TYPE: 'pms_store_in_type',
  PLAN_TYPE: 'plan_type', // 计划类别
  ASSIGN_DEPT: 'assign_dept', // 计划类别
  FEE_TYPE: 'fee_type', // 费用类型
  SYSTEM_TYPE: 'system_type',
  PURPOSE_TYPE: 'purpose_type',
  PMS_DELIVER_TYPE: 'pms_deliver_type',
  PMS_TYPE: 'pms_type',
  PMS_PURCODE: 'pms_purcode',
  SHIP_PMS_PURCODE: 'ship_pms_purcode',
  IS_CONTAIN_TAX: 'is_contain_tax',
  PMS_CHECK_STUS: 'pms_check_stus',
  PMS_PO_APPID: 'pms_po_appid',
  BASIC_MATRL_GRADE: 'basic_matrl_grade',
  INSP_ADJUST_TYPE: 'insp_adjust_type',
  PMS_PURPOSE_T: 'pms_purpose_t',
  PMS_BATCHING_RESULT: 'pms_batching_result',
  PAYINFO_PAY_TYPE: 'payinfo_pay_type',
  PAYINFO_PAY_STUS: 'payinfo_pay_stus',
  PMS_DO_STUS: "pms_do_stus",
  SHIP_RELS_PORT: 'ship_rels_port',
  SHIP_PO_TYPE: 'ship_po_type',
  SHIP_INSP_TYPE: 'ship_insp_type',
  INSP_SHIP_ITEM_TYPE: 'insp_ship_item_type',
  INSP_SHIP_TEST_ROOM: 'insp_ship_test_room',
  INSP_SHIP_TRANS_COMPANY: 'insp_ship_trans_company',
  GM_SETTLE_CODE: 'gm_settle_code',
  GM_PRICE_BASEON: 'gm_price_baseon',
  TRAN_FEE_PMS_PURCODE: 'tran_fee_pms_purcode',
  PMS_COST_TYPE: 'pms_cost_type',
  TRANS_FEE_SETTLE_TYPE: 'trans_fee_settle_type',
  SHIP_CHK_TYPE: 'ship_chk_type',
  MPP_PURCHASING: 'MPP_PURCHASING',
  PMS_PO_STUS_XN: 'pms_po_stus_xn',
  PMS_FEE_TYPE: 'pms_fee_type',

  PROCUREMENT_EFFECTIVE_STATUS: 'PROCUREMENT_EFFECTIVE_STATUS',

  // ========== 国贸  模块 ==========
  GM_INVOICE_TYPE: 'gm_invoice_type',
  CONFIRM_STUS: 'confirm_stus',
  POLICY_GOODS_CATEGORY: 'policy_goods_category',
  POLICY_CONTAINER_TYPE: 'policy_container_type',
  POLICY_TRANSIT: 'policy_transit',
  PMS_GM_REGION_TYPE: 'pms_gm_region_type',
  PMS_GM_LC_STUS: 'pms_gm_lc_stus',
  PMS_GM_BANK_LIMIT_STATUS: 'pms_gm_bank_limit_status',
  GM_TRADE_TERMS: 'gm_trade_terms',
  GM_PMS_INVOICE_STUS: 'gm_pms_invoice_stus',

  EXT_SYS_DIC: "EXT_SYS_DIC",
  DEMAND_PLAN_STATUS: "demand_plan_status",
  MR_TYPE: "MR_TYPE",
  COST_CENTER: "COST_CENTER",
  EXPENSE_TYPE: "EXPENSE_TYPE",
  USE_TYPE: "USE_TYPE",
  ADD_ASSETS_TYPE: "ADD_ASSETS_TYPE",
  PROJECT_TYPE: "PROJECT_TYPE",
  // ========== SMS  模块 ==========
  ERP_CARRIER_TYPE: 'ERP_CARRIER_TYPE',
  SMS_GM_PO_DOMFRM: 'SMS_GM_PO_DOMFRM',
  SMS_GM_PO_PAY_TYPE: 'SMS_GM_PO_PAY_TYPE',
  SMS_ORDER_STATUS: 'sms_order_status', // 订单状态
  SMS_ORDER_TYPE: 'sms_order_type', // 订单类型
  SMS_SALE_WAY: 'sms_sale_way', // 销售方式
  SMS_ORDER_WAY: 'sms_order_way', // 订货方式
  SMS_TRANS_WAY: 'sms_trans_way', // 运输方式
  SMS_MEA_WAY: 'sms_mea_way', // 计重方式
  SMS_COLL_WAY: 'sms_coll_way', // 收款方式
  SMS_ORDER_PRICE_WAY: 'sms_order_price_way', // 订货与结算价格方式
  SMS_QTY_UNIT: 'sms_qty_unit', // 数量单位
  SMS_WGT_UNIT: 'sms_wgt_unit', // 重量单位
  FILE_UPLOAD_TYPE: 'file_upload_type', // 文件上传类型
  LOAD_WGT_CLOSE_STATUS: 'load_wgt_close_status', // 销账状态
  SALE_TYPE: 'sale_type', // 销售类型
  MY_SALE_TYPE: 'MY_SALE_TYPE', // 销售类型
  DISPLISTNO_STATUS: 'displistno_status', // 提货单状态
  RETURN_STATUS: 'return_status', // 退货状态
  RETURN_REASON: 'return_reason', // 退货原因
  SMS_SETT_TYPE: 'sms_sett_Type', // 资料类别
  COLL_STATUS: 'coll_status', // 收款状态
  COLL_FROM: 'coll_from', // 收款来源
  FROM_TYPE: 'from_type', // 来源类别
  PARCEL_STATUS: 'parcel_status', // 分配状态
  LOCK_TYPE: 'LOCK_TYPE', // 锁定类别
  SMS_RCV_STATUS: 'SMS_RCV_STATUS', // 货款表状态
  SMS_PRICE_STATUS: 'SMS_PRICE_STATUS', // 货款表状态
  CUST_STATUS: 'CUST_STATUS', // 客户状态
  REFUND_STATUS: 'REFUND_STATUS', // 退款单状态
  END_TYPE: 'END_TYPE', // 结算类型
  EXCHANGE_RATE: 'exchange_rate', // 汇率
  BUSINESS_TYPE: 'BUSINESS_TYPE', // 汇率
  INVOICE_STATE: 'invoice_state',
  TT_FINAL_INVOICE_STATUS: 'TT_FINAL_INVOICE_STATUS',

  // ========== SMS --- 国贸 模块 ==========
  GM_PAYMENT_TYPE: 'GM_PAYMENT_TYPE',
  SMS_NC_FLAG : 'SMS_NC_FLAG',
  SMS_SHIPPING_DETAIL_STATE : 'SMS_SHIPPING_DETAIL_STATE',
  SMS_INVOICE_TYPE : 'SMS_INVOICE_TYPE',
  TRANSPORT_INVOICE_STATUS : 'TRANSPORT_INVOICE_STATUS',
  SMS_CARRIER: 'SMS_CARRIER',
  SMS_EXPENSE_TYPE: 'SMS_EXPENSE_TYPE',
  IT_SOURCE: 'it_source',
  IT_TEXTURE: 'it_texture',
  IT_CONTRACT_TYPE: 'it_contract_type',
  IT_BARGAIN: 'it_bargain',
  IT_PAYMENT_CLAUSE: 'it_payment_clause',
  IT_TRADE_MODE: 'it_trade_mode',
  IT_FACTORY: 'it_factory',
  IT_FACTORY_LINE_B: 'it_factory_line_B',
  IT_FACTORY_LINE_T: 'it_factory_line_T',
  IT_FACTORY_LINE_G: 'it_factory_line_G',
  IT_FACTORY_LINE_L: 'it_factory_line_L',
  IT_FACTORY_LINE_P: 'it_factory_line_P',
  PRODCLASS_BG2: 'prodclass_bg2',
  PRODCLASS_BG: 'prodclass_bg',
  IT_CURRENCY_TYPE: 'it_currency_type',
  IT_CONTROL_TYPE: 'it_control_type',
  IT_CUST_TYPE: 'it_cust_type',
  IT_GM_COMP_TYPE: 'sms_gm_comp_type',
  IT_GM_MANAGE_PROPERTY: 'sms_gm_manage_property',
  IT_COUNTRY_TYPE: 'sms_gm_country_type',
  IT_PORT_TYPE: 'sms_gm_port_type',
  IT_SIZE_INFO: 'sms_gm_size_info',
  IT_CUT_EDGE: 'sms_gm_cutedge',
  SMS_GM_TARGET_MARKET: 'sms_gm_target_market',
  SMS_GM_PRICE_STATE: 'SMS_GM_PRICE_STATE',
  SMS_GM_CARRIER: 'sms_gm_carrier',
  SMS_GM_SHIP_CLAUSE: 'sms_gm_ship_clause',
  SMS_GM_SHIP_STATE: 'ship_resource_state',
  SMS_GM_TRANS_WAY: 'trans_way',


  // ========== MALL - BASIC 模块 ==========
  BASIC_AUDIT_STATUS: 'BASIC_AUDIT_STATUS',
  INVEN_TORY_TYPE: 'inven_tory_type', // 品别
  INVEN_TORY_TYPE2: 'inven_tory_type2', // 品别
  ABC: 'abc', // 品别
  STOCK_UNIT: 'stock_unit', // 库存单位
  PROCUREMENT_UNIT: 'procurement_unit',// 采购单位
  LENGTH_UNIT: 'length_unit', // 长度单位
  MATRL_INDEX_NUM: 'MATRL_INDEX_NUM', // 长度单位
  MATRL_TYPE: 'MATRL_TYPE', // 长度单位
  APPLY_TYPE: 'APPLY_TYPE', // 长度单位
  MATRL_DEGREE: 'MATRL_DEGREE', // 长度单位
  IS_MADEINCHINA: 'IS_MADEINCHINA', // 长度单位
  MATRL_STATUS: 'MATRL_STATUS', // 长度单位
  PRICE_TYPE: 'PRICE_TYPE', // 长度单位
  SP_MANAGE_TYPE: 'SP_MANAGE_TYPE', // 长度单位
  PUR_PROCESS: 'PUR_PROCESS', // 长度单位
  SPEC_CTRL_TYPE: 'SPEC_CTRL_TYPE', // 长度单位
  ACCEPTANCE_FLAG: 'ACCEPTANCE_FLAG', // 长度单位
  TIME_DIC: 'TIME_DIC', // 长度单位
  ITEM_LEVEL: 'ITEM_LEVEL', // 长度单位
  BASIC_MATERIAL_AUDIT: 'BASIC_MATERIAL_AUDIT', // 长度单位
  BASIC_EDIT_TYPE: 'BASIC_EDIT_TYPE', // 长度单位
  PMS_MATRLNO_WATER: 'pms_matrlno_water', // 长度单位
  GRADE: 'GRADE', // 长度单位
  MRKEYACCT: 'MRKEYACCT', // 长度单位
  SELF_PRODUCE: 'SELF_PRODUCE', // 长度单位
  MRCHKITEM: 'MRChkItem', // 长度单位
  STOCK_WAY: 'STOCK_WAY', // 长度单位
  IF_QUALITY_CHECK: 'IF_QUALITY_CHECK', // 长度单位
  TP_SPECNO: 'TP_SPECNO', // 长度单位
  IP_SYSTEM_SECOND_CODE: 'IP_SYSTEM_SECOND_CODE', // 长度单位

  // ========== MALL - SRM 模块 ==========
  SRM_UNIT_NATURE: 'srm_unit_nature',
  SRM_IF_CUSTOMER: 'srm_if_customer',
  SRM_IF_SUPPLIER: 'srm_if_supplier',
  SRM_DOCUMENT_TYPE: 'srm_document_type',
  SRM_COUNTRY: 'srm_country',
  SRM_PROVINCE: 'srm_province',
  SRM_CITY: 'srm_city',
  SRM_BANK_STSTUS: 'srm_bank_status',

  // ========== MALL - CUST 模块 ==========
  CUSTOM_LEVEL: 'custom_level',
  CUST_STATE: 'cust_state',
  ENQUIRY_STATE: 'enquiry_state',
  PERMISION: 'permision',
  CLUE_STATE: 'clue_state',
  BUSINESS_STATE: 'business_state',
  CUST_KIND: 'cust_kind',
  ADVICE_STATE: 'advice_state',
  PMS_IMPORT_INDICATORS: 'pms_import_indicators',
  PMS_INDEX_TYPE: 'pms_index_type',
  PMS_IMPORT_DISCOUNT: 'pms_import_discount',
}

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas(dictType) {
  return store.getters.dict_datas[dictType] || []
}

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @param values 数组、单个元素
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas2(dictType, values) {
  if (values === undefined) {
    return [];
  }
  // 如果是单个元素，则转换成数组
  if (!Array.isArray(values)) {
    values = [this.value];
  }
  // 获得字典数据
  const results = [];
  for (const value of values) {
    const dict = getDictData(dictType, value);
    if (dict) {
      results.push(dict);
    }
  }
  return results;
}

export function getDictData(dictType, value) {
  // 获取 dictType 对应的数据字典数组
  const dictDatas = getDictDatas(dictType)
  if (!dictDatas || dictDatas.length === 0) {
    return ''
  }
  // 获取 value 对应的展示名
  value = value + '' // 强制转换成字符串，因为 DictData 小类数值，是字符串
  for (const dictData of dictDatas) {
    if (dictData.value === value) {
      return dictData;
    }
  }
  return undefined
}

export function getDictDataLabel(dictType, value) {
  const dict = getDictData(dictType, value);
  return dict ? dict.label : '';
}
