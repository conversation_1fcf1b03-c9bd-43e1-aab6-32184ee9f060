<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="800px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="料号" prop="materialNo">
            <el-input v-model="form.materialNo" placeholder="请输入料号" readonly>
              <el-button slot="append" icon="el-icon-search" @click="openMaterialSelect">选择物料</el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品别代码" prop="categoryCode">
            <el-input v-model="form.categoryCode" placeholder="请输入品别代码" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="中文品名" prop="materialNameCn">
            <el-input v-model="form.materialNameCn" placeholder="请输入中文品名" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="型号规格" prop="modelSpec">
            <el-input v-model="form.modelSpec" placeholder="请输入型号规格" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="物料描述" prop="materialDesc">
            <el-input v-model="form.materialDesc" type="textarea" placeholder="请输入物料描述" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="材质" prop="materialType">
            <el-input v-model="form.materialType" placeholder="请输入材质" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图号" prop="drawingNo">
            <el-input v-model="form.drawingNo" placeholder="请输入图号" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="图档" prop="drawingFile">
            <el-input v-model="form.drawingFile" placeholder="请输入图档" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存单位" prop="stockUnit">
            <el-input v-model="form.stockUnit" placeholder="请输入库存单位" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="状况码" prop="statusCode">
            <el-select v-model="form.statusCode" placeholder="请选择状况码">
              <el-option label="正常" value="NORMAL" />
              <el-option label="停用" value="DISABLED" />
              <el-option label="淘汰" value="OBSOLETE" />
              <el-option label="试用" value="TRIAL" />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number v-model="form.sortOrder" :min="0" placeholder="请输入排序" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>

    <!-- 物料选择弹窗 -->
    <material-search ref="materialSearch" @success="handleMaterialSelect" />
  </div>
</template>

<script>
import { getProcurementCollectionItem, createProcurementCollectionItem, updateProcurementCollectionItem } from "@/api/pms/procurement-collection";
import MaterialSearch from "@/views/pms/pomain/components/MaterialSearch";

export default {
  name: "ProcurementCollectionItemForm",
  components: {
    MaterialSearch
  },
  props: {
    collectionId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialNo: [
          { required: true, message: "料号不能为空", trigger: "blur" }
        ],
        categoryCode: [
          { required: true, message: "品别代码不能为空", trigger: "blur" }
        ],
        materialNameCn: [
          { required: true, message: "中文品名不能为空", trigger: "blur" }
        ],
        stockUnit: [
          { required: true, message: "库存单位不能为空", trigger: "blur" }
        ],
        statusCode: [
          { required: true, message: "状况码不能为空", trigger: "change" }
        ]
      }
    };
  },
  methods: {
    // 打开弹窗
    open(id) {
      this.reset();
      if (id != null) {
        this.title = "修改物料明细";
        getProcurementCollectionItem(id).then(response => {
          this.form = response.data;
          this.dialogVisible = true;
        });
      } else {
        this.title = "新增物料明细";
        this.form.collectionId = this.collectionId;
        this.dialogVisible = true;
      }
    },
    // 取消按钮
    cancel() {
      this.dialogVisible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        collectionId: this.collectionId,
        materialNo: null,
        categoryCode: null,
        materialNameCn: null,
        modelSpec: null,
        materialDesc: null,
        materialType: null,
        drawingNo: null,
        drawingFile: null,
        stockUnit: null,
        statusCode: 'NORMAL',
        sortOrder: 0
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateProcurementCollectionItem(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          } else {
            createProcurementCollectionItem(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          }
        }
      });
    },
    /** 打开物料选择弹窗 */
    openMaterialSelect() {
      this.$refs.materialSearch.open('M'); // 'M' 表示物料类型
    },
    /** 处理物料选择结果 */
    handleMaterialSelect(material) {
      if (material) {
        // 将选中的物料信息填充到表单中
        this.form.materialNo = material.matrlno;
        this.form.categoryCode = material.bigTypeCode + material.midTypeCode + material.leafTypeCode;
        this.form.materialNameCn = material.cnmdesc;
        this.form.modelSpec = material.nmspec;
        this.form.materialDesc = material.cnmdesc;
        this.form.materialType = material.quality;
        this.form.drawingNo = material.picno;
        this.form.drawingFile = material.picno;
        this.form.stockUnit = material.unitinv;

        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.form.validateField(['materialNo', 'categoryCode', 'materialNameCn', 'stockUnit']);
        });
      }
    }
  }
};
</script>
