<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="600px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="集采编号" prop="collectionNo">
            <el-input v-model="form.collectionNo" placeholder="请输入集采编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司别" prop="companyCode">
            <el-input v-model="form.companyCode" placeholder="请输入公司别" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="版本号" prop="versionNo">
            <el-input v-model="form.versionNo" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建立人职工编号" prop="creatorEmpNo">
            <el-input v-model="form.creatorEmpNo" placeholder="请输入建立人职工编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker clearable v-model="form.effectiveDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择生效日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效状态" prop="effectiveStatus">
            <el-select v-model="form.effectiveStatus" placeholder="请选择生效状态">
              <el-option label="未生效" :value="0" />
              <el-option label="已生效" :value="1" />
              <el-option label="已失效" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="修改人职工编号" prop="updaterEmpNo">
            <el-input v-model="form.updaterEmpNo" placeholder="请输入修改人职工编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getProcurementCollection, createProcurementCollection, updateProcurementCollection } from "@/api/pms/procurement-collection";

export default {
  name: "ProcurementCollectionForm",
  data() {
    return {
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        collectionNo: [
          { required: true, message: "集采编号不能为空", trigger: "blur" }
        ],
        companyCode: [
          { required: true, message: "公司别不能为空", trigger: "blur" }
        ],
        versionNo: [
          { required: true, message: "版本号不能为空", trigger: "blur" }
        ],
        creatorEmpNo: [
          { required: true, message: "建立人职工编号不能为空", trigger: "blur" }
        ],
        effectiveStatus: [
          { required: true, message: "生效状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  methods: {
    // 打开弹窗
    open(id) {
      this.reset();
      if (id != null) {
        this.title = "修改集采信息主表";
        getProcurementCollection(id).then(response => {
          this.form = response.data;
          this.dialogVisible = true;
        });
      } else {
        this.title = "新增集采信息主表";
        this.dialogVisible = true;
      }
    },
    // 取消按钮
    cancel() {
      this.dialogVisible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        collectionNo: null,
        companyCode: null,
        versionNo: "1.0",
        creatorEmpNo: null,
        createDate: null,
        updaterEmpNo: null,
        updateDate: null,
        effectiveDate: null,
        effectiveStatus: 0,
        remark: null
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateProcurementCollection(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          } else {
            createProcurementCollection(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          }
        }
      });
    }
  }
};
</script>
