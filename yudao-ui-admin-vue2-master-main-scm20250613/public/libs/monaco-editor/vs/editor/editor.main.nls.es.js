/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.23.0(82e8ea39fc101d639262435542c7d43bc20d8aa2)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls.es",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["entrada"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Coincid<PERSON> may\xFAsculas y min\xFAsculas","Solo palabras completas","Usar expresi\xF3n regular"],"vs/base/browser/ui/findinput/replaceInput":["entrada","Conservar may/min"],"vs/base/browser/ui/iconLabel/iconLabel":["Cargando..."],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Advertencia: {0}","Informaci\xF3n: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Sin enlazar"],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Borrar","Desactivar filtro en tipo","Activar filtro en el tipo","No se encontraron elementos","{0} de {1} elementos coincidentes"],"vs/base/common/actions":["(vac\xEDo)"],"vs/base/common/errorMessage":["{0}: {1}","Error del sistema ({0})","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles.","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles.","{0} ({1} errores en total)","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles."],"vs/base/common/keybindingLabels":["Ctrl","May\xFAs","Alt","Windows","Ctrl","May\xFAs","Alt","Super","Control","May\xFAs","Alt","Comando","Control","May\xFAs","Alt","Windows","Control","May\xFAs","Alt","Super"],"vs/base/parts/quickinput/browser/quickInput":["Atr\xE1s","{0}/{1}","Escriba para restringir los resultados.","{0} resultados","{0} seleccionados","Aceptar","Personalizado","Atr\xE1s ({0})","Atr\xE1s"],"vs/base/parts/quickinput/browser/quickInputList":["Entrada r\xE1pida"],"vs/editor/browser/controller/coreCommands":["Anclar al final incluso cuando se vayan a l\xEDneas m\xE1s largas","Anclar al final incluso cuando se vayan a l\xEDneas m\xE1s largas"],"vs/editor/browser/controller/textAreaHandler":["editor","El editor no es accesible en este momento. Pulse {0} para ver las opciones."],"vs/editor/browser/core/keybindingCancellation":['Indica si el editor ejecuta una operaci\xF3n que se puede cancelar como, por ejemplo, "Inspeccionar referencias"'],"vs/editor/browser/editorExtensions":["&&Deshacer","Deshacer","&&Rehacer","Rehacer","&&Seleccionar todo","Seleccionar todo"],"vs/editor/browser/widget/codeEditorWidget":["El n\xFAmero de cursores se ha limitado a {0}."],"vs/editor/browser/widget/diffEditorWidget":["Decoraci\xF3n de l\xEDnea para las inserciones en el editor de diferencias.","Decoraci\xF3n de l\xEDnea para las eliminaciones en el editor de diferencias.","Los archivos no se pueden comparar porque uno de ellos es demasiado grande."],"vs/editor/browser/widget/diffReview":['Icono para "Insertar" en la revisi\xF3n de diferencias.','Icono para "Quitar" en la revisi\xF3n de diferencias.','Icono para "Cerrar" en la revisi\xF3n de diferencias.',"Cerrar","no se han cambiado l\xEDneas","1 l\xEDnea cambiada","{0} l\xEDneas cambiadas","Diferencia {0} de {1}: l\xEDnea original {2}, {3}, l\xEDnea modificada {4}, {5}","vac\xEDo","{0} l\xEDnea sin cambios {1}","{0} l\xEDnea original {1} l\xEDnea modificada {2}","+ {0} l\xEDnea modificada {1}","- {0} l\xEDnea original {1}","Ir a la siguiente diferencia","Ir a la diferencia anterior"],"vs/editor/browser/widget/inlineDiffMargin":["Copiar l\xEDneas eliminadas","Copiar l\xEDnea eliminada","Copiar la l\xEDnea eliminada ({0})","Revertir este cambio","Copiar la l\xEDnea eliminada ({0})"],"vs/editor/common/config/commonEditorConfig":["Editor",'El n\xFAmero de espacios a los que equivale una tabulaci\xF3n. Este valor se invalida en funci\xF3n del contenido del archivo cuando "#editor.detectIndentation#" est\xE1 activado.','Insertar espacios al presionar "TAB". Este valor se invalida en funci\xF3n del contenido del archivo cuando "#editor.detectIndentation#" est\xE1 activado. ','Controla si "#editor.tabSize#" y "#editor.insertSpaces#" se detectar\xE1n autom\xE1ticamente al abrir un archivo en funci\xF3n del contenido de este.',"Quitar el espacio en blanco final autoinsertado.","Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.","Habilita sugerencias basadas en palabras.","Sugerir palabras solo del documento activo.","Sugerir palabras de todos los documentos abiertos del mismo idioma.","Sugerir palabras de todos los documentos abiertos.","Controla de qu\xE9 documentos se calculan las finalizaciones basadas en palabras.","El resaltado sem\xE1ntico est\xE1 habilitado para todos los temas de color.","El resaltado sem\xE1ntico est\xE1 deshabilitado para todos los temas de color.",'El resaltado sem\xE1ntico est\xE1 configurado con el valor "semanticHighlighting" del tema de color actual.',"Controla si se muestra semanticHighlighting para los idiomas que lo admiten.",'Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar "Escape".',"Las lineas por encima de esta longitud no se tokenizar\xE1n por razones de rendimiento.","Tiempo de espera en milisegundos despu\xE9s del cual se cancela el c\xE1lculo de diferencias. Utilice 0 para no usar tiempo de espera.","Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.","Cuando est\xE1 habilitado, el editor de diferencias omite los cambios en los espacios en blanco iniciales o finales.","Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados.","Controla si el editor muestra CodeLens.","Las l\xEDneas no se ajustar\xE1n nunca.","Las l\xEDneas se ajustar\xE1n en el ancho de la ventanilla.",'Las l\xEDneas se ajustar\xE1n en funci\xF3n de la configuraci\xF3n de "#editor.wordWrap#".'],"vs/editor/common/config/editorOptions":["El editor usar\xE1 API de plataforma para detectar cu\xE1ndo est\xE1 conectado un lector de pantalla.","El editor se optimizar\xE1 de forma permanente para su uso con un lector de pantalla. El ajuste de l\xEDneas se deshabilitar\xE1.","El editor nunca se optimizar\xE1 para su uso con un lector de pantalla.","Controla si el editor se debe ejecutar en un modo optimizado para lectores de pantalla. Si se activa, se deshabilitar\xE1 el ajuste de l\xEDneas.","Controla si se inserta un car\xE1cter de espacio al comentar.","Controla si las l\xEDneas vac\xEDas deben ignorarse con la opci\xF3n de alternar, agregar o quitar acciones para los comentarios de l\xEDnea.","Controla si al copiar sin selecci\xF3n se copia la l\xEDnea actual.","Controla si el cursor debe saltar para buscar coincidencias mientras se escribe.","Controla si la cadena de b\xFAsqueda del widget de b\xFAsqueda se inicializa desde la selecci\xF3n del editor.","No activar nunca Buscar en la selecci\xF3n autom\xE1ticamente (predeterminado)","Activar siempre autom\xE1ticamente Buscar en la selecci\xF3n","Active Buscar en la selecci\xF3n autom\xE1ticamente cuando se seleccionen varias l\xEDneas de contenido.","Controla la condici\xF3n para activar la b\xFAsqueda en la selecci\xF3n de forma autom\xE1tica.","Controla si el widget de b\xFAsqueda debe leer o modificar el Portapapeles de b\xFAsqueda compartido en macOS.","Controla si Encontrar widget debe agregar m\xE1s l\xEDneas en la parte superior del editor. Si es true, puede desplazarse m\xE1s all\xE1 de la primera l\xEDnea cuando Encontrar widget est\xE1 visible.","Controla si la b\xFAsqueda se reinicia autom\xE1ticamente desde el principio (o el final) cuando no se encuentran m\xE1s coincidencias.",'Habilita o deshabilita las ligaduras tipogr\xE1ficas (caracter\xEDsticas de fuente "calt" y "liga"). C\xE1mbielo a una cadena para el control espec\xEDfico de la propiedad de CSS "font-feature-settings".','Propiedad de CSS "font-feature-settings" expl\xEDcita. En su lugar, puede pasarse un valor booleano si solo es necesario activar o desactivar las ligaduras.','Configura las ligaduras tipogr\xE1ficas o las caracter\xEDsticas de fuente. Puede ser un valor booleano para habilitar o deshabilitar las ligaduras o bien una cadena para el valor de la propiedad "font-feature-settings" de CSS.',"Controla el tama\xF1o de fuente en p\xEDxeles.",'Solo se permiten las palabras clave "normal" y "negrita" o los n\xFAmeros entre 1 y 1000.','Controla el grosor de la fuente. Acepta las palabras clave "normal" y "negrita" o los n\xFAmeros entre 1 y 1000.',"Mostrar vista de inspecci\xF3n de los resultados (predeterminado)","Ir al resultado principal y mostrar una vista de inspecci\xF3n","Vaya al resultado principal y habilite la navegaci\xF3n sin peek para otros",'Esta configuraci\xF3n est\xE1 en desuso. Use configuraciones separadas como "editor.editor.gotoLocation.multipleDefinitions" o "editor.editor.gotoLocation.multipleImplementations" en su lugar.','Controla el comportamiento del comando "Ir a definici\xF3n" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a definici\xF3n de tipo" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a declaraci\xF3n" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a implementaciones" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a referencias" cuando existen varias ubicaciones de destino.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a definici\xF3n" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a definici\xF3n de tipo" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a declaraci\xF3n" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a implementaci\xF3n" es la ubicaci\xF3n actual.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a referencia" es la ubicaci\xF3n actual.',"Controla si se muestra la informaci\xF3n al mantener el puntero sobre un elemento.","Controla el retardo en milisegundos despu\xE9s del cual se muestra la informaci\xF3n al mantener el puntero sobre un elemento.","Controla si la informaci\xF3n que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.","Habilita la bombilla de acci\xF3n de c\xF3digo en el editor.","Habilita las sugerencias insertadas en el editor.",'Controla el tama\xF1o de fuente de las sugerencias insertadas en el editor. Cuando se establece en "0", se usa el 90\xA0% de "#editor.fontSize#".',"Controla la familia de fuentes de las sugerencias insertadas en el editor.","Controla la altura de l\xEDnea. Usa 0 para utilizar la altura del tama\xF1o de fuente.","Controla si se muestra el minimapa.","El minimapa tiene el mismo tama\xF1o que el contenido del editor (y podr\xEDa desplazarse).","El minimapa se estirar\xE1 o reducir\xE1 seg\xFAn sea necesario para ocupar la altura del editor (sin desplazamiento).","El minimapa se reducir\xE1 seg\xFAn sea necesario para no ser nunca m\xE1s grande que el editor (sin desplazamiento).","Controla el tama\xF1o del minimapa.","Controla en qu\xE9 lado se muestra el minimapa.","Controla cu\xE1ndo se muestra el control deslizante del minimapa.","Escala del contenido dibujado en el minimapa: 1, 2 o 3.","Represente los caracteres reales en una l\xEDnea, por oposici\xF3n a los bloques de color.","Limite el ancho del minimapa para representar como mucho un n\xFAmero de columnas determinado.","Controla la cantidad de espacio entre el borde superior del editor y la primera l\xEDnea.","Controla el espacio entre el borde inferior del editor y la \xFAltima l\xEDnea.","Habilita un elemento emergente que muestra documentaci\xF3n de los par\xE1metros e informaci\xF3n de los tipos mientras escribe.","Controla si el men\xFA de sugerencias de par\xE1metros se cicla o se cierra al llegar al final de la lista.","Habilita sugerencias r\xE1pidas en las cadenas.","Habilita sugerencias r\xE1pidas en los comentarios.","Habilita sugerencias r\xE1pidas fuera de las cadenas y los comentarios.","Controla si deben mostrarse sugerencias autom\xE1ticamente mientras se escribe.","Los n\xFAmeros de l\xEDnea no se muestran.","Los n\xFAmeros de l\xEDnea se muestran como un n\xFAmero absoluto.","Los n\xFAmeros de l\xEDnea se muestran como distancia en l\xEDneas a la posici\xF3n del cursor.","Los n\xFAmeros de l\xEDnea se muestran cada 10 l\xEDneas.","Controla la visualizaci\xF3n de los n\xFAmeros de l\xEDnea.","N\xFAmero de caracteres monoespaciales en los que se representar\xE1 esta regla del editor.","Color de esta regla del editor.","Muestra reglas verticales despu\xE9s de un cierto n\xFAmero de caracteres monoespaciados. Usa m\xFAltiples valores para mostrar m\xFAltiples reglas. Si la matriz est\xE1 vac\xEDa, no se muestran reglas.","Inserte la sugerencia sin sobrescribir el texto a la derecha del cursor.","Inserte la sugerencia y sobrescriba el texto a la derecha del cursor.","Controla si las palabras se sobrescriben al aceptar la finalizaci\xF3n. Tenga en cuenta que esto depende de las extensiones que participan en esta caracter\xEDstica.","Controla si el filtrado y la ordenaci\xF3n de sugerencias se tienen en cuenta para los errores ortogr\xE1ficos peque\xF1os.","Controla si la ordenaci\xF3n de palabras mejora lo que aparece cerca del cursor.",'Controla si las selecciones de sugerencias recordadas se comparten entre m\xFAltiples \xE1reas de trabajo y ventanas (necesita "#editor.suggestSelection#").',"Controla si un fragmento de c\xF3digo activo impide sugerencias r\xE1pidas.","Controla si mostrar u ocultar iconos en sugerencias.","Controla la visibilidad de la barra de estado en la parte inferior del widget de sugerencias.","Controla si los detalles de sugerencia se muestran incorporados con la etiqueta o solo en el widget de detalles.","La configuraci\xF3n est\xE1 en desuso. Ahora puede cambiarse el tama\xF1o del widget de sugerencias.",'Esta configuraci\xF3n est\xE1 en desuso. Use configuraciones separadas como "editor.suggest.showKeyword" o "editor.suggest.showSnippets" en su lugar.','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "method".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "funci\xF3n".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "constructor".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "field".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "variable".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "class".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "struct".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "interface".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "module".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "property".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "event".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "operator".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "unit".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "value".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "constant".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "enum".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "enumMember".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "keyword".','Si est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "text".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "color".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "file".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "reference".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "customcolor".','Si est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "folder".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "typeParameter".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "snippet".',"Cuando est\xE1 habilitado, IntelliSense muestra sugerencias del usuario.","Cuando est\xE1 habilitado IntelliSense muestra sugerencias para problemas.","Indica si los espacios en blanco iniciales y finales deben seleccionarse siempre.",'Controla si se deben aceptar sugerencias en los caracteres de confirmaci\xF3n. Por ejemplo, en Javascript, el punto y coma (";") puede ser un car\xE1cter de confirmaci\xF3n que acepta una sugerencia y escribe ese car\xE1cter.','Aceptar solo una sugerencia con "Entrar" cuando realiza un cambio textual.','Controla si las sugerencias deben aceptarse con "Entrar", adem\xE1s de "TAB". Ayuda a evitar la ambig\xFCedad entre insertar nuevas l\xEDneas o aceptar sugerencias.',"Controla el n\xFAmero de l\xEDneas en el editor que puede leer un lector de pantalla. Advertencia: Esto puede afectar al rendimiento de n\xFAmeros superiores al predeterminado.","Contenido del editor","Utilizar las configuraciones del lenguaje para determinar cu\xE1ndo cerrar los corchetes autom\xE1ticamente.","Cerrar autom\xE1ticamente los corchetes cuando el cursor est\xE9 a la izquierda de un espacio en blanco.","Controla si el editor debe cerrar autom\xE1ticamente los corchetes despu\xE9s de que el usuario agregue un corchete de apertura.","Escriba en las comillas o los corchetes solo si se insertaron autom\xE1ticamente.","Controla si el editor debe escribir entre comillas o corchetes.","Utilizar las configuraciones del lenguaje para determinar cu\xE1ndo cerrar las comillas autom\xE1ticamente. ","Cerrar autom\xE1ticamente las comillas cuando el cursor est\xE9 a la izquierda de un espacio en blanco. ","Controla si el editor debe cerrar autom\xE1ticamente las comillas despu\xE9s de que el usuario agrega uma comilla de apertura.","El editor no insertar\xE1 la sangr\xEDa autom\xE1ticamente.","El editor mantendr\xE1 la sangr\xEDa de la l\xEDnea actual.","El editor respetar\xE1 la sangr\xEDa de la l\xEDnea actual y los corchetes definidos por el idioma.","El editor mantendr\xE1 la sangr\xEDa de la l\xEDnea actual, respetar\xE1 los corchetes definidos por el idioma e invocar\xE1 onEnterRules especiales definidos por idiomas.","El editor respetar\xE1 la sangr\xEDa de la l\xEDnea actual, los corchetes definidos por idiomas y las reglas indentationRules definidas por idiomas, adem\xE1s de invocar reglas onEnterRules especiales.","Controla si el editor debe ajustar autom\xE1ticamente la sangr\xEDa mientras los usuarios escriben, pegan, mueven o sangran l\xEDneas.","Use las configuraciones de idioma para determinar cu\xE1ndo delimitar las selecciones autom\xE1ticamente.","Envolver con comillas, pero no con corchetes.","Envolver con corchetes, pero no con comillas.","Controla si el editor debe rodear autom\xE1ticamente las selecciones al escribir comillas o corchetes.","Emule el comportamiento de selecci\xF3n de los caracteres de tabulaci\xF3n al usar espacios para la sangr\xEDa. La selecci\xF3n se aplicar\xE1 a las tabulaciones.","Controla si el editor muestra CodeLens.","Controla la familia de fuentes para CodeLens.",'Controla el tama\xF1o de fuente de CodeLens en p\xEDxeles. Cuando se establece en "0", se usa el 90\xA0% de "#editor.fontSize#".',"Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en l\xEDnea.","Habilite que la selecci\xF3n con el mouse y las teclas est\xE9 realizando la selecci\xF3n de columnas.","Controla si el resaltado de sintaxis debe ser copiado al portapapeles.","Controla el estilo de animaci\xF3n del cursor.","Controla si la animaci\xF3n suave del cursor debe estar habilitada.","Controla el estilo del cursor.",'Controla el n\xFAmero m\xEDnimo de l\xEDneas iniciales y finales visibles que rodean al cursor. En algunos otros editores, se conoce como "scrollOff" o "scrollOffset".','Solo se aplica "cursorSurroundingLines" cuando se desencadena mediante el teclado o la API.','"cursorSurroundingLines" se aplica siempre.','Controla cuando se debe aplicar "cursorSurroundingLines".','Controla el ancho del cursor cuando "#editor.cursorStyle#" se establece en "line".',"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.",'Multiplicador de la velocidad de desplazamiento al presionar "Alt".',"Controla si el editor tiene el plegado de c\xF3digo habilitado.","Utilice una estrategia de plegado espec\xEDfica del idioma, si est\xE1 disponible, de lo contrario la basada en sangr\xEDa.","Utilice la estrategia de plegado basada en sangr\xEDa.","Controla la estrategia para calcular rangos de plegado.","Controla si el editor debe destacar los rangos plegados.","Controla si al hacer clic en el contenido vac\xEDo despu\xE9s de una l\xEDnea plegada se desplegar\xE1 la l\xEDnea.","Controla la familia de fuentes.","Controla si el editor debe dar formato autom\xE1ticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. ","Controla si el editor debe dar formato a la l\xEDnea autom\xE1ticamente despu\xE9s de escribirla.","Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuraci\xF3n.","Controla si el cursor debe ocultarse en la regla de informaci\xF3n general.","Controla si el editor debe resaltar la gu\xEDa de sangr\xEDa activa.","Controla el espacio entre letras en p\xEDxeles.","Controla si el editor tiene habilitada la edici\xF3n vinculada. Dependiendo del lenguaje, los s\xEDmbolos relacionados (por ejemplo, las etiquetas HTML) se actualizan durante la edici\xF3n.","Controla si el editor debe detectar v\xEDnculos y hacerlos interactivos.","Resaltar par\xE9ntesis coincidentes.",'Se usar\xE1 un multiplicador en los eventos de desplazamiento de la rueda del mouse "deltaX" y "deltaY". ','Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona "Ctrl".',"Combinar varios cursores cuando se solapan.",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opci\xF3n" en macOS.',"El modificador que se usar\xE1 para agregar varios cursores con el mouse. Los gestos del mouse Ir a definici\xF3n y Abrir v\xEDnculo se adaptar\xE1n de modo que no entren en conflicto con el modificador multicursor. [M\xE1s informaci\xF3n](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Cada cursor pega una \xFAnica l\xEDnea del texto.","Cada cursor pega el texto completo.","Controla el pegado cuando el recuento de l\xEDneas del texto pegado coincide con el recuento de cursores.","Controla si el editor debe resaltar las apariciones de s\xEDmbolos sem\xE1nticos.","Controla si debe dibujarse un borde alrededor de la regla de informaci\xF3n general.","Enfocar el \xE1rbol al abrir la inspecci\xF3n","Enfocar el editor al abrir la inspecci\xF3n","Controla si se debe enfocar el editor en l\xEDnea o el \xE1rbol en el widget de vista.","Controla si el gesto del mouse Ir a definici\xF3n siempre abre el widget interactivo.","Controla el retraso, en milisegundos, tras el cual aparecer\xE1n sugerencias r\xE1pidas.","Controla si el editor cambia el nombre autom\xE1ticamente en el tipo.",'En desuso. Utilice "editor.linkedEditing" en su lugar.',"Controla si el editor debe representar caracteres de control.","Controla si el editor debe representar gu\xEDas de sangr\xEDa.","Representar el n\xFAmero de la \xFAltima l\xEDnea cuando el archivo termina con un salto de l\xEDnea.","Resalta el medianil y la l\xEDnea actual.","Controla c\xF3mo debe representar el editor el resaltado de l\xEDnea actual.","Controla si el editor debe representar el resaltado de la l\xEDnea actual solo cuando el editor est\xE1 enfocado","Representa caracteres de espacio en blanco, excepto los espacios individuales entre palabras.","Represente los caracteres de espacio en blanco solo en el texto seleccionado.","Representar solo los caracteres de espacio en blanco al final","Controla la forma en que el editor debe representar los caracteres de espacio en blanco.","Controla si las selecciones deber\xEDan tener las esquinas redondeadas.","Controla el n\xFAmero de caracteres adicionales a partir del cual el editor se desplazar\xE1 horizontalmente.","Controla si el editor seguir\xE1 haciendo scroll despu\xE9s de la \xFAltima l\xEDnea.","Despl\xE1cese solo a lo largo del eje predominante cuando se desplace vertical y horizontalmente al mismo tiempo. Evita la deriva horizontal cuando se desplaza verticalmente en un trackpad.","Controla si el portapapeles principal de Linux debe admitirse.","Controla si el editor debe destacar las coincidencias similares a la selecci\xF3n.","Mostrar siempre los controles de plegado.","Mostrar solo los controles de plegado cuando el mouse est\xE1 sobre el medianil.","Controla cu\xE1ndo se muestran los controles de plegado en el medianil.","Controla el fundido de salida del c\xF3digo no usado.","Controla las variables en desuso tachadas.","Mostrar sugerencias de fragmentos de c\xF3digo por encima de otras sugerencias.","Mostrar sugerencias de fragmentos de c\xF3digo por debajo de otras sugerencias.","Mostrar sugerencias de fragmentos de c\xF3digo con otras sugerencias.","No mostrar sugerencias de fragmentos de c\xF3digo.","Controla si se muestran los fragmentos de c\xF3digo con otras sugerencias y c\xF3mo se ordenan.","Controla si el editor se desplazar\xE1 con una animaci\xF3n.","Tama\xF1o de la fuente para el widget de sugerencias. Cuando se establece a `0`, se utilizar\xE1 el valor `#editor.fontSize#`.",'Altura de la l\xEDnea del widget de sugerencias. Cuando se establece en "0", se usa el valor "#editor.lineHeight#". El valor m\xEDnimo es 8.',"Controla si deben aparecer sugerencias de forma autom\xE1tica al escribir caracteres desencadenadores.","Seleccionar siempre la primera sugerencia.",'Seleccione sugerencias recientes a menos que al escribir m\xE1s se seleccione una, por ejemplo, "console.| -> console.log" porque "log" se ha completado recientemente.','Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, "co -> console" y "con -> const".',"Controla c\xF3mo se preseleccionan las sugerencias cuando se muestra la lista,","La pesta\xF1a se completar\xE1 insertando la mejor sugerencia de coincidencia encontrada al presionar la pesta\xF1a","Deshabilitar los complementos para pesta\xF1as.","La pesta\xF1a se completa con fragmentos de c\xF3digo cuando su prefijo coincide. Funciona mejor cuando las 'quickSuggestions' no est\xE1n habilitadas.","Habilita completar pesta\xF1as.","Los terminadores de l\xEDnea no habituales se quitan autom\xE1ticamente.","Los terminadores de l\xEDnea no habituales se omiten.","Advertencia de terminadores de l\xEDnea inusuales que se quitar\xE1n.","Quite los terminadores de l\xEDnea inusuales que podr\xEDan provocar problemas.","La inserci\xF3n y eliminaci\xF3n del espacio en blanco sigue a las tabulaciones.","Caracteres que se usar\xE1n como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.","Las l\xEDneas no se ajustar\xE1n nunca.","Las l\xEDneas se ajustar\xE1n en el ancho de la ventanilla.",'Las l\xEDneas se ajustar\xE1n al valor de "#editor.wordWrapColumn#". ','Las l\xEDneas se ajustar\xE1n al valor que sea inferior: el tama\xF1o de la ventanilla o el valor de "#editor.wordWrapColumn#".',"Controla c\xF3mo deben ajustarse las l\xEDneas.",'Controla la columna de ajuste del editor cuando "#editor.wordWrap#" es "wordWrapColumn" o "bounded".',"No hay sangr\xEDa. Las l\xEDneas ajustadas comienzan en la columna 1.","A las l\xEDneas ajustadas se les aplica la misma sangr\xEDa que al elemento primario.","A las l\xEDneas ajustadas se les aplica una sangr\xEDa de +1 respecto al elemento primario.","A las l\xEDneas ajustadas se les aplica una sangr\xEDa de +2 respecto al elemento primario.","Controla la sangr\xEDa de las l\xEDneas ajustadas.","Se supone que todos los caracteres son del mismo ancho. Este es un algoritmo r\xE1pido que funciona correctamente para fuentes monoespaciales y ciertos scripts (como caracteres latinos) donde los glifos tienen el mismo ancho.","Delega el c\xE1lculo de puntos de ajuste en el explorador. Es un algoritmo lento, que podr\xEDa causar bloqueos para archivos grandes, pero funciona correctamente en todos los casos.","Controla el algoritmo que calcula los puntos de ajuste."],"vs/editor/common/editorContextKeys":["Si el texto del editor tiene el foco (el cursor parpadea)","Si el editor o un widget del editor tiene el foco (por ejemplo, el foco est\xE1 en el widget de b\xFAsqueda)","Si un editor o una entrada de texto enriquecido tienen el foco (el cursor parpadea)","Si el editor es de solo lectura","Si el contexto es un editor de diferencias",'Si "editor.columnSelection" se ha habilitado',"Si el editor tiene texto seleccionado","Si el editor tiene varias selecciones",'Si "Tabulaci\xF3n" mover\xE1 el foco fuera del editor',"Si el mantenimiento del puntero del editor es visible","Si el editor forma parte de otro m\xE1s grande (por ejemplo, blocs de notas)","Identificador de idioma del editor","Si el editor tiene un proveedor de elementos de finalizaci\xF3n","Si el editor tiene un proveedor de acciones de c\xF3digo","Si el editor tiene un proveedor de CodeLens","Si el editor tiene un proveedor de definiciones","Si el editor tiene un proveedor de declaraciones","Si el editor tiene un proveedor de implementaci\xF3n","Si el editor tiene un proveedor de definiciones de tipo","Si el editor tiene un proveedor de contenido con mantenimiento del puntero","Si el editor tiene un proveedor de resaltado de documentos","Si el editor tiene un proveedor de s\xEDmbolos de documentos","Si el editor tiene un proveedor de referencia","Si el editor tiene un proveedor de cambio de nombre","Si el editor tiene un proveedor de ayuda de signatura","Si el editor tiene un proveedor de sugerencias insertadas","Si el editor tiene un proveedor de formatos de documento","Si el editor tiene un proveedor de formatos de selecci\xF3n de documentos","Si el editor tiene varios proveedores de formatos del documento","Si el editor tiene varios proveedores de formato de la selecci\xF3n de documentos"],"vs/editor/common/model/editStack":["Escribiendo"],"vs/editor/common/modes/modesRegistry":["Texto sin formato"],"vs/editor/common/standaloneStrings":["Sin selecci\xF3n","L\xEDnea {0}, columna {1} ({2} seleccionadas)","L\xEDnea {0}, columna {1}","{0} selecciones ({1} caracteres seleccionados)","{0} selecciones",'Se cambiar\xE1 ahora el valor "accessibilitySupport" a "activado".',"Se abrir\xE1 ahora la p\xE1gina de documentaci\xF3n de accesibilidad del editor.","en un panel de solo lectura de un editor de diferencias.","en un panel de un editor de diferencias.","en un editor de c\xF3digo de solo lectura"," en un editor de c\xF3digo","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Comando+E.","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Control+E.","El editor est\xE1 configurado para optimizarse para su uso con un lector de pantalla.","El editor est\xE1 configurado para que no se optimice nunca su uso con un lector de pantalla, que en este momento no es el caso.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Al presionar TAB en el editor actual, se insertar\xE1 el car\xE1cter de tabulaci\xF3n. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, se insertar\xE1 el car\xE1cter de tabulaci\xF3n. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Presione ahora Comando+H para abrir una ventana del explorador con m\xE1s informaci\xF3n relacionada con la accesibilidad del editor.","Presione ahora Control+H para abrir una ventana del explorador con m\xE1s informaci\xF3n relacionada con la accesibilidad del editor.","Para descartar esta informaci\xF3n sobre herramientas y volver al editor, presione Esc o May\xFAs+Escape.","Mostrar ayuda de accesibilidad","Desarrollador: inspeccionar tokens","Vaya a L\xEDnea/Columna...","Mostrar todos los proveedores de acceso r\xE1pido","Paleta de comandos","Mostrar y ejecutar comandos","Ir a s\xEDmbolo...","Ir a s\xEDmbolo por categor\xEDa...","Contenido del editor","Presione Alt+F1 para ver las opciones de accesibilidad.","Alternar tema de contraste alto","{0} ediciones realizadas en {1} archivos"],"vs/editor/common/view/editorColorRegistry":["Color de fondo para la l\xEDnea resaltada en la posici\xF3n del cursor.","Color de fondo del borde alrededor de la l\xEDnea en la posici\xF3n del cursor.","Color de fondo de rangos resaltados, como en abrir r\xE1pido y encontrar caracter\xEDsticas. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo del borde alrededor de los intervalos resaltados.","Color de fondo del s\xEDmbolo destacado, como Ir a definici\xF3n o Ir al siguiente/anterior s\xEDmbolo. El color no debe ser opaco para no ocultar la decoraci\xF3n subyacente.","Color de fondo del borde alrededor de los s\xEDmbolos resaltados.","Color del cursor del editor.","Color de fondo del cursor de edici\xF3n. Permite personalizar el color del caracter solapado por el bloque del cursor.","Color de los caracteres de espacio en blanco del editor.","Color de las gu\xEDas de sangr\xEDa del editor.","Color de las gu\xEDas de sangr\xEDa activas del editor.","Color de n\xFAmeros de l\xEDnea del editor.","Color del n\xFAmero de l\xEDnea activa en el editor","ID es obsoleto. Usar en lugar 'editorLineNumber.activeForeground'. ","Color del n\xFAmero de l\xEDnea activa en el editor","Color de las reglas del editor","Color principal de lentes de c\xF3digo en el editor","Color de fondo tras corchetes coincidentes","Color de bloques con corchetes coincidentes","Color del borde de la regla de visi\xF3n general.","Color de fondo de la regla de informaci\xF3n general del editor. Solo se usa cuando el minimapa est\xE1 habilitado y est\xE1 ubicado en el lado derecho del editor.","Color de fondo del margen del editor. Este espacio contiene los m\xE1rgenes de glifos y los n\xFAmeros de l\xEDnea.","Color del borde de c\xF3digo fuente innecesario (sin usar) en el editor.",`Opacidad de c\xF3digo fuente innecesario (sin usar) en el editor. Por ejemplo, "#000000c0" representar\xE1 el c\xF3digo con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema 'editorUnnecessaryCode.border' para resaltar el c\xF3digo innecesario en vez de atenuarlo.`,"Color de marcador de regla general para los destacados de rango. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de regla de informaci\xF3n general para errores. ","Color de marcador de regla de informaci\xF3n general para advertencias.","Color de marcador de regla de informaci\xF3n general para mensajes informativos. "],"vs/editor/contrib/anchorSelect/anchorSelect":["Delimitador de la selecci\xF3n","Delimitador establecido en {0}:{1}","Establecer el delimitador de la selecci\xF3n","Ir al delimitador de la selecci\xF3n","Seleccionar desde el delimitador hasta el cursor","Cancelar el delimitador de la selecci\xF3n"],"vs/editor/contrib/bracketMatching/bracketMatching":["Resumen color de marcador de regla para corchetes.","Ir al corchete","Seleccionar para corchete","Ir al &&corchete"],"vs/editor/contrib/caretOperations/caretOperations":["Mover el texto seleccionado a la izquierda","Mover el texto seleccionado a la derecha"],"vs/editor/contrib/caretOperations/transpose":["Transponer letras"],"vs/editor/contrib/clipboard/clipboard":["Cor&&tar","Cortar","Cortar","&&Copiar","Copiar","Copiar","&&Pegar","Pegar","Pegar","Copiar con resaltado de sintaxis"],"vs/editor/contrib/codeAction/codeActionCommands":["Tipo de la acci\xF3n de c\xF3digo que se va a ejecutar.","Controla cu\xE1ndo se aplican las acciones devueltas.","Aplicar siempre la primera acci\xF3n de c\xF3digo devuelto.","Aplicar la primera acci\xF3n de c\xF3digo devuelta si solo hay una.","No aplique las acciones de c\xF3digo devuelto.","Controla si solo se deben devolver las acciones de c\xF3digo preferidas.","Se ha producido un error desconocido al aplicar la acci\xF3n de c\xF3digo","Correcci\xF3n R\xE1pida","No hay acciones de c\xF3digo disponibles",'No hay acciones de c\xF3digo preferidas para "{0}" disponibles','No hay ninguna acci\xF3n de c\xF3digo para "{0}" disponible.',"No hay acciones de c\xF3digo preferidas disponibles","No hay acciones de c\xF3digo disponibles","Refactorizar...",'No hay refactorizaciones preferidas de "{0}" disponibles','No hay refactorizaciones de "{0}" disponibles',"No hay ninguna refactorizaci\xF3n favorita disponible.","No hay refactorizaciones disponibles","Acci\xF3n de Origen...",'No hay acciones de origen preferidas para "{0}" disponibles','No hay ninguna acci\xF3n de origen para "{0}" disponible.',"No hay ninguna acci\xF3n de origen favorita disponible.","No hay acciones de origen disponibles","Organizar Importaciones","No hay acciones de importaci\xF3n disponibles","Corregir todo","No est\xE1 disponible la acci\xF3n de corregir todo","Corregir autom\xE1ticamente...","No hay autocorrecciones disponibles"],"vs/editor/contrib/codeAction/lightBulbWidget":["Mostrar correcciones. Soluci\xF3n preferida disponible ({0})","Mostrar correcciones ({0})","Mostrar correcciones"],"vs/editor/contrib/codelens/codelensController":["Mostrar comandos de lente de c\xF3digo para la l\xEDnea actual"],"vs/editor/contrib/comment/comment":["Alternar comentario de l\xEDnea","&&Alternar comentario de l\xEDnea","Agregar comentario de l\xEDnea","Quitar comentario de l\xEDnea","Alternar comentario de bloque","Alternar &&bloque de comentario"],"vs/editor/contrib/contextmenu/contextmenu":["Mostrar men\xFA contextual del editor"],"vs/editor/contrib/cursorUndo/cursorUndo":["Cursor Deshacer","Cursor Rehacer"],"vs/editor/contrib/find/findController":["Buscar","&&Buscar","Buscar con selecci\xF3n","Buscar siguiente","Buscar siguiente","Buscar anterior","Buscar anterior","Buscar selecci\xF3n siguiente","Buscar selecci\xF3n anterior","Reemplazar","&&Reemplazar"],"vs/editor/contrib/find/findWidget":['Icono para "Buscar en selecci\xF3n" en el widget de b\xFAsqueda del editor.',"Icono para indicar que el widget de b\xFAsqueda del editor est\xE1 contra\xEDdo.","Icono para indicar que el widget de b\xFAsqueda del editor est\xE1 expandido.",'Icono para "Reemplazar" en el widget de b\xFAsqueda del editor.','Icono para "Reemplazar todo" en el widget de b\xFAsqueda del editor.','Icono para "Buscar anterior" en el widget de b\xFAsqueda del editor.','Icono para "Buscar siguiente" en el widget de b\xFAsqueda del editor.',"Buscar","Buscar","Coincidencia anterior","Pr\xF3xima coincidencia","Buscar en selecci\xF3n","Cerrar","Reemplazar","Reemplazar","Reemplazar","Reemplazar todo","Alternar modo de reemplazar","S\xF3lo los primeros {0} resultados son resaltados, pero todas las operaciones de b\xFAsqueda trabajan en todo el texto.","{0} de {1}","No hay resultados","Encontrados: {0}",'{0} encontrado para "{1}"','{0} encontrado para "{1}", en {2}','{0} encontrado para "{1}"',"Ctrl+Entrar ahora inserta un salto de l\xEDnea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento."],"vs/editor/contrib/folding/folding":["Desplegar","Desplegar de forma recursiva","Plegar","Alternar plegado","Plegar de forma recursiva","Cerrar todos los comentarios de bloque","Plegar todas las regiones","Desplegar Todas las Regiones","Plegar todo","Desplegar todo","Nivel de plegamiento {0}","Color de fondo detr\xE1s de los rangos plegados. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del control plegable en el medianil del editor."],"vs/editor/contrib/folding/foldingDecorations":["Icono de rangos expandidos en el margen de glifo del editor.","Icono de rangos contra\xEDdos en el margen de glifo del editor."],"vs/editor/contrib/fontZoom/fontZoom":["Acercarse a la tipograf\xEDa del editor","Alejarse de la tipograf\xEDa del editor","Restablecer alejamiento de la tipograf\xEDa del editor"],"vs/editor/contrib/format/format":["1 edici\xF3n de formato en la l\xEDnea {0}","{0} ediciones de formato en la l\xEDnea {1}","1 edici\xF3n de formato entre las l\xEDneas {0} y {1}","{0} ediciones de formato entre las l\xEDneas {1} y {2}"],"vs/editor/contrib/format/formatActions":["Dar formato al documento","Dar formato a la selecci\xF3n"],"vs/editor/contrib/gotoError/gotoError":["Ir al siguiente problema (Error, Advertencia, Informaci\xF3n)","Icono para ir al marcador siguiente.","Ir al problema anterior (Error, Advertencia, Informaci\xF3n)","Icono para ir al marcador anterior.","Ir al siguiente problema en Archivos (Error, Advertencia, Informaci\xF3n)","Siguiente &&problema","Ir al problema anterior en Archivos (Error, Advertencia, Informaci\xF3n)","Anterior &&problema"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Error","Advertencia","Informaci\xF3n","Sugerencia","{0} en {1}. ","{0} de {1} problemas","{0} de {1} problema","Color de los errores del widget de navegaci\xF3n de marcadores del editor.","Color de las advertencias del widget de navegaci\xF3n de marcadores del editor.","Color del widget informativo marcador de navegaci\xF3n en el editor.","Fondo del widget de navegaci\xF3n de marcadores del editor."],"vs/editor/contrib/gotoSymbol/goToCommands":["Ver","Definiciones",'No se encontr\xF3 ninguna definici\xF3n para "{0}"',"No se encontr\xF3 ninguna definici\xF3n","Ir a definici\xF3n","Ir a &&definici\xF3n","Abrir definici\xF3n en el lateral","Ver la definici\xF3n sin salir","Declaraciones","No se encontr\xF3 ninguna definici\xF3n para '{0}'","No se encontr\xF3 ninguna declaraci\xF3n","Ir a Definici\xF3n","Ir a &&Declaraci\xF3n","No se encontr\xF3 ninguna definici\xF3n para '{0}'","No se encontr\xF3 ninguna declaraci\xF3n","Inspeccionar Definici\xF3n","Definiciones de tipo",'No se encontr\xF3 ninguna definici\xF3n de tipo para "{0}"',"No se encontr\xF3 ninguna definici\xF3n de tipo","Ir a la definici\xF3n de tipo","Ir a la definici\xF3n de &&tipo","Inspeccionar definici\xF3n de tipo","Implementaciones",'No se encontr\xF3 ninguna implementaci\xF3n para "{0}"',"No se encontr\xF3 ninguna implementaci\xF3n","Ir a Implementaciones","Ir a &&Implementaciones","Inspeccionar implementaciones",'No se ha encontrado ninguna referencia para "{0}".',"No se encontraron referencias","Ir a Referencias","Ir a &&Referencias","Referencias","Inspeccionar Referencias","Referencias","Ir a cualquier s\xEDmbolo","Ubicaciones",'No hay resultados para "{0}"',"Referencias"],"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Haga clic para mostrar {0} definiciones."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Cargando...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["{0} referencias","{0} referencia","Referencias"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["vista previa no disponible","No hay resultados","Referencias"],"vs/editor/contrib/gotoSymbol/referencesModel":["s\xEDmbolo en {0} linea {1} en la columna {2}","s\xEDmbolo en {0} l\xEDnea {1} en la columna {2}, {3}","1 s\xEDmbolo en {0}, ruta de acceso completa {1}","{0} s\xEDmbolos en {1}, ruta de acceso completa {2}","No se encontraron resultados","Encontr\xF3 1 s\xEDmbolo en {0}","Encontr\xF3 {0} s\xEDmbolos en {1}","Encontr\xF3 {0} s\xEDmbolos en {1} archivos"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["S\xEDmbolo {0} de {1}, {2} para el siguiente","S\xEDmbolo {0} de {1}"],"vs/editor/contrib/hover/hover":["Mostrar al mantener el puntero","Mostrar vista previa de la definici\xF3n que aparece al mover el puntero"],"vs/editor/contrib/hover/markdownHoverParticipant":["Cargando..."],"vs/editor/contrib/hover/markerHoverParticipant":["Ver el problema","No hay correcciones r\xE1pidas disponibles","Buscando correcciones r\xE1pidas...","No hay correcciones r\xE1pidas disponibles","Correcci\xF3n R\xE1pida"],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Reemplazar con el valor anterior","Reemplazar con el valor siguiente"],"vs/editor/contrib/indentation/indentation":["Convertir sangr\xEDa en espacios","Convertir sangr\xEDa en tabulaciones","Tama\xF1o de tabulaci\xF3n configurado","Seleccionar tama\xF1o de tabulaci\xF3n para el archivo actual","Aplicar sangr\xEDa con tabulaciones","Aplicar sangr\xEDa con espacios","Detectar sangr\xEDa del contenido","Volver a aplicar sangr\xEDa a l\xEDneas","Volver a aplicar sangr\xEDa a l\xEDneas seleccionadas"],"vs/editor/contrib/linesOperations/linesOperations":["Copiar l\xEDnea arriba","&&Copiar l\xEDnea arriba","Copiar l\xEDnea abajo","Co&&piar l\xEDnea abajo","Selecci\xF3n duplicada","&&Duplicar selecci\xF3n","Mover l\xEDnea hacia arriba","Mo&&ver l\xEDnea arriba","Mover l\xEDnea hacia abajo","Mover &&l\xEDnea abajo","Ordenar l\xEDneas en orden ascendente","Ordenar l\xEDneas en orden descendente","Recortar espacio final","Eliminar l\xEDnea","Sangr\xEDa de l\xEDnea","Anular sangr\xEDa de l\xEDnea","Insertar l\xEDnea arriba","Insertar l\xEDnea debajo","Eliminar todo a la izquierda","Eliminar todo lo que est\xE1 a la derecha","Unir l\xEDneas","Transponer caracteres alrededor del cursor","Transformar a may\xFAsculas","Transformar a min\xFAsculas","Transformar en Title Case","Transformar en Snake Case"],"vs/editor/contrib/linkedEditing/linkedEditing":["Iniciar edici\xF3n vinculada","Color de fondo cuando el editor cambia el nombre autom\xE1ticamente al escribir."],"vs/editor/contrib/links/links":["Ejecutar comando","Seguir v\xEDnculo","cmd + clic","ctrl + clic","opci\xF3n + clic","alt + clic","Ejecutar el comando {0}","No se pudo abrir este v\xEDnculo porque no tiene un formato correcto: {0}","No se pudo abrir este v\xEDnculo porque falta el destino.","Abrir v\xEDnculo"],"vs/editor/contrib/message/messageController":["Indica si el editor muestra actualmente un mensaje insertado","No se puede editar en un editor de s\xF3lo lectura"],"vs/editor/contrib/multicursor/multicursor":["Agregar cursor arriba","&&Agregar cursor arriba","Agregar cursor debajo","A&&gregar cursor abajo","A\xF1adir cursores a finales de l\xEDnea","Agregar c&&ursores a extremos de l\xEDnea","A\xF1adir cursores a la parte inferior","A\xF1adir cursores a la parte superior","Agregar selecci\xF3n hasta la siguiente coincidencia de b\xFAsqueda","Agregar &&siguiente repetici\xF3n","Agregar selecci\xF3n hasta la anterior coincidencia de b\xFAsqueda","Agregar r&&epetici\xF3n anterior","Mover \xFAltima selecci\xF3n hasta la siguiente coincidencia de b\xFAsqueda","Mover \xFAltima selecci\xF3n hasta la anterior coincidencia de b\xFAsqueda","Seleccionar todas las repeticiones de coincidencia de b\xFAsqueda","Seleccionar todas las &&repeticiones","Cambiar todas las ocurrencias"],"vs/editor/contrib/parameterHints/parameterHints":["Sugerencias para par\xE1metros Trigger"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["Icono para mostrar la sugerencia de par\xE1metro siguiente.","Icono para mostrar la sugerencia de par\xE1metro anterior.","{0}, sugerencia"],"vs/editor/contrib/peekView/peekView":["Cerrar","Color de fondo del \xE1rea de t\xEDtulo de la vista de inspecci\xF3n.","Color del t\xEDtulo de la vista de inpecci\xF3n.","Color de la informaci\xF3n del t\xEDtulo de la vista de inspecci\xF3n.","Color de los bordes y la flecha de la vista de inspecci\xF3n.","Color de fondo de la lista de resultados de vista de inspecci\xF3n.","Color de primer plano de los nodos de inspecci\xF3n en la lista de resultados.","Color de primer plano de los archivos de inspecci\xF3n en la lista de resultados.","Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspecci\xF3n.","Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspecci\xF3n.","Color de fondo del editor de vista de inspecci\xF3n.","Color de fondo del margen en el editor de vista de inspecci\xF3n.","Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspecci\xF3n.","Buscar coincidencia del color de resultado del editor de vista de inspecci\xF3n.","Hacer coincidir el borde resaltado en el editor de vista previa."],"vs/editor/contrib/quickAccess/gotoLineQuickAccess":["Abra primero un editor de texto para ir a una l\xEDnea.","Vaya a la l\xEDnea {0} y a la columna {1}.","Ir a la l\xEDnea {0}.","L\xEDnea actual: {0}, Car\xE1cter: {1}. Escriba un n\xFAmero de l\xEDnea entre 1 y {2} a los que navegar.","L\xEDnea actual: {0}, Car\xE1cter: {1}. Escriba un n\xFAmero de l\xEDnea al que navegar."],"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess":["Para ir a un s\xEDmbolo, primero abra un editor de texto con informaci\xF3n de s\xEDmbolo.","El editor de texto activo no proporciona informaci\xF3n de s\xEDmbolos.","No hay ning\xFAn s\xEDmbolo del editor coincidente.","No hay s\xEDmbolos del editor.","Abrir en el lateral","Abrir en la parte inferior","s\xEDmbolos ({0})","propiedades ({0})","m\xE9todos ({0})","funciones ({0})","constructores ({0})","variables ({0})","clases ({0})","estructuras ({0})","eventos ({0})","operadores ({0})","interfaces ({0})","espacios de nombres ({0})","paquetes ({0})","par\xE1metros de tipo ({0})","m\xF3dulos ({0})","propiedades ({0})","enumeraciones ({0})","miembros de enumeraci\xF3n ({0})","cadenas ({0})","archivos ({0})","matrices ({0})","n\xFAmeros ({0})","booleanos ({0})","objetos ({0})","claves ({0})","campos ({0})","constantes ({0})"],"vs/editor/contrib/rename/rename":["No hay ning\xFAn resultado.","Error desconocido al resolver el cambio de nombre de la ubicaci\xF3n",'Cambiando el nombre de "{0}"',"Cambiar el nombre de {0}","Nombre cambiado correctamente de '{0}' a '{1}'. Resumen: {2}","No se pudo cambiar el nombre a las ediciones de aplicaci\xF3n","No se pudo cambiar el nombre de las ediciones de c\xE1lculo","Cambiar el nombre del s\xEDmbolo","Activar/desactivar la capacidad de previsualizar los cambios antes de cambiar el nombre"],"vs/editor/contrib/rename/renameInputField":["Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar.","{0} para cambiar de nombre, {1} para obtener una vista previa"],"vs/editor/contrib/smartSelect/smartSelect":["Expandir selecci\xF3n","&&Expandir selecci\xF3n","Reducir la selecci\xF3n","&&Reducir selecci\xF3n"],"vs/editor/contrib/snippet/snippetVariables":["Domingo","Lunes","Martes","Mi\xE9rcoles","Jueves","Viernes","S\xE1bado","Dom","Lun","Mar","Mi\xE9","Jue","Vie","S\xE1b","Enero","Febrero","Marzo","Abril","May","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre","Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"],"vs/editor/contrib/suggest/suggestController":['Aceptando "{0}" ediciones adicionales de {1} realizadas',"Sugerencias para Trigger","Insertar","Insertar","Reemplazar","Reemplazar","Insertar","mostrar menos","mostrar m\xE1s","Restablecer tama\xF1o del widget de sugerencias"],"vs/editor/contrib/suggest/suggestWidget":["Color de fondo del widget sugerido.","Color de borde del widget sugerido.","Color de primer plano del widget sugerido.","Color de fondo de la entrada seleccionada del widget sugerido.","Color del resaltado coincidido en el widget sugerido.","Cargando...","No hay sugerencias.","{0}, documentos: {1}","Sugerir"],"vs/editor/contrib/suggest/suggestWidgetDetails":["Cerrar","Cargando..."],"vs/editor/contrib/suggest/suggestWidgetRenderer":["Icono para obtener m\xE1s informaci\xF3n en el widget de sugerencias.","Leer m\xE1s"],"vs/editor/contrib/suggest/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/symbolIcons":["Color de primer plano de los s\xEDmbolos de matriz. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos booleanos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de clase. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de color. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos constantes. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de constructor. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de enumerador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de miembro del enumerador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de evento. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de campo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de archivo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de carpeta. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de funci\xF3n. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de interfaz. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de claves. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de palabra clave. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de m\xE9todo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de m\xF3dulo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de espacio de nombres. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos nulos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos num\xE9ricos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de objeto. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos del operador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de paquete. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de propiedad. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de referencia. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de fragmento de c\xF3digo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de cadena. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de estructura. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de texto. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos de par\xE1metro de tipo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de unidad. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos variables. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias."],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Alternar tecla de tabulaci\xF3n para mover el punto de atenci\xF3n","Presionando la pesta\xF1a ahora mover\xE1 el foco al siguiente elemento enfocable.","Presionando la pesta\xF1a ahora insertar\xE1 el car\xE1cter de tabulaci\xF3n"],"vs/editor/contrib/tokenization/tokenization":["Desarrollador: forzar nueva aplicaci\xF3n de token"],"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators":["Terminadores de l\xEDnea inusuales","Se han detectado terminadores de l\xEDnea inusuales",`Este archivo contiene uno o varios caracteres de terminador de l\xEDnea inusuales, como Separador de l\xEDneas (LS) o Separador de p\xE1rrafos (PS).\r
\r
Se recomienda quitarlos del archivo. Se puede configurar a trav\xE9s de "editor.unusualLineTerminators".`,"Corregir este archivo","Ignorar problema para este archivo"],"vs/editor/contrib/wordHighlighter/wordHighlighter":["Color de fondo de un s\xEDmbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo de un s\xEDmbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo de un s\xEDmbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.","Color de fondo de un s\xEDmbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.","Color del marcador de regla general para destacados de s\xEDmbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de marcador de regla general para destacados de s\xEDmbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Ir al siguiente s\xEDmbolo destacado","Ir al s\xEDmbolo destacado anterior","Desencadenar los s\xEDmbolos destacados"],"vs/editor/contrib/wordOperations/wordOperations":["Eliminar palabra"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["La configuraci\xF3n del lenguaje predeterminada se reemplaza","Establecer los valores de configuraci\xF3n que se reemplazar\xE1n para un lenguaje.","Esta configuraci\xF3n no admite la configuraci\xF3n por idioma.","No se puede registrar una propiedad vac\xEDa.",`No se puede registrar "{0}". Coincide con el patr\xF3n de propiedad '\\\\[.*\\\\]$' para describir la configuraci\xF3n del editor espec\xEDfica del lenguaje. Utilice la contribuci\xF3n "configurationDefaults".`,'No se puede registrar "{0}". Esta propiedad ya est\xE1 registrada.'],"vs/platform/contextkey/browser/contextKeyService":["Comando que devuelve informaci\xF3n sobre las claves de contexto"],"vs/platform/contextkey/common/contextkeys":["Si el sistema operativo es Windows"],"vs/platform/keybinding/common/abstractKeybindingService":["Se presion\xF3 ({0}). Esperando la siguiente tecla...","La combinaci\xF3n de claves ({0}, {1}) no es un comando."],"vs/platform/list/browser/listService":["\xC1rea de trabajo",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opci\xF3n" en macOS.',"El modificador que se utilizar\xE1 para agregar un elemento en los \xE1rboles y listas para una selecci\xF3n m\xFAltiple con el rat\xF3n (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de rat\xF3n 'Abrir hacia' - si est\xE1n soportados - se adaptar\xE1n de forma tal que no tenga conflicto con el modificador m\xFAltiple.","Controla c\xF3mo abrir elementos en los \xE1rboles y las listas mediante el mouse (si se admite). Tenga en cuenta que algunos \xE1rboles y listas pueden optar por ignorar esta configuraci\xF3n si no es aplicable.","Controla si las listas y los \xE1rboles admiten el desplazamiento horizontal en el \xE1rea de trabajo. Advertencia: La activaci\xF3n de esta configuraci\xF3n repercute en el rendimiento.","Controla la sangr\xEDa de \xE1rbol en p\xEDxeles.","Controla si el \xE1rbol debe representar gu\xEDas de sangr\xEDa.","Controla si las listas y los \xE1rboles tienen un desplazamiento suave.","La navegaci\xF3n simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.","Destacar la navegaci\xF3n del teclado resalta los elementos que coinciden con la entrada del teclado. M\xE1s arriba y abajo la navegaci\xF3n atravesar\xE1 solo los elementos destacados.","La navegaci\xF3n mediante el teclado de filtro filtrar\xE1 y ocultar\xE1 todos los elementos que no coincidan con la entrada del teclado.","Controla el estilo de navegaci\xF3n del teclado para listas y \xE1rboles en el \xE1rea de trabajo. Puede ser simple, resaltar y filtrar.",'Controla si la navegaci\xF3n del teclado en listas y \xE1rboles se activa autom\xE1ticamente simplemente escribiendo. Si se establece en "false", la navegaci\xF3n con el teclado solo se activa al ejecutar el comando "list.toggleKeyboardNavigation", para el cual puede asignar un m\xE9todo abreviado de teclado.',"Controla c\xF3mo se expanden las carpetas de \xE1rbol al hacer clic en sus nombres. Tenga en cuenta que algunos \xE1rboles y listas pueden optar por omitir esta configuraci\xF3n si no es aplicable."],"vs/platform/markers/common/markers":["Error","Advertencia","Informaci\xF3n"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","usado recientemente","otros comandos",'El comando "{0}" dio lugar a un error ({1})'],"vs/platform/quickinput/browser/helpQuickAccess":["comandos globales","comandos del editor","{0}, {1}"],"vs/platform/theme/common/colorRegistry":["Color de primer plano general. Este color solo se usa si un componente no lo invalida.","Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.","El color predeterminado para los iconos en el \xE1rea de trabajo.","Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.","Un borde adicional alrededor de los elementos para separarlos unos de otros y as\xED mejorar el contraste.","Un borde adicional alrededor de los elementos activos para separarlos unos de otros y as\xED mejorar el contraste.","Color de primer plano para los v\xEDnculos en el texto.","Color de fondo para los bloques de c\xF3digo en el texto.","Color de sombra de los widgets dentro del editor, como buscar/reemplazar","Fondo de cuadro de entrada.","Primer plano de cuadro de entrada.","Borde de cuadro de entrada.","Color de borde de opciones activadas en campos de entrada.","Color de fondo de las opciones activadas en los campos de entrada.","Color de primer plano de las opciones activadas en los campos de entrada.","Color de fondo de validaci\xF3n de entrada para gravedad de informaci\xF3n.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de gravedad.","Color de borde de validaci\xF3n de entrada para gravedad de informaci\xF3n.","Color de fondo de validaci\xF3n de entrada para gravedad de advertencia.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de advertencia.","Color de borde de validaci\xF3n de entrada para gravedad de advertencia.","Color de fondo de validaci\xF3n de entrada para gravedad de error.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de error.","Color de borde de valdaci\xF3n de entrada para gravedad de error.","Fondo de lista desplegable.","Primer plano de lista desplegable.","Color de primer plano del bot\xF3n.","Color de fondo del bot\xF3n.","Color de fondo del bot\xF3n al mantener el puntero.","Color de fondo de la insignia. Las insignias son peque\xF1as etiquetas de informaci\xF3n, por ejemplo los resultados de un n\xFAmero de resultados.","Color de primer plano de la insignia. Las insignias son peque\xF1as etiquetas de informaci\xF3n, por ejemplo los resultados de un n\xFAmero de resultados.","Sombra de la barra de desplazamiento indica que la vista se ha despazado.","Color de fondo de control deslizante de barra de desplazamiento.","Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.","Color de fondo de la barra de desplazamiento al hacer clic.","Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duraci\xF3n.","Color de fondo del texto de error del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de squigglies de error en el editor.","Color del borde de los cuadros de error en el editor.","Color de fondo del texto de advertencia del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de squigglies de advertencia en el editor.","Color del borde de los cuadros de advertencia en el editor.","Color de fondo del texto de informaci\xF3n del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de los subrayados ondulados informativos en el editor.","Color del borde de los cuadros de informaci\xF3n en el editor.","Color de primer plano de pista squigglies en el editor.","Color del borde de los cuadros de sugerencia en el editor.","Color de fondo del editor.","Color de primer plano predeterminado del editor.","Color de fondo del editor de widgets como buscar/reemplazar","Color de primer plano de los widgets del editor, como buscar y reemplazar.","Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.","Color del borde de la barra de cambio de tama\xF1o de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tama\xF1o y si un widget no invalida el color.","Color de fondo del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Color de primer plano del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Color de fondo del t\xEDtulo del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Color de fondo del selector r\xE1pido para el elemento con el foco.","Selector de color r\xE1pido para la agrupaci\xF3n de etiquetas.","Selector de color r\xE1pido para la agrupaci\xF3n de bordes.","Color de la selecci\xF3n del editor.","Color del texto seleccionado para alto contraste.","Color de la selecci\xF3n en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color en las regiones con el mismo contenido que la selecci\xF3n. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de las regiones con el mismo contenido que la selecci\xF3n.","Color de la coincidencia de b\xFAsqueda actual.","Color de los otros resultados de la b\xFAsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de la gama que limita la b\xFAsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de la coincidencia de b\xFAsqueda actual.","Color de borde de otra b\xFAsqueda que coincide.","Color del borde de la gama que limita la b\xFAsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo al mantener el puntero en el editor.","Color de primer plano al mantener el puntero en el editor.","Color del borde al mantener el puntero en el editor.","Color de fondo de la barra de estado al mantener el puntero en el editor.","Color de los v\xEDnculos activos.","Color de primer plano de las sugerencias insertadas","Color de fondo de las sugerencias insertadas","El color utilizado para el icono de bombilla de acciones.","El color utilizado para el icono de la bombilla de acciones de correcci\xF3n autom\xE1tica.","Color de fondo para el texto que se insert\xF3. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo para el texto que se elimin\xF3. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de contorno para el texto insertado.","Color de contorno para el texto quitado.","Color del borde entre ambos editores de texto.","Color de relleno diagonal del editor de diferencias. El relleno diagonal se usa en las vistas de diferencias en paralelo.","Color de fondo de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de contorno de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Color de fondo de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de fondo de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol esta inactiva. Una lista o un \xE1rbol tiene el foco del teclado cuando est\xE1 activo, cuando esta inactiva no.","Color de fondo de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Color de contorno de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Fondo de la lista o el \xE1rbol al mantener el mouse sobre los elementos.","Color de primer plano de la lista o el \xE1rbol al pasar por encima de los elementos con el rat\xF3n.","Fondo de arrastrar y colocar la lista o el \xE1rbol al mover los elementos con el mouse.","Color de primer plano de la lista o el \xE1rbol de las coincidencias resaltadas al buscar dentro de la lista o el \xE1bol.","Color de fondo del widget de filtro de tipo en listas y \xE1rboles.","Color de contorno del widget de filtro de tipo en listas y \xE1rboles.","Color de contorno del widget de filtro de tipo en listas y \xE1rboles, cuando no hay coincidencias.","Color de trazo de \xE1rbol para las gu\xEDas de sangr\xEDa.","Color de trazo de \xE1rbol para las gu\xEDas de sangr\xEDa.","Color del borde de los men\xFAs.","Color de primer plano de los elementos de men\xFA.","Color de fondo de los elementos de men\xFA.","Color de primer plano del menu para el elemento del men\xFA seleccionado.","Color de fondo del menu para el elemento del men\xFA seleccionado.","Color del borde del elemento seleccionado en los men\xFAs.","Color del separador del menu para un elemento del men\xFA.","Resaltado del color de fondo para una ficha de un fragmento de c\xF3digo.","Resaltado del color del borde para una ficha de un fragmento de c\xF3digo.","Resaltado del color de fondo para la \xFAltima ficha de un fragmento de c\xF3digo.","Resaltado del color del borde para la \xFAltima tabulaci\xF3n de un fragmento de c\xF3digo.","Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del marcador de la regla general para los destacados de la selecci\xF3n. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de minimapa para coincidencias de b\xFAsqueda.","Color del marcador de minimapa para la selecci\xF3n del editor.","Color del marcador de minimapa para errores.","Color del marcador de minimapa para advertencias.","Color de fondo del minimapa.","Color de fondo del deslizador del minimapa.","Color de fondo del deslizador del minimapa al pasar el puntero.","Color de fondo del deslizador de minimapa al hacer clic en \xE9l.","Color utilizado para el icono de error de problemas.","Color utilizado para el icono de advertencia de problemas.","Color utilizado para el icono de informaci\xF3n de problemas."],"vs/platform/theme/common/iconRegistry":["Identificador de la fuente que se va a usar. Si no se establece, se usa la fuente definida en primer lugar.","Car\xE1cter de fuente asociado a la definici\xF3n del icono.","Icono de la acci\xF3n de cierre en los widgets."],"vs/platform/undoRedo/common/undoRedoService":["Se han cerrado los siguientes archivos y se han modificado en el disco: {0}.","Los siguientes archivos se han modificado de forma incompatible: {0}.",'No se pudo deshacer "{0}" en todos los archivos. {1}','No se pudo deshacer "{0}" en todos los archivos. {1}','No se pudo deshacer "{0}" en todos los archivos porque se realizaron cambios en {1}','No se pudo deshacer "{0}" en todos los archivos porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n en {1}','No se pudo deshacer "{0}" en todos los archivos porque se produjo una operaci\xF3n de deshacer o rehacer mientras tanto','\xBFDesea deshacer "{0}" en todos los archivos?',"Deshacer en {0} archivos","Deshacer este archivo","Cancelar",'No se pudo deshacer "{0}" porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n.','\xBFQuiere deshacer "{0}"?',"Deshacer","Cancelar",'No se pudo rehacer "{0}" en todos los archivos. {1}','No se pudo rehacer "{0}" en todos los archivos. {1}','No se pudo volver a hacer "{0}" en todos los archivos porque se realizaron cambios en {1}','No se pudo rehacer "{0}" en todos los archivos porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n en {1}','No se pudo rehacer "{0}" en todos los archivos porque se produjo una operaci\xF3n de deshacer o rehacer mientras tanto','No se pudo rehacer "{0}" porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n.']});
