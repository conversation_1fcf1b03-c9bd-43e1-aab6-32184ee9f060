/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.23.0(82e8ea39fc101d639262435542c7d43bc20d8aa2)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls.fr",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["entr\xE9e"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Respecter la casse","Mot entier","Utiliser une expression r\xE9guli\xE8re"],"vs/base/browser/ui/findinput/replaceInput":["entr\xE9e","Pr\xE9server la casse"],"vs/base/browser/ui/iconLabel/iconLabel":["Chargement..."],"vs/base/browser/ui/inputbox/inputBox":["Erreur\xA0: {0}","Avertissement\xA0: {0}","Info\xA0: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Ind\xE9pendant"],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Effacer","D\xE9sactiver le filtre sur le type","Activer le filtre sur le type","Aucun \xE9l\xE9ment","{0}\xA0\xE9l\xE9ments sur {1} correspondants"],"vs/base/common/actions":["(vide)"],"vs/base/common/errorMessage":["{0}: {1}","Une erreur syst\xE8me s'est produite ({0})","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails.","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails.","{0} ({1}\xA0erreurs au total)","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails."],"vs/base/common/keybindingLabels":["Ctrl","Maj","Alt","Windows","Ctrl","Maj","Alt","Super","Contr\xF4le","Maj","Alt","Commande","Contr\xF4le","Maj","Alt","Windows","Contr\xF4le","Maj","Alt","Super"],"vs/base/parts/quickinput/browser/quickInput":["Pr\xE9c\xE9dent","{0}/{1}","Taper pour affiner les r\xE9sultats.","{0}\xA0r\xE9sultats","{0} S\xE9lectionn\xE9s","OK","Personnalis\xE9","Pr\xE9c\xE9dent ({0})","Pr\xE9c\xE9dent"],"vs/base/parts/quickinput/browser/quickInputList":["Entr\xE9e rapide"],"vs/editor/browser/controller/coreCommands":["Aligner par rapport \xE0 la fin m\xEAme en cas de passage \xE0 des lignes plus longues","Aligner par rapport \xE0 la fin m\xEAme en cas de passage \xE0 des lignes plus longues"],"vs/editor/browser/controller/textAreaHandler":["\xE9diteur","L'\xE9diteur n'est pas accessible pour le moment. Appuyez sur {0} pour voir les options."],"vs/editor/browser/core/keybindingCancellation":["Indique si l'\xE9diteur ex\xE9cute une op\xE9ration annulable, par exemple 'Avoir un aper\xE7u des r\xE9f\xE9rences'"],"vs/editor/browser/editorExtensions":["Ann&&uler","Annuler","&&R\xE9tablir","R\xE9tablir","&&S\xE9lectionner tout","Tout s\xE9lectionner"],"vs/editor/browser/widget/codeEditorWidget":["Le nombre de curseurs a \xE9t\xE9 limit\xE9 \xE0\xA0{0}."],"vs/editor/browser/widget/diffEditorWidget":["\xC9l\xE9ment d\xE9coratif de ligne pour les insertions dans l'\xE9diteur de diff\xE9rences.","\xC9l\xE9ment d\xE9coratif de ligne pour les suppressions dans l'\xE9diteur de diff\xE9rences.","Impossible de comparer les fichiers car l'un d'eux est trop volumineux."],"vs/editor/browser/widget/diffReview":["Ic\xF4ne de l'option Ins\xE9rer dans la revue des diff\xE9rences.","Ic\xF4ne de l'option Supprimer dans la revue des diff\xE9rences.","Ic\xF4ne de l'option Fermer dans la revue des diff\xE9rences.","Fermer","aucune ligne chang\xE9e","1\xA0ligne chang\xE9e","{0}\xA0lignes chang\xE9es","Diff\xE9rence\xA0{0} sur\xA0{1}\xA0: ligne d'origine {2}, {3}, ligne modifi\xE9e {4}, {5}","vide","{0} ligne inchang\xE9e {1}","{0}\xA0ligne d'origine {1}\xA0ligne modifi\xE9e {2}","+ {0}\xA0ligne modifi\xE9e {1}","- {0} ligne d'origine {1}","Acc\xE9der \xE0 la diff\xE9rence suivante","Acc\xE9der la diff\xE9rence pr\xE9c\xE9dente"],"vs/editor/browser/widget/inlineDiffMargin":["Copier les lignes supprim\xE9es","Copier la ligne supprim\xE9e","Copier la ligne supprim\xE9e ({0})","Annuler la modification","Copier la ligne supprim\xE9e ({0})"],"vs/editor/common/config/commonEditorConfig":["\xC9diteur","Le nombre d'espaces auxquels une tabulation est \xE9gale. Ce param\xE8tre est substitu\xE9 bas\xE9 sur le contenu du fichier lorsque `#editor.detectIndentation#` est \xE0 'on'.","Espaces ins\xE9r\xE9s quand vous appuyez sur la touche Tab. Ce param\xE8tre est remplac\xE9 en fonction du contenu du fichier quand '#editor.detectIndentation#' est activ\xE9.","Contr\xF4le si '#editor.tabSize#' et '#editor.insertSpaces#' sont automatiquement d\xE9tect\xE9s lors de l\u2019ouverture d\u2019un fichier en fonction de son contenu.","Supprimer l'espace blanc de fin ins\xE9r\xE9 automatiquement.","Traitement sp\xE9cial des fichiers volumineux pour d\xE9sactiver certaines fonctionnalit\xE9s utilisant beaucoup de m\xE9moire.","Contr\xF4le si la saisie semi-automatique doit \xEAtre calcul\xE9e en fonction des mots pr\xE9sents dans le document.","Sugg\xE8re uniquement des mots dans le document actif.","Sugg\xE8re des mots dans tous les documents ouverts du m\xEAme langage.","Sugg\xE8re des mots dans tous les documents ouverts.","Contr\xF4le la fa\xE7on dont sont calcul\xE9es les compl\xE9tions bas\xE9es sur des mots dans les documents.","Coloration s\xE9mantique activ\xE9e pour tous les th\xE8mes de couleur.","Coloration s\xE9mantique d\xE9sactiv\xE9e pour tous les th\xE8mes de couleur.","La coloration s\xE9mantique est configur\xE9e par le param\xE8tre 'semanticHighlighting' du th\xE8me de couleur actuel.","Contr\xF4le si semanticHighlighting est affich\xE9 pour les langages qui le prennent en charge.","Garder les \xE9diteurs d'aper\xE7u ouverts m\xEAme si l'utilisateur double-clique sur son contenu ou appuie sur la touche \xC9chap. ","Les lignes plus longues que cette valeur ne sont pas tokenis\xE9es pour des raisons de performances","D\xE9lai d'expiration en millisecondes avant annulation du calcul de diff. Utilisez\xA00 pour supprimer le d\xE9lai d'expiration.","Contr\xF4le si l'\xE9diteur de diff\xE9rences affiche les diff\xE9rences en mode c\xF4te \xE0 c\xF4te ou inline.","Quand il est activ\xE9, l'\xE9diteur de diff\xE9rences ignore les changements d'espace blanc de d\xE9but ou de fin.","Contr\xF4le si l'\xE9diteur de diff\xE9rences affiche les indicateurs +/- pour les changements ajout\xE9s/supprim\xE9s .","Contr\xF4le si l'\xE9diteur affiche CodeLens.","Le retour automatique \xE0 la ligne n'est jamais effectu\xE9.","Le retour automatique \xE0 la ligne s'effectue en fonction de la largeur de la fen\xEAtre d'affichage.","Le retour automatique \xE0 la ligne d\xE9pend du param\xE8tre '#editor.wordWrap#'."],"vs/editor/common/config/editorOptions":["L'\xE9diteur utilise les API de la plateforme pour d\xE9tecter si un lecteur d'\xE9cran est attach\xE9.","L'\xE9diteur est optimis\xE9 en permanence pour les lecteurs d'\xE9cran. Le retour automatique \xE0 la ligne est d\xE9sactiv\xE9.","L'\xE9diteur n'est jamais optimis\xE9 pour une utilisation avec un lecteur d'\xE9cran.","Contr\xF4le si l'\xE9diteur doit s'ex\xE9cuter dans un mode optimis\xE9 pour les lecteurs d'\xE9cran. Si la valeur est on, le retour automatique \xE0 la ligne est d\xE9sactiv\xE9.","Contr\xF4le si un espace est ins\xE9r\xE9 pour les commentaires.","Contr\xF4le si les lignes vides doivent \xEAtre ignor\xE9es avec des actions d'activation/de d\xE9sactivation, d'ajout ou de suppression des commentaires de ligne.","Contr\xF4le si la copie sans s\xE9lection permet de copier la ligne actuelle.","Contr\xF4le si le curseur doit sauter pour rechercher les correspondances lors de la saisie.","D\xE9termine si la cha\xEEne de recherche dans le Widget Recherche est initialis\xE9e avec la s\xE9lection de l\u2019\xE9diteur.","Ne jamais activer Rechercher automatiquement dans la s\xE9lection (par d\xE9faut)","Toujours activer Rechercher automatiquement dans la s\xE9lection","Activez Rechercher automatiquement dans la s\xE9lection quand plusieurs lignes de contenu sont s\xE9lectionn\xE9es.","Contr\xF4le la condition d'activation automatique de la recherche dans la s\xE9lection.","D\xE9termine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partag\xE9 sur macOS.","Contr\xF4le si le widget Recherche doit ajouter des lignes suppl\xE9mentaires en haut de l'\xE9diteur. Quand la valeur est true, vous pouvez faire d\xE9filer au-del\xE0 de la premi\xE8re ligne si le widget Recherche est visible.","Contr\xF4le si la recherche red\xE9marre automatiquement depuis le d\xE9but (ou la fin) quand il n'existe aucune autre correspondance.","Active/d\xE9sactive les ligatures de police (fonctionnalit\xE9s de police 'calt' et 'liga'). Remplacez ceci par une cha\xEEne pour contr\xF4ler de mani\xE8re pr\xE9cise la propri\xE9t\xE9 CSS 'font-feature-settings'.","Propri\xE9t\xE9 CSS 'font-feature-settings' explicite. Vous pouvez passer une valeur bool\xE9enne \xE0 la place si vous devez uniquement activer/d\xE9sactiver les ligatures.","Configure les ligatures de police ou les fonctionnalit\xE9s de police. Il peut s'agir d'une valeur bool\xE9enne permettant d'activer/de d\xE9sactiver les ligatures, ou d'une cha\xEEne correspondant \xE0 la valeur de la propri\xE9t\xE9 CSS 'font-feature-settings'.","Contr\xF4le la taille de police en pixels.",'Seuls les mots cl\xE9s "normal" et "bold", ou les nombres compris entre\xA01 et\xA01\xA0000 sont autoris\xE9s.',`Contr\xF4le l'\xE9paisseur de police. Accepte les mots cl\xE9s "normal" et "bold", ou les nombres compris entre\xA01 et\xA01\xA0000.`,"Montrer l'aper\xE7u des r\xE9sultats (par d\xE9faut)","Acc\xE9der au r\xE9sultat principal et montrer un aper\xE7u","Acc\xE9der au r\xE9sultat principal et activer l'acc\xE8s sans aper\xE7u pour les autres","Ce param\xE8tre est d\xE9pr\xE9ci\xE9, utilisez des param\xE8tres distincts comme 'editor.editor.gotoLocation.multipleDefinitions' ou 'editor.editor.gotoLocation.multipleImplementations' \xE0 la place.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9finition' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9finition de type' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9claration' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre les impl\xE9mentations' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre les r\xE9f\xE9rences' quand plusieurs emplacements cibles existent.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9finition' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9finition de type' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9claration' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre l'impl\xE9mentation' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la r\xE9f\xE9rence' est l'emplacement actuel.","Contr\xF4le si le pointage est affich\xE9.","Contr\xF4le le d\xE9lai en millisecondes, apr\xE8s lequel le survol est affich\xE9.","Contr\xF4le si le pointage doit rester visible quand la souris est d\xE9plac\xE9e au-dessus.","Active l\u2019ampoule d\u2019action de code dans l\u2019\xE9diteur.","Active les indicateurs inline dans l'\xE9diteur.","Contr\xF4le la taille de police des indicateurs inline dans l'\xE9diteur. Quand la valeur est '0', 90\xA0% de '#editor.fontSize#' est utilis\xE9.","Contr\xF4le la famille de polices des indicateurs inline dans l'\xE9diteur.","Contr\xF4le la hauteur de ligne. Utilisez 0 pour calculer la hauteur de ligne de la taille de la police.","Contr\xF4le si la minimap est affich\xE9e.","Le minimap a la m\xEAme taille que le contenu de l'\xE9diteur (d\xE9filement possible).","Le minimap s'agrandit ou se r\xE9duit selon les besoins pour remplir la hauteur de l'\xE9diteur (pas de d\xE9filement).","Le minimap est r\xE9duit si n\xE9cessaire pour ne jamais d\xE9passer la taille de l'\xE9diteur (pas de d\xE9filement).","Contr\xF4le la taille du minimap.","Contr\xF4le le c\xF4t\xE9 o\xF9 afficher la minimap.","Contr\xF4le quand afficher le curseur du minimap.","\xC9chelle du contenu dessin\xE9 dans le minimap\xA0: 1, 2\xA0ou\xA03.","Afficher les caract\xE8res r\xE9els sur une ligne par opposition aux blocs de couleur.","Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.","Contr\xF4le la quantit\xE9 d\u2019espace entre le bord sup\xE9rieur de l\u2019\xE9diteur et la premi\xE8re ligne.","Contr\xF4le la quantit\xE9 d'espace entre le bord inf\xE9rieur de l'\xE9diteur et la derni\xE8re ligne.","Active une fen\xEAtre contextuelle qui affiche de la documentation sur les param\xE8tres et des informations sur les types \xE0 mesure que vous tapez.","D\xE9termine si le menu de suggestions de param\xE8tres se ferme ou reviens au d\xE9but lorsque la fin de la liste est atteinte.","Activez les suggestions rapides dans les cha\xEEnes.","Activez les suggestions rapides dans les commentaires.","Activez les suggestions rapides en dehors des cha\xEEnes et des commentaires.","Contr\xF4le si les suggestions doivent appara\xEEtre automatiquement pendant la saisie.","Les num\xE9ros de ligne ne sont pas affich\xE9s.","Les num\xE9ros de ligne sont affich\xE9s en nombre absolu.","Les num\xE9ros de ligne sont affich\xE9s sous la forme de distance en lignes \xE0 la position du curseur.","Les num\xE9ros de ligne sont affich\xE9s toutes les 10 lignes.","Contr\xF4le l'affichage des num\xE9ros de ligne.","Nombre de caract\xE8res monospace auxquels cette r\xE8gle d'\xE9diteur effectue le rendu.","Couleur de cette r\xE8gle d'\xE9diteur.","Rendre les r\xE8gles verticales apr\xE8s un certain nombre de caract\xE8res \xE0 espacement fixe. Utiliser plusieurs valeurs pour plusieurs r\xE8gles. Aucune r\xE8gle n'est dessin\xE9e si le tableau est vide.","Ins\xE9rez une suggestion sans remplacer le texte \xE0 droite du curseur.","Ins\xE9rez une suggestion et remplacez le texte \xE0 droite du curseur.","Contr\xF4le si les mots sont remplac\xE9s en cas d'acceptation de la saisie semi-automatique. Notez que cela d\xE9pend des extensions adh\xE9rant \xE0 cette fonctionnalit\xE9.","D\xE9termine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.","Contr\xF4le si le tri favorise trier les mots qui apparaissent pr\xE8s du curseur.","Contr\xF4le si les s\xE9lections de suggestion m\xE9moris\xE9es sont partag\xE9es entre plusieurs espaces de travail et fen\xEAtres (n\xE9cessite '#editor.suggestSelection#').","Contr\xF4le si un extrait de code actif emp\xEAche les suggestions rapides.","Contr\xF4le s'il faut montrer ou masquer les ic\xF4nes dans les suggestions.","Contr\xF4le la visibilit\xE9 de la barre d'\xE9tat en bas du widget de suggestion.","D\xE9termine si les d\xE9tails du widget de suggestion sont inclus dans l'\xE9tiquette ou uniquement dans le widget de d\xE9tails","Ce param\xE8tre est d\xE9pr\xE9ci\xE9. Le widget de suggestion peut d\xE9sormais \xEAtre redimensionn\xE9.","Ce param\xE8tre est d\xE9pr\xE9ci\xE9, veuillez utiliser des param\xE8tres distincts comme 'editor.suggest.showKeywords' ou 'editor.suggest.showSnippets' \xE0 la place.","Si activ\xE9, IntelliSense montre des suggestions de type 'method'.","Si activ\xE9, IntelliSense montre des suggestions de type 'function'.","Si activ\xE9, IntelliSense montre des suggestions de type 'constructor'.","Si activ\xE9, IntelliSense montre des suggestions de type 'field'.","Si activ\xE9, IntelliSense montre des suggestions de type 'variable'.","Si activ\xE9, IntelliSense montre des suggestions de type 'class'.","Si activ\xE9, IntelliSense montre des suggestions de type 'struct'.","Si activ\xE9, IntelliSense montre des suggestions de type 'interface'.","Si activ\xE9, IntelliSense montre des suggestions de type 'module'.","Si activ\xE9, IntelliSense montre des suggestions de type 'property'.","Si activ\xE9, IntelliSense montre des suggestions de type 'event'.","Si activ\xE9, IntelliSense montre des suggestions de type 'operator'.","Si activ\xE9, IntelliSense montre des suggestions de type 'unit'.","Si activ\xE9, IntelliSense montre des suggestions de type 'value'.","Si activ\xE9, IntelliSense montre des suggestions de type 'constant'.","Si activ\xE9, IntelliSense montre des suggestions de type 'enum'.","Si activ\xE9, IntelliSense montre des suggestions de type 'enumMember'.","Si activ\xE9, IntelliSense montre des suggestions de type 'keyword'.","Si activ\xE9, IntelliSense montre des suggestions de type 'text'.","Si activ\xE9, IntelliSense montre des suggestions de type 'color'.","Si activ\xE9, IntelliSense montre des suggestions de type 'file'.","Si activ\xE9, IntelliSense montre des suggestions de type 'reference'.","Si activ\xE9, IntelliSense montre des suggestions de type 'customcolor'.","Si activ\xE9, IntelliSense montre des suggestions de type 'folder'.","Si activ\xE9, IntelliSense montre des suggestions de type 'typeParameter'.","Si activ\xE9, IntelliSense montre des suggestions de type 'snippet'.","Si activ\xE9, IntelliSense montre des suggestions de type 'utilisateur'.","Si activ\xE9, IntelliSense montre des suggestions de type 'probl\xE8mes'.","Indique si les espaces blancs de d\xE9but et de fin doivent toujours \xEAtre s\xE9lectionn\xE9s.","Contr\xF4le si les suggestions doivent \xEAtre accept\xE9es sur les caract\xE8res de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut \xEAtre un caract\xE8re de validation qui accepte une suggestion et tape ce caract\xE8re.","Accepter uniquement une suggestion avec 'Entr\xE9e' quand elle effectue une modification textuelle.","Contr\xF4le si les suggestions sont accept\xE9es apr\xE8s appui sur 'Entr\xE9e', en plus de 'Tab'. Permet d\u2019\xE9viter toute ambigu\xEFt\xE9 entre l\u2019insertion de nouvelles lignes et l'acceptation de suggestions.","Contr\xF4le le nombre de lignes dans l'\xE9diteur qui peuvent \xEAtre lues par un lecteur d'\xE9cran. Avertissement : Ce param\xE8tre a une incidence sur les performances quand le nombre est sup\xE9rieur \xE0 la valeur par d\xE9faut.","Contenu de l'\xE9diteur","Utilisez les configurations de langage pour d\xE9terminer quand fermer automatiquement les parenth\xE8ses.","Fermer automatiquement les parenth\xE8ses uniquement lorsque le curseur est \xE0 gauche de l\u2019espace.","Contr\xF4le si l\u2019\xE9diteur doit fermer automatiquement les parenth\xE8ses quand l\u2019utilisateur ajoute une parenth\xE8se ouvrante.","Tapez avant les guillemets ou les crochets fermants uniquement s'ils sont automatiquement ins\xE9r\xE9s.","Contr\xF4le si l'\xE9diteur doit taper avant les guillemets ou crochets fermants.","Utilisez les configurations de langage pour d\xE9terminer quand fermer automatiquement les guillemets.","Fermer automatiquement les guillemets uniquement lorsque le curseur est \xE0 gauche de l\u2019espace.","Contr\xF4le si l\u2019\xE9diteur doit fermer automatiquement les guillemets apr\xE8s que l\u2019utilisateur ajoute un guillemet ouvrant.","L'\xE9diteur n'ins\xE8re pas de retrait automatiquement.","L'\xE9diteur conserve le retrait de la ligne actuelle.","L'\xE9diteur conserve le retrait de la ligne actuelle et honore les crochets d\xE9finis par le langage.","L'\xE9diteur conserve le retrait de la ligne actuelle, honore les crochets d\xE9finis par le langage et appelle des objets onEnterRules sp\xE9ciaux d\xE9finis par les langages.","L'\xE9diteur conserve le retrait de la ligne actuelle, honore les crochets d\xE9finis par le langage, appelle des objets onEnterRules sp\xE9ciaux d\xE9finis par les langages et honore les objets indentationRules d\xE9finis par les langages.","Contr\xF4le si l'\xE9diteur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, d\xE9placent ou mettent en retrait des lignes.","Utilisez les configurations de langue pour d\xE9terminer quand entourer automatiquement les s\xE9lections.","Entourez avec des guillemets et non des crochets.","Entourez avec des crochets et non des guillemets.","Contr\xF4le si l'\xE9diteur doit automatiquement entourer les s\xE9lections quand l'utilisateur tape des guillemets ou des crochets.","\xC9mule le comportement des tabulations pour la s\xE9lection quand des espaces sont utilis\xE9s \xE0 des fins de mise en retrait. La s\xE9lection respecte les taquets de tabulation.","Contr\xF4le si l'\xE9diteur affiche CodeLens.","Contr\xF4le la famille de polices pour CodeLens.","Contr\xF4le la taille de police en pixels pour CodeLens. Quand la valeur est '0', 90\xA0% de '#editor.fontSize#' est utilis\xE9.","Contr\xF4le si l'\xE9diteur doit afficher les \xE9l\xE9ments d\xE9coratifs de couleurs inline et le s\xE9lecteur de couleurs.","Autoriser l'utilisation de la souris et des touches pour s\xE9lectionner des colonnes.","Contr\xF4le si la coloration syntaxique doit \xEAtre copi\xE9e dans le presse-papiers.","Contr\xF4ler le style d\u2019animation du curseur.","Contr\xF4le si l'animation du point d'insertion doit \xEAtre activ\xE9e.","Contr\xF4le le style du curseur.","Contr\xF4le le nombre minimal de lignes de d\xE9but et de fin visibles autour du curseur. \xC9galement appel\xE9 'scrollOff' ou 'scrollOffset' dans d'autres \xE9diteurs.","'cursorSurroundingLines' est appliqu\xE9 seulement s'il est d\xE9clench\xE9 via le clavier ou une API.","'cursorSurroundingLines' est toujours appliqu\xE9.","Contr\xF4le quand 'cursorSurroundingLines' doit \xEAtre appliqu\xE9.","D\xE9termine la largeur du curseur lorsque `#editor.cursorStyle#` est \xE0 `line`.","Contr\xF4le si l\u2019\xE9diteur autorise le d\xE9placement de s\xE9lections par glisser-d\xE9placer.","Multiplicateur de vitesse de d\xE9filement quand vous appuyez sur 'Alt'.","Contr\xF4le si l'\xE9diteur a le pliage de code activ\xE9.","Utilisez une strat\xE9gie de pliage propre \xE0 la langue, si disponible, sinon utilisez la strat\xE9gie bas\xE9e sur le retrait.","Utilisez la strat\xE9gie de pliage bas\xE9e sur le retrait.","Contr\xF4le la strat\xE9gie de calcul des plages de pliage.","Contr\xF4le si l'\xE9diteur doit mettre en \xE9vidence les plages pli\xE9es.","Contr\xF4le si le fait de cliquer sur le contenu vide apr\xE8s une ligne pli\xE9e d\xE9plie la ligne.","Contr\xF4le la famille de polices.","D\xE9termine si l\u2019\xE9diteur doit automatiquement mettre en forme le contenu coll\xE9. Un formateur doit \xEAtre disponible et \xEAtre capable de mettre en forme une plage dans un document.","Contr\xF4le si l\u2019\xE9diteur doit mettre automatiquement en forme la ligne apr\xE8s la saisie.","Contr\xF4le si l'\xE9diteur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au d\xE9bogage.","Contr\xF4le si le curseur doit \xEAtre masqu\xE9 dans la r\xE8gle de la vue d\u2019ensemble.","Contr\xF4le si l\u2019\xE9diteur doit mettre en surbrillance le guide de mise en retrait actif.","Contr\xF4le l'espacement des lettres en pixels.","Contr\xF4le si la modification li\xE9e est activ\xE9e dans l'\xE9diteur. En fonction du langage, les symboles associ\xE9s, par exemple les balises HTML, sont mis \xE0 jour durant le processus de modification.","Contr\xF4le si l\u2019\xE9diteur doit d\xE9tecter les liens et les rendre cliquables.","Mettez en surbrillance les crochets correspondants.","Un multiplicateur \xE0 utiliser sur les `deltaX` et `deltaY` des \xE9v\xE9nements de d\xE9filement de roulette de souris.","Faire un zoom sur la police de l'\xE9diteur quand l'utilisateur fait tourner la roulette de la souris tout en maintenant la touche 'Ctrl' enfonc\xE9e.","Fusionnez plusieurs curseurs quand ils se chevauchent.","Mappe vers 'Contr\xF4le' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur \xE0 utiliser pour ajouter plusieurs curseurs avec la souris. Les gestes de souris Atteindre la d\xE9finition et Ouvrir le lien s'adapteront tels qu\u2019ils n\u2019entrent pas en conflit avec le modificateur multicursor. [Lire la suite] (https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Chaque curseur colle une seule ligne de texte.","Chaque curseur colle le texte en entier.","Contr\xF4le le collage quand le nombre de lignes du texte coll\xE9 correspond au nombre de curseurs.","Contr\xF4le si l'\xE9diteur doit mettre en surbrillance les occurrences de symboles s\xE9mantiques.","Contr\xF4le si une bordure doit \xEAtre dessin\xE9e autour de la r\xE8gle de la vue d'ensemble.","Focus sur l'arborescence \xE0 l'ouverture de l'aper\xE7u","Placer le focus sur l'\xE9diteur \xE0 l'ouverture de l'aper\xE7u","Contr\xF4le s'il faut mettre le focus sur l'\xE9diteur inline ou sur l'arborescence dans le widget d'aper\xE7u.","Contr\xF4le si le geste de souris Acc\xE9der \xE0 la d\xE9finition ouvre toujours le widget d'aper\xE7u.","Contr\xF4le le d\xE9lai en millisecondes apr\xE8s lequel des suggestions rapides sont affich\xE9es.","Contr\xF4le si l'\xE9diteur renomme automatiquement selon le type.","D\xE9pr\xE9ci\xE9. Utilisez 'editor.linkedEditing' \xE0 la place.","Contr\xF4le si l\u2019\xE9diteur doit afficher les caract\xE8res de contr\xF4le.","Contr\xF4le si l\u2019\xE9diteur doit afficher les guides de mise en retrait.","Affichez le dernier num\xE9ro de ligne quand le fichier se termine par un saut de ligne.","Met en surbrillance la goutti\xE8re et la ligne actuelle.","Contr\xF4le la fa\xE7on dont l\u2019\xE9diteur doit afficher la mise en surbrillance de la ligne actuelle.","Contr\xF4le si l'\xE9diteur doit afficher la mise en surbrillance de la ligne actuelle seulement quand l'\xE9diteur a le focus","Affiche les espaces blancs \xE0 l'exception des espaces uniques entre les mots.","Afficher les espaces blancs uniquement sur le texte s\xE9lectionn\xE9.","Afficher uniquement les caract\xE8res correspondant aux espaces blancs de fin","Contr\xF4le la fa\xE7on dont l\u2019\xE9diteur doit restituer les caract\xE8res espaces.","Contr\xF4le si les s\xE9lections doivent avoir des angles arrondis.","Contr\xF4le le nombre de caract\xE8res suppl\xE9mentaires, au-del\xE0 duquel l\u2019\xE9diteur d\xE9file horizontalement.","Contr\xF4le si l\u2019\xE9diteur d\xE9file au-del\xE0 de la derni\xE8re ligne.","Faites d\xE9filer uniquement le long de l'axe pr\xE9dominant quand le d\xE9filement est \xE0 la fois vertical et horizontal. Emp\xEAche la d\xE9rive horizontale en cas de d\xE9filement vertical sur un pav\xE9 tactile.","Contr\xF4le si le presse-papiers principal Linux doit \xEAtre pris en charge.","Contr\xF4le si l'\xE9diteur doit mettre en surbrillance les correspondances similaires \xE0 la s\xE9lection.","Affichez toujours les contr\xF4les de pliage.","Affichez uniquement les contr\xF4les de pliage quand la souris est au-dessus de la reliure.","Contr\xF4le quand afficher les contr\xF4les de pliage sur la reliure.","Contr\xF4le la disparition du code inutile.","Contr\xF4le les variables d\xE9pr\xE9ci\xE9es barr\xE9es.","Afficher des suggestions d\u2019extraits au-dessus d\u2019autres suggestions.","Afficher des suggestions d\u2019extraits en-dessous d\u2019autres suggestions.","Afficher des suggestions d\u2019extraits avec d\u2019autres suggestions.","Ne pas afficher de suggestions d\u2019extrait de code.","Contr\xF4le si les extraits de code s'affichent en m\xEAme temps que d'autres suggestions, ainsi que leur mode de tri.","Contr\xF4le si l'\xE9diteur d\xE9file en utilisant une animation.","Taille de la police pour le widget de suggestion. Lorsque la valeur est \xE0 `0`, la valeur de `#editor.fontSize` est utilis\xE9e.","Hauteur de ligne du widget de suggestion. Quand la valeur est '0', la valeur de '#editor.lineHeight#' est utilis\xE9e. La valeur minimale est\xA08.","Contr\xF4le si les suggestions devraient automatiquement s\u2019afficher lorsque vous tapez les caract\xE8res de d\xE9clencheur.","S\xE9lectionnez toujours la premi\xE8re suggestion.","S\xE9lectionnez les suggestions r\xE9centes sauf si une entr\xE9e ult\xE9rieure en a s\xE9lectionn\xE9 une, par ex., 'console.| -> console.log', car 'log' a \xE9t\xE9 effectu\xE9 r\xE9cemment.","S\xE9lectionnez des suggestions en fonction des pr\xE9fixes pr\xE9c\xE9dents qui ont compl\xE9t\xE9 ces suggestions, par ex., 'co -> console' et 'con -> const'.","Contr\xF4le comment les suggestions sont pr\xE9-s\xE9lectionn\xE9s lors de l\u2019affichage de la liste de suggestion.","La compl\xE9tion par tabulation ins\xE9rera la meilleure suggestion lorsque vous appuyez sur tab.","D\xE9sactiver les compl\xE9tions par tabulation.","Compl\xE9ter les extraits de code par tabulation lorsque leur pr\xE9fixe correspond. Fonctionne mieux quand les 'quickSuggestions' ne sont pas activ\xE9es.","Active les compl\xE9tions par tabulation","Les marques de fin de ligne inhabituelles sont automatiquement supprim\xE9es.","Les marques de fin de ligne inhabituelles sont ignor\xE9es.","Les marques de fin de ligne inhabituelles demandent \xE0 \xEAtre supprim\xE9es.","Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des probl\xE8mes.","L'insertion et la suppression des espaces blancs suit les taquets de tabulation.","Caract\xE8res utilis\xE9s comme s\xE9parateurs de mots durant la navigation ou les op\xE9rations bas\xE9es sur les mots","Le retour automatique \xE0 la ligne n'est jamais effectu\xE9.","Le retour automatique \xE0 la ligne s'effectue en fonction de la largeur de la fen\xEAtre d'affichage.","Les lignes seront termin\xE9es \xE0 `#editor.wordWrapColumn#`.","Les lignes seront termin\xE9es au minimum du viewport et `#editor.wordWrapColumn#`.","Contr\xF4le comment les lignes doivent \xEAtre limit\xE9es.","Contr\xF4le la colonne de terminaison de l\u2019\xE9diteur lorsque `#editor.wordWrap#` est \xE0 `wordWrapColumn` ou `bounded`.","Aucune mise en retrait. Les lignes envelopp\xE9es commencent \xE0 la colonne 1.","Les lignes envelopp\xE9es obtiennent la m\xEAme mise en retrait que le parent.","Les lignes justifi\xE9es obtiennent une mise en retrait +1 vers le parent.","Les lignes justifi\xE9es obtiennent une mise en retrait +2 vers le parent. ","Contr\xF4le la mise en retrait des lignes justifi\xE9es.","Suppose que tous les caract\xE8res ont la m\xEAme largeur. Il s'agit d'un algorithme rapide qui fonctionne correctement pour les polices \xE0 espacement fixe et certains scripts (comme les caract\xE8res latins) o\xF9 les glyphes ont la m\xEAme largeur.","D\xE9l\xE8gue le calcul des points de wrapping au navigateur. Il s'agit d'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.","Contr\xF4le l'algorithme qui calcule les points de wrapping."],"vs/editor/common/editorContextKeys":["Indique si le texte de l'\xE9diteur a le focus (le curseur clignote)","Indique si l'\xE9diteur ou un widget de l'\xE9diteur a le focus (par exemple, le focus se trouve sur le widget de recherche)","Indique si un \xE9diteur ou une entr\xE9e de texte mis en forme a le focus (le curseur clignote)","Indique si l'\xE9diteur est en lecture seule","Indique si le contexte est celui d'un \xE9diteur de diff\xE9rences","Indique si 'editor.columnSelection' est activ\xE9","Indique si du texte est s\xE9lectionn\xE9 dans l'\xE9diteur","Indique si l'\xE9diteur a plusieurs s\xE9lections","Indique si la touche Tab permet de d\xE9placer le focus hors de l'\xE9diteur","Indique si le pointage de l'\xE9diteur est visible","Indique si l'\xE9diteur fait partie d'un \xE9diteur plus important (par exemple Notebooks)","Identificateur de langage de l'\xE9diteur","Indique si l'\xE9diteur a un fournisseur d'\xE9l\xE9ments de compl\xE9tion","Indique si l'\xE9diteur a un fournisseur d'actions de code","Indique si l'\xE9diteur a un fournisseur d'informations CodeLens","Indique si l'\xE9diteur a un fournisseur de d\xE9finitions","Indique si l'\xE9diteur a un fournisseur de d\xE9clarations","Indique si l'\xE9diteur a un fournisseur d'impl\xE9mentation","Indique si l'\xE9diteur a un fournisseur de d\xE9finitions de type","Indique si l'\xE9diteur a un fournisseur de pointage","Indique si l'\xE9diteur a un fournisseur de mise en surbrillance pour les documents","Indique si l'\xE9diteur a un fournisseur de symboles pour les documents","Indique si l'\xE9diteur a un fournisseur de r\xE9f\xE9rence","Indique si l'\xE9diteur a un fournisseur de renommage","Indique si l'\xE9diteur a un fournisseur d'aide sur les signatures","Indique si l'\xE9diteur a un fournisseur d'indicateurs inline","Indique si l'\xE9diteur a un fournisseur de mise en forme pour les documents","Indique si l'\xE9diteur a un fournisseur de mise en forme de s\xE9lection pour les documents","Indique si l'\xE9diteur a plusieurs fournisseurs de mise en forme pour les documents","Indique si l'\xE9diteur a plusieurs fournisseurs de mise en forme de s\xE9lection pour les documents"],"vs/editor/common/model/editStack":["Frappe en cours"],"vs/editor/common/modes/modesRegistry":["Texte brut"],"vs/editor/common/standaloneStrings":["Aucune s\xE9lection","Ligne {0}, colonne {1} ({2} s\xE9lectionn\xE9)","Ligne {0}, colonne {1}","{0} s\xE9lections ({1} caract\xE8res s\xE9lectionn\xE9s)","{0} s\xE9lections","Remplacement du param\xE8tre 'accessibilitySupport' par 'on'.","Ouverture de la page de documentation sur l'accessibilit\xE9 de l'\xE9diteur.","dans un volet en lecture seule d'un \xE9diteur de diff\xE9rences.","dans un volet d'un \xE9diteur de diff\xE9rences."," dans un \xE9diteur de code en lecture seule"," dans un \xE9diteur de code","Pour configurer l'\xE9diteur de mani\xE8re \xE0 \xEAtre optimis\xE9 en cas d'utilisation d'un lecteur d'\xE9cran, appuyez sur Commande+E maintenant.","Pour configurer l'\xE9diteur de mani\xE8re \xE0 \xEAtre optimis\xE9 en cas d'utilisation d'un lecteur d'\xE9cran, appuyez sur Contr\xF4le+E maintenant.","L'\xE9diteur est configur\xE9 pour \xEAtre optimis\xE9 en cas d'utilisation avec un lecteur d'\xE9cran.","L'\xE9diteur est configur\xE9 pour ne jamais \xEAtre optimis\xE9 en cas d'utilisation avec un lecteur d'\xE9cran, ce qui n'est pas le cas pour le moment.","Appuyez sur Tab dans l'\xE9diteur pour d\xE9placer le focus vers le prochain \xE9l\xE9ment pouvant \xEAtre d\xE9sign\xE9 comme \xE9l\xE9ment actif. Activez ou d\xE9sactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'\xE9diteur pour d\xE9placer le focus vers le prochain \xE9l\xE9ment pouvant \xEAtre d\xE9sign\xE9 comme \xE9l\xE9ment actif. La commande {0} ne peut pas \xEAtre d\xE9clench\xE9e par une combinaison de touches.","Appuyez sur Tab dans l'\xE9diteur pour ins\xE9rer le caract\xE8re de tabulation. Activez ou d\xE9sactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'\xE9diteur pour ins\xE9rer le caract\xE8re de tabulation. La commande {0} ne peut pas \xEAtre d\xE9clench\xE9e par une combinaison de touches.","Appuyez sur Commande+H maintenant pour ouvrir une fen\xEAtre de navigateur avec plus d'informations sur l'accessibilit\xE9 de l'\xE9diteur.","Appuyez sur Contr\xF4le+H maintenant pour ouvrir une fen\xEAtre de navigateur avec plus d'informations sur l'accessibilit\xE9 de l'\xE9diteur.","Vous pouvez masquer cette info-bulle et revenir \xE0 l'\xE9diteur en appuyant sur \xC9chap ou Maj+\xC9chap.","Afficher l'aide sur l'accessibilit\xE9","D\xE9veloppeur\xA0: Inspecter les jetons","Acc\xE9der \xE0 la ligne/colonne...","Afficher tous les fournisseurs d'acc\xE8s rapide","Palette de commandes","Commandes d'affichage et d'ex\xE9cution","Acc\xE9der au symbole...","Acc\xE9der au symbole par cat\xE9gorie...","Contenu de l'\xE9diteur","Appuyez sur Alt+F1 pour voir les options d'accessibilit\xE9.","Activer/d\xE9sactiver le th\xE8me \xE0 contraste \xE9lev\xE9","{0} modifications dans {1} fichiers"],"vs/editor/common/view/editorColorRegistry":["Couleur d'arri\xE8re-plan de la mise en surbrillance de la ligne \xE0 la position du curseur.","Couleur d'arri\xE8re-plan de la bordure autour de la ligne \xE0 la position du curseur.","Couleur d'arri\xE8re-plan des plages mises en surbrillance, comme par les fonctionnalit\xE9s de recherche et Quick Open. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan de la bordure autour des plages mises en surbrillance.","Couleur d'arri\xE8re-plan du symbole mis en surbrillance, comme le symbole Atteindre la d\xE9finition ou Suivant/Pr\xE9c\xE9dent. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur d'arri\xE8re-plan de la bordure autour des symboles mis en surbrillance.","Couleur du curseur de l'\xE9diteur.","La couleur de fond du curseur de l'\xE9diteur. Permet de personnaliser la couleur d'un caract\xE8re survol\xE9 par un curseur de bloc.","Couleur des espaces blancs dans l'\xE9diteur.","Couleur des rep\xE8res de retrait de l'\xE9diteur.","Couleur des guides d'indentation de l'\xE9diteur actif","Couleur des num\xE9ros de ligne de l'\xE9diteur.","Couleur des num\xE9ros de lignes actives de l'\xE9diteur","L\u2019ID est d\xE9pr\xE9ci\xE9. Utilisez \xE0 la place 'editorLineNumber.activeForeground'.","Couleur des num\xE9ros de lignes actives de l'\xE9diteur","Couleur des r\xE8gles de l'\xE9diteur","Couleur pour les indicateurs CodeLens","Couleur d'arri\xE8re-plan pour les accolades associ\xE9es","Couleur pour le contour des accolades associ\xE9es","Couleur de la bordure de la r\xE8gle d'aper\xE7u.","Couleur d'arri\xE8re-plan de la r\xE8gle d'aper\xE7u de l'\xE9diteur. Utilis\xE9e uniquement quand la minimap est activ\xE9e et plac\xE9e sur le c\xF4t\xE9 droit de l'\xE9diteur.","Couleur de fond pour la bordure de l'\xE9diteur. La bordure contient les marges pour les symboles et les num\xE9ros de ligne.","Couleur de bordure du code source inutile (non utilis\xE9) dans l'\xE9diteur.","Opacit\xE9 du code source inutile (non utilis\xE9) dans l'\xE9diteur. Par exemple, '#000000c0' affiche le code avec une opacit\xE9 de 75\xA0%. Pour les th\xE8mes \xE0 fort contraste, utilisez la couleur de th\xE8me 'editorUnnecessaryCode.border' pour souligner le code inutile au lieu d'utiliser la transparence.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des plages. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les erreurs.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les avertissements.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les informations."],"vs/editor/contrib/anchorSelect/anchorSelect":["Ancre de s\xE9lection","Ancre d\xE9finie sur {0}:{1}","D\xE9finir l'ancre de s\xE9lection","Atteindre l'ancre de s\xE9lection","S\xE9lectionner de l'ancre au curseur","Annuler l'ancre de s\xE9lection"],"vs/editor/contrib/bracketMatching/bracketMatching":["Couleur du marqueur de la r\xE8gle d'aper\xE7u pour rechercher des parenth\xE8ses.","Atteindre le crochet","S\xE9lectionner jusqu'au crochet","Acc\xE9der au &&crochet"],"vs/editor/contrib/caretOperations/caretOperations":["D\xE9placer le texte s\xE9lectionn\xE9 \xE0 gauche","D\xE9placer le texte s\xE9lectionn\xE9 \xE0 droite"],"vs/editor/contrib/caretOperations/transpose":["Transposer les lettres"],"vs/editor/contrib/clipboard/clipboard":["Co&&uper","Couper","Couper","&&Copier","Copier","Copier","Co&&ller","Coller","Coller","Copier avec la coloration syntaxique"],"vs/editor/contrib/codeAction/codeActionCommands":["Type d'action de code \xE0 ex\xE9cuter.","Contr\xF4le quand les actions retourn\xE9es sont appliqu\xE9es.","Appliquez toujours la premi\xE8re action de code retourn\xE9e.","Appliquez la premi\xE8re action de code retourn\xE9e si elle est la seule.","N'appliquez pas les actions de code retourn\xE9es.","Contr\xF4le si seules les actions de code par d\xE9faut doivent \xEAtre retourn\xE9es.","Une erreur inconnue s'est produite \xE0 l'application de l'action du code","Correction rapide...","Aucune action de code disponible","Aucune action de code pr\xE9f\xE9r\xE9e n'est disponible pour '{0}'","Aucune action de code disponible pour '{0}'","Aucune action de code par d\xE9faut disponible","Aucune action de code disponible","Remanier...","Aucune refactorisation par d\xE9faut disponible pour '{0}'","Aucune refactorisation disponible pour '{0}'","Aucune refactorisation par d\xE9faut disponible","Aucune refactorisation disponible","Action de la source","Aucune action source par d\xE9faut disponible pour '{0}'","Aucune action source disponible pour '{0}'","Aucune action source par d\xE9faut disponible","Aucune action n'est disponible","Organiser les importations","Aucune action organiser les imports disponible","Tout corriger","Aucune action Tout corriger disponible","Corriger automatiquement...","Aucun correctif automatique disponible"],"vs/editor/contrib/codeAction/lightBulbWidget":["Affichez les corrections. Correction pr\xE9f\xE9r\xE9e disponible ({0})","Afficher les correctifs ({0})","Afficher les correctifs"],"vs/editor/contrib/codelens/codelensController":["Afficher les commandes Code Lens de la ligne actuelle"],"vs/editor/contrib/comment/comment":["Activer/d\xE9sactiver le commentaire de ligne","Afficher/masquer le commen&&taire de ligne","Ajouter le commentaire de ligne","Supprimer le commentaire de ligne","Activer/d\xE9sactiver le commentaire de bloc","Afficher/masquer le commentaire de &&bloc"],"vs/editor/contrib/contextmenu/contextmenu":["Afficher le menu contextuel de l'\xE9diteur"],"vs/editor/contrib/cursorUndo/cursorUndo":["Annulation du curseur","Restauration du curseur"],"vs/editor/contrib/find/findController":["Rechercher","&&Rechercher","Rechercher dans la s\xE9lection","Rechercher suivant","Rechercher suivant","Rechercher pr\xE9c\xE9dent","Rechercher pr\xE9c\xE9dent","S\xE9lection suivante","S\xE9lection pr\xE9c\xE9dente","Remplacer","&&Remplacer"],"vs/editor/contrib/find/findWidget":["Ic\xF4ne de l'option Rechercher dans la s\xE9lection dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne permettant d'indiquer que le widget de recherche de l'\xE9diteur est r\xE9duit.","Ic\xF4ne permettant d'indiquer que le widget de recherche de l'\xE9diteur est d\xE9velopp\xE9.","Ic\xF4ne de l'option Remplacer dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Tout remplacer dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Rechercher pr\xE9c\xE9dent dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Rechercher suivant dans le widget de recherche de l'\xE9diteur.","Rechercher","Rechercher","Correspondance pr\xE9c\xE9dente","Prochaine correspondance","Rechercher dans la s\xE9lection","Fermer","Remplacer","Remplacer","Remplacer","Tout remplacer","Changer le mode de remplacement","Seuls les {0} premiers r\xE9sultats sont mis en \xE9vidence, mais toutes les op\xE9rations de recherche fonctionnent sur l\u2019ensemble du texte.","{0} sur {1}","Aucun r\xE9sultat","{0} trouv\xE9(s)","{0} trouv\xE9 pour '{1}'","{0} trouv\xE9 pour '{1}', sur {2}","{0} trouv\xE9 pour '{1}'","La combinaison Ctrl+Entr\xE9e permet d\xE9sormais d'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour red\xE9finir le comportement."],"vs/editor/contrib/folding/folding":["D\xE9plier","D\xE9plier de mani\xE8re r\xE9cursive","Plier","Activer/d\xE9sactiver le pliage","Plier de mani\xE8re r\xE9cursive","Replier tous les commentaires de bloc","Replier toutes les r\xE9gions","D\xE9plier toutes les r\xE9gions","Plier tout","D\xE9plier tout","Niveau de pliage {0}","Couleur d'arri\xE8re-plan des gammes pli\xE9es. La couleur ne doit pas \xEAtre opaque pour ne pas cacher les d\xE9corations sous-jacentes.","Couleur du contr\xF4le de pliage dans la marge de l'\xE9diteur."],"vs/editor/contrib/folding/foldingDecorations":["Ic\xF4ne des plages d\xE9velopp\xE9es dans la marge de glyphes de l'\xE9diteur.","Ic\xF4ne des plages r\xE9duites dans la marge de glyphes de l'\xE9diteur."],"vs/editor/contrib/fontZoom/fontZoom":["Agrandissement de l'\xE9diteur de polices de caract\xE8res","R\xE9tr\xE9cissement de l'\xE9diteur de polices de caract\xE8res","Remise \xE0 niveau du zoom de l'\xE9diteur de polices de caract\xE8res"],"vs/editor/contrib/format/format":["1\xA0modification de format effectu\xE9e \xE0 la ligne {0}","{0} modifications de format effectu\xE9es \xE0 la ligne {1}","1\xA0modification de format effectu\xE9e entre les lignes {0} et {1}","{0} modifications de format effectu\xE9es entre les lignes {1} et {2}"],"vs/editor/contrib/format/formatActions":["Mettre le document en forme","Mettre la s\xE9lection en forme"],"vs/editor/contrib/gotoError/gotoError":["Aller au probl\xE8me suivant (Erreur, Avertissement, Info)","Ic\xF4ne du prochain marqueur goto.","Aller au probl\xE8me pr\xE9c\xE9dent (Erreur, Avertissement, Info)","Ic\xF4ne du pr\xE9c\xE9dent marqueur goto.","Aller au probl\xE8me suivant dans Fichiers (Erreur, Avertissement, Info)","&&Probl\xE8me suivant","Aller au probl\xE8me pr\xE9c\xE9dent dans Fichiers (Erreur, Avertissement, Info)","&&Probl\xE8me pr\xE9c\xE9dent"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Erreur","Avertissement","Info","Conseil","{0} \xE0 {1}. ","{0}\xA0probl\xE8mes sur\xA0{1}","{0}\xA0probl\xE8me(s) sur {1}","Couleur d'erreur du widget de navigation dans les marqueurs de l'\xE9diteur.","Couleur d'avertissement du widget de navigation dans les marqueurs de l'\xE9diteur.","Couleur d\u2019information du widget de navigation du marqueur de l'\xE9diteur.","Arri\xE8re-plan du widget de navigation dans les marqueurs de l'\xE9diteur."],"vs/editor/contrib/gotoSymbol/goToCommands":["Aper\xE7u","D\xE9finitions","D\xE9finition introuvable pour '{0}'","D\xE9finition introuvable","Atteindre la d\xE9finition","Atteindre la &&d\xE9finition","Ouvrir la d\xE9finition sur le c\xF4t\xE9","Faire un Peek de la D\xE9finition","D\xE9clarations","Aucune d\xE9claration pour '{0}'","Aucune d\xE9claration","Acc\xE9der \xE0 la d\xE9claration","Atteindre la &&d\xE9claration","Aucune d\xE9claration pour '{0}'","Aucune d\xE9claration","Aper\xE7u de la d\xE9claration","D\xE9finitions de type","D\xE9finition de type introuvable pour '{0}'","D\xE9finition de type introuvable","Atteindre la d\xE9finition de type","Acc\xE9der \xE0 la d\xE9finition de &&type","Aper\xE7u de la d\xE9finition du type","Impl\xE9mentations","Impl\xE9mentation introuvable pour '{0}'","Impl\xE9mentation introuvable","Atteindre les impl\xE9mentations","Atteindre les &&impl\xE9mentations","Impl\xE9mentations d'aper\xE7u","Aucune r\xE9f\xE9rence pour '{0}'","Aucune r\xE9f\xE9rence","Atteindre les r\xE9f\xE9rences","Atteindre les &&r\xE9f\xE9rences","R\xE9f\xE9rences","Aper\xE7u des r\xE9f\xE9rences","R\xE9f\xE9rences","Atteindre un symbole","Emplacements","Aucun r\xE9sultat pour \xAB\xA0{0}\xA0\xBB","R\xE9f\xE9rences"],"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Cliquez pour afficher {0}\xA0d\xE9finitions."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Chargement en cours...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["{0} r\xE9f\xE9rences","{0} r\xE9f\xE9rence","R\xE9f\xE9rences"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["aper\xE7u non disponible","Aucun r\xE9sultat","R\xE9f\xE9rences"],"vs/editor/contrib/gotoSymbol/referencesModel":["symbole dans {0} sur la ligne {1}, colonne {2}","symbole dans {0} \xE0 la ligne {1}, colonne {2}, {3}","1 symbole dans {0}, chemin complet {1}","{0} symboles dans {1}, chemin complet {2}","R\xE9sultats introuvables","1\xA0symbole dans {0}","{0}\xA0symboles dans {1}","{0}\xA0symboles dans {1} fichiers"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Symbole {0} sur {1}, {2} pour le suivant","Symbole {0} sur {1}"],"vs/editor/contrib/hover/hover":["Afficher par pointage","Afficher le pointeur de l'aper\xE7u de d\xE9finition"],"vs/editor/contrib/hover/markdownHoverParticipant":["Chargement en cours..."],"vs/editor/contrib/hover/markerHoverParticipant":["Voir le probl\xE8me","Aucune solution disponible dans l'imm\xE9diat","Recherche de correctifs rapides...","Aucune solution disponible dans l'imm\xE9diat","Correction rapide..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Remplacer par la valeur pr\xE9c\xE9dente","Remplacer par la valeur suivante"],"vs/editor/contrib/indentation/indentation":["Convertir les retraits en espaces","Convertir les retraits en tabulations","Taille des tabulations configur\xE9e","S\xE9lectionner la taille des tabulations pour le fichier actuel","Mettre en retrait avec des tabulations","Mettre en retrait avec des espaces","D\xE9tecter la mise en retrait \xE0 partir du contenu","Remettre en retrait les lignes","R\xE9indenter les lignes s\xE9lectionn\xE9es"],"vs/editor/contrib/linesOperations/linesOperations":["Copier la ligne en haut","&&Copier la ligne en haut","Copier la ligne en bas","Co&&pier la ligne en bas","Dupliquer la s\xE9lection","&&Dupliquer la s\xE9lection","D\xE9placer la ligne vers le haut","D\xE9placer la ligne &&vers le haut","D\xE9placer la ligne vers le bas","D\xE9placer la &&ligne vers le bas","Trier les lignes dans l'ordre croissant","Trier les lignes dans l'ordre d\xE9croissant","D\xE9couper l'espace blanc de fin","Supprimer la ligne","Mettre en retrait la ligne","Ajouter un retrait n\xE9gatif \xE0 la ligne","Ins\xE9rer une ligne au-dessus","Ins\xE9rer une ligne sous","Supprimer tout ce qui est \xE0 gauche","Supprimer tout ce qui est \xE0 droite","Joindre les lignes","Transposer les caract\xE8res autour du curseur","Transformer en majuscule","Transformer en minuscule",'Appliquer la casse "1re lettre des mots en majuscule"',"Transformer en snake case"],"vs/editor/contrib/linkedEditing/linkedEditing":["D\xE9marrer la modification li\xE9e","Couleur d'arri\xE8re-plan quand l'\xE9diteur renomme automatiquement le type."],"vs/editor/contrib/links/links":["Ex\xE9cuter la commande","suivre le lien","cmd + clic","ctrl + clic","option + clic","alt + clic","Ex\xE9cuter la commande {0}","\xC9chec de l'ouverture de ce lien, car il n'est pas bien form\xE9\xA0: {0}","\xC9chec de l'ouverture de ce lien, car sa cible est manquante.","Ouvrir le lien"],"vs/editor/contrib/message/messageController":["Indique si l'\xE9diteur affiche un message inline","Impossible de modifier dans l\u2019\xE9diteur en lecture seule"],"vs/editor/contrib/multicursor/multicursor":["Ajouter un curseur au-dessus","&&Ajouter un curseur au-dessus","Ajouter un curseur en dessous","Aj&&outer un curseur en dessous","Ajouter des curseurs \xE0 la fin des lignes","Ajouter des c&&urseurs \xE0 la fin des lignes","Ajouter des curseurs en bas","Ajouter des curseurs en haut","Ajouter la s\xE9lection \xE0 la correspondance de recherche suivante","Ajouter l'occurrence suiva&&nte","Ajouter la s\xE9lection \xE0 la correspondance de recherche pr\xE9c\xE9dente","Ajouter l'occurrence p&&r\xE9c\xE9dente","D\xE9placer la derni\xE8re s\xE9lection vers la correspondance de recherche suivante","D\xE9placer la derni\xE8re s\xE9lection \xE0 la correspondance de recherche pr\xE9c\xE9dente","S\xE9lectionner toutes les occurrences des correspondances de la recherche","S\xE9lectionner toutes les &&occurrences","Modifier toutes les occurrences"],"vs/editor/contrib/parameterHints/parameterHints":["Indicateurs des param\xE8tres Trigger"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["Ic\xF4ne d'affichage du prochain conseil de param\xE8tre.","Ic\xF4ne d'affichage du pr\xE9c\xE9dent conseil de param\xE8tre.","{0}, conseil"],"vs/editor/contrib/peekView/peekView":["Fermer","Couleur d'arri\xE8re-plan de la zone de titre de l'affichage d'aper\xE7u.","Couleur du titre de l'affichage d'aper\xE7u.","Couleur des informations sur le titre de l'affichage d'aper\xE7u.","Couleur des bordures et de la fl\xE8che de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan des noeuds de lignes dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan des noeuds de fichiers dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de l'entr\xE9e s\xE9lectionn\xE9e dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan de l'entr\xE9e s\xE9lectionn\xE9e dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de l'\xE9diteur d'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de la bordure de l'\xE9diteur d'affichage d'aper\xE7u.","Couleur de mise en surbrillance d'une correspondance dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de mise en surbrillance d'une correspondance dans l'\xE9diteur de l'affichage d'aper\xE7u.","Bordure de mise en surbrillance d'une correspondance dans l'\xE9diteur de l'affichage d'aper\xE7u."],"vs/editor/contrib/quickAccess/gotoLineQuickAccess":["Ouvrez d'abord un \xE9diteur de texte pour acc\xE9der \xE0 une ligne.","Allez \xE0 la ligne {0}, colonne {1}.","Acc\xE9dez \xE0 la ligne {0}.","Ligne actuelle\xA0: {0}, caract\xE8re\xA0: {1}. Tapez un num\xE9ro de ligne entre\xA01 et\xA0{2} auquel acc\xE9der.","Ligne actuelle\xA0: {0}, caract\xE8re\xA0: {1}. Tapez un num\xE9ro de ligne auquel acc\xE9der."],"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess":["Pour acc\xE9der \xE0 un symbole, ouvrez d'abord un \xE9diteur de texte avec des informations de symbole.","L'\xE9diteur de texte actif ne fournit pas les informations de symbole.","Aucun symbole d'\xE9diteur correspondant","Aucun symbole d'\xE9diteur","Ouvrir sur le c\xF4t\xE9","Ouvrir en bas","symboles ({0})","propri\xE9t\xE9s ({0})","m\xE9thodes ({0})","fonctions ({0})","constructeurs ({0})","variables ({0})","classes ({0})","structs ({0})","\xE9v\xE9nements ({0})","op\xE9rateurs ({0})","interfaces ({0})","espaces de noms ({0})","packages ({0})","param\xE8tres de type ({0})","modules ({0})","propri\xE9t\xE9s ({0})","\xE9num\xE9rations ({0})","membres d'\xE9num\xE9ration ({0})","cha\xEEnes ({0})","fichiers ({0})","tableaux ({0})","nombres ({0})","bool\xE9ens ({0})","objets ({0})","cl\xE9s ({0})","champs ({0})","constantes ({0})"],"vs/editor/contrib/rename/rename":["Aucun r\xE9sultat.","Une erreur inconnue s'est produite lors de la r\xE9solution de l'emplacement de renommage","Renommage de '{0}'","Changement du nom de {0}","'{0}' renomm\xE9 en '{1}'. R\xE9capitulatif : {2}","Le renommage n'a pas pu appliquer les modifications","Le renommage n'a pas pu calculer les modifications","Renommer le symbole","Activer/d\xE9sactiver la possibilit\xE9 d'afficher un aper\xE7u des changements avant le renommage"],"vs/editor/contrib/rename/renameInputField":["Renommez l'entr\xE9e. Tapez le nouveau nom et appuyez sur Entr\xE9e pour valider.","{0} pour renommer, {1} pour afficher un aper\xE7u"],"vs/editor/contrib/smartSelect/smartSelect":["\xC9tendre la s\xE9lection","D\xE9v&&elopper la s\xE9lection","R\xE9duire la s\xE9lection","&&R\xE9duire la s\xE9lection"],"vs/editor/contrib/snippet/snippetVariables":["Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi","Dim","Lun","Mar","Mer","Jeu","Ven","Sam","Janvier","F\xE9vrier","Mars","Avril","Mai","Juin","Juillet","Ao\xFBt","Septembre","Octobre","Novembre","D\xE9cembre","Jan","F\xE9v","Mar","Avr","Mai","Juin","Jul","Ao\xFB","Sept","Oct","Nov","D\xE9c"],"vs/editor/contrib/suggest/suggestController":["L'acceptation de '{0}' a entra\xEEn\xE9 {1}\xA0modifications suppl\xE9mentaires","Suggestions pour Trigger","Ins\xE9rer","Ins\xE9rer","Remplacer","Remplacer","Ins\xE9rer","afficher moins","afficher plus","R\xE9initialiser la taille du widget de suggestion"],"vs/editor/contrib/suggest/suggestWidget":["Couleur d'arri\xE8re-plan du widget de suggestion.","Couleur de bordure du widget de suggestion.","Couleur de premier plan du widget de suggestion.","Couleur d'arri\xE8re-plan de l'entr\xE9e s\xE9lectionn\xE9e dans le widget de suggestion.","Couleur de la surbrillance des correspondances dans le widget de suggestion.","Chargement en cours...","Pas de suggestions.","{0}, documents\xA0: {1}","Sugg\xE9rer"],"vs/editor/contrib/suggest/suggestWidgetDetails":["Fermer","Chargement en cours..."],"vs/editor/contrib/suggest/suggestWidgetRenderer":["Ic\xF4ne d'affichage d'informations suppl\xE9mentaires dans le widget de suggestion.","Lire la suite"],"vs/editor/contrib/suggest/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/symbolIcons":["Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles bool\xE9ens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'\xE9num\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de membre d'\xE9num\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'\xE9v\xE9nement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de cl\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de mot cl\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de m\xE9thode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'op\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de propri\xE9t\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de r\xE9f\xE9rence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de cha\xEEne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de param\xE8tre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'unit\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion."],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Activer/d\xE9sactiver l'utilisation de la touche Tab pour d\xE9placer le focus","Appuyer sur Tab d\xE9placera le focus vers le prochain \xE9l\xE9ment pouvant \xEAtre d\xE9sign\xE9 comme \xE9l\xE9ment actif","Appuyer sur Tab ins\xE9rera le caract\xE8re de tabulation"],"vs/editor/contrib/tokenization/tokenization":["D\xE9veloppeur\xA0: forcer la retokenisation"],"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators":["Marques de fin de ligne inhabituelles","Marques de fin de ligne inhabituelles d\xE9tect\xE9es",`Ce fichier contient un ou plusieurs caract\xE8res de fin de ligne inhabituels, par exemple le s\xE9parateur de ligne (LS) ou le s\xE9parateur de paragraphe (PS).\r
\r
Il est recommand\xE9 de les supprimer du fichier. Vous pouvez le configurer via 'editor.unusualLineTerminators'.`,"Corriger ce fichier","Ignorer le probl\xE8me pour ce fichier"],"vs/editor/contrib/wordHighlighter/wordHighlighter":["Couleur d'arri\xE8re-plan d'un symbole pendant l'acc\xE8s en lecture, comme la lecture d'une variable. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan d'un symbole pendant l'acc\xE8s en \xE9criture, comme l'\xE9criture d'une variable. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure d'un symbole durant l'acc\xE8s en lecture, par exemple la lecture d'une variable.","Couleur de bordure d'un symbole durant l'acc\xE8s en \xE9criture, par exemple l'\xE9criture dans une variable.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des symboles. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des symboles d'acc\xE8s en \xE9criture. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Aller \xE0 la prochaine mise en \xE9vidence de symbole","Aller \xE0 la mise en \xE9vidence de symbole pr\xE9c\xE9dente","D\xE9clencher la mise en \xE9vidence de symbole"],"vs/editor/contrib/wordOperations/wordOperations":["Supprimer le mot"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["Substitutions de configuration du langage par d\xE9faut","Configurez les param\xE8tres d'\xE9diteur \xE0 remplacer pour un langage.","Ce param\xE8tre ne prend pas en charge la configuration par langage.","Impossible d'inscrire une propri\xE9t\xE9 vide","Impossible d'inscrire '{0}'. Ceci correspond au mod\xE8le de propri\xE9t\xE9 '\\\\[.*\\\\]$' permettant de d\xE9crire les param\xE8tres d'\xE9diteur sp\xE9cifiques \xE0 un langage. Utilisez la contribution 'configurationDefaults'.","Impossible d'inscrire '{0}'. Cette propri\xE9t\xE9 est d\xE9j\xE0 inscrite."],"vs/platform/contextkey/browser/contextKeyService":["Commande qui retourne des informations sur les cl\xE9s de contexte"],"vs/platform/contextkey/common/contextkeys":["Indique si le syst\xE8me d'exploitation est Windows"],"vs/platform/keybinding/common/abstractKeybindingService":["Touche ({0}) utilis\xE9e. En attente d'une seconde touche...","La combinaison de touches ({0}, {1}) n\u2019est pas une commande."],"vs/platform/list/browser/listService":["Banc d'essai","Mappe vers 'Contr\xF4le' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur \xE0 utiliser pour ajouter un \xE9l\xE9ment dans les arbres et listes pour une s\xE9lection multiple avec la souris (par exemple dans l\u2019Explorateur, les \xE9diteurs ouverts et la vue scm). Les mouvements de la souris 'Ouvrir \xE0 c\xF4t\xE9' (si pris en charge) s'adapteront tels qu\u2019ils n'entrent pas en conflit avec le modificateur multiselect.","Contr\xF4le l'ouverture des \xE9l\xE9ments dans les arborescences et les listes \xE0 l'aide de la souris (si cela est pris en charge). Notez que certaines arborescences et listes peuvent choisir d'ignorer ce param\xE8tre, s'il est non applicable.","Contr\xF4le si les listes et les arborescences prennent en charge le d\xE9filement horizontal dans le banc d'essai. Avertissement : L'activation de ce param\xE8tre a un impact sur les performances.","Contr\xF4le la mise en retrait de l'arborescence, en pixels.","Contr\xF4le si l'arborescence doit afficher les rep\xE8res de mise en retrait.","D\xE9termine si les listes et les arborescences ont un d\xE9filement fluide.","La navigation au clavier Simple place le focus sur les \xE9l\xE9ments qui correspondent \xE0 l'entr\xE9e de clavier. La mise en correspondance est effectu\xE9e sur les pr\xE9fixes uniquement.","La navigation de mise en surbrillance au clavier met en surbrillance les \xE9l\xE9ments qui correspondent \xE0 l'entr\xE9e de clavier. La navigation ult\xE9rieure vers le haut ou vers le bas parcourt uniquement les \xE9l\xE9ments mis en surbrillance.","La navigation au clavier Filtrer filtre et masque tous les \xE9l\xE9ments qui ne correspondent pas \xE0 l'entr\xE9e de clavier.","Contr\xF4le le style de navigation au clavier pour les listes et les arborescences dans le banc d'essai. Les options sont Simple, Mise en surbrillance et Filtrer.","Contr\xF4le si la navigation au clavier dans les listes et les arborescences est automatiquement d\xE9clench\xE9e simplement par la frappe. Si d\xE9fini sur 'false', la navigation au clavier est seulement d\xE9clench\xE9e avec l'ex\xE9cution de la commande 'list.toggleKeyboardNavigation', \xE0 laquelle vous pouvez attribuer un raccourci clavier.","Contr\xF4le la fa\xE7on dont les dossiers de l'arborescence sont d\xE9velopp\xE9s quand vous cliquez sur les noms de dossiers. Notez que certaines arborescences et listes peuvent choisir d'ignorer ce param\xE8tre, s'il est non applicable."],"vs/platform/markers/common/markers":["Erreur","Avertissement","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","r\xE9cemment utilis\xE9es","autres commandes","La commande '{0}' a entra\xEEn\xE9 une erreur ({1})"],"vs/platform/quickinput/browser/helpQuickAccess":["commandes globales","commandes de l'\xE9diteur","{0}, {1}"],"vs/platform/theme/common/colorRegistry":["Couleur de premier plan globale. Cette couleur est utilis\xE9e si elle n'est pas remplac\xE9e par un composant.","Couleur principale de premier plan pour les messages d'erreur. Cette couleur est utilis\xE9e uniquement si elle n'est pas red\xE9finie par un composant.","Couleur par d\xE9faut des ic\xF4nes du banc d'essai.","Couleur de bordure globale des \xE9l\xE9ments ayant le focus. Cette couleur est utilis\xE9e si elle n'est pas remplac\xE9e par un composant.","Bordure suppl\xE9mentaire autour des \xE9l\xE9ments pour les s\xE9parer des autres et obtenir un meilleur contraste.","Bordure suppl\xE9mentaire autour des \xE9l\xE9ments actifs pour les s\xE9parer des autres et obtenir un meilleur contraste.","Couleur des liens dans le texte.","Couleur d'arri\xE8re-plan des blocs de code dans le texte.","Couleur de l'ombre des widgets, comme rechercher/remplacer, au sein de l'\xE9diteur.","Arri\xE8re-plan de la zone d'entr\xE9e.","Premier plan de la zone d'entr\xE9e.","Bordure de la zone d'entr\xE9e.","Couleur de la bordure des options activ\xE9es dans les champs d'entr\xE9e.","Couleur d'arri\xE8re-plan des options activ\xE9es dans les champs d'entr\xE9e.","Couleur de premier plan des options activ\xE9es dans les champs d'entr\xE9e.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 des informations.","Couleur de premier plan de validation de saisie pour la s\xE9v\xE9rit\xE9 Information.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 des informations.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 de l'avertissement.","Couleur de premier plan de la validation de la saisie pour la s\xE9v\xE9rit\xE9 Avertissement.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 de l'avertissement.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 de l'erreur.","Couleur de premier plan de la validation de saisie pour la s\xE9v\xE9rit\xE9 Erreur.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 de l'erreur. ","Arri\xE8re-plan de la liste d\xE9roulante.","Premier plan de la liste d\xE9roulante.","Couleur de premier plan du bouton.","Couleur d'arri\xE8re-plan du bouton.","Couleur d'arri\xE8re-plan du bouton pendant le pointage.","Couleur de fond des badges. Les badges sont de courts libell\xE9s d'information, ex. le nombre de r\xE9sultats de recherche.","Couleur des badges. Les badges sont de courts libell\xE9s d'information, ex. le nombre de r\xE9sultats de recherche.","Ombre de la barre de d\xE9filement pour indiquer que la vue d\xE9file.","Couleur de fond du curseur de la barre de d\xE9filement.","Couleur de fond du curseur de la barre de d\xE9filement lors du survol.","Couleur d\u2019arri\xE8re-plan de la barre de d\xE9filement lorsqu'on clique dessus.","Couleur de fond pour la barre de progression qui peut s'afficher lors d'op\xE9rations longues.","Couleur d'arri\xE8re-plan du texte d'erreur dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les erreurs dans l'\xE9diteur.","Couleur de bordure des zones d'erreur dans l'\xE9diteur.","Couleur d'arri\xE8re-plan du texte d'avertissement dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les avertissements dans l'\xE9diteur.","Couleur de bordure des zones d'avertissement dans l'\xE9diteur.","Couleur d'arri\xE8re-plan du texte d'information dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les informations dans l'\xE9diteur.","Couleur de bordure des zones d'informations dans l'\xE9diteur.","Couleur de premier plan de la ligne ondul\xE9e d'indication dans l'\xE9diteur.","Couleur de bordure des zones d'indication dans l'\xE9diteur.","Couleur d'arri\xE8re-plan de l'\xE9diteur.","Couleur de premier plan par d\xE9faut de l'\xE9diteur.","Couleur d'arri\xE8re-plan des gadgets de l'\xE9diteur tels que rechercher/remplacer.","Couleur de premier plan des widgets de l'\xE9diteur, notamment Rechercher/remplacer.","Couleur de bordure des widgets de l'\xE9diteur. La couleur est utilis\xE9e uniquement si le widget choisit d'avoir une bordure et si la couleur n'est pas remplac\xE9e par un widget.","Couleur de bordure de la barre de redimensionnement des widgets de l'\xE9diteur. La couleur est utilis\xE9e uniquement si le widget choisit une bordure de redimensionnement et si la couleur n'est pas remplac\xE9e par un widget.","Couleur d'arri\xE8re-plan du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur de premier plan du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur d'arri\xE8re-plan du titre du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur d'arri\xE8re-plan du s\xE9lecteur rapide pour l'\xE9l\xE9ment ayant le focus.","Couleur du s\xE9lecteur rapide pour les \xE9tiquettes de regroupement.","Couleur du s\xE9lecteur rapide pour les bordures de regroupement.","Couleur de la s\xE9lection de l'\xE9diteur.","Couleur du texte s\xE9lectionn\xE9 pour le contraste \xE9lev\xE9.","Couleur de la s\xE9lection dans un \xE9diteur inactif. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur des r\xE9gions dont le contenu est le m\xEAme que celui de la s\xE9lection. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des r\xE9gions dont le contenu est identique \xE0 la s\xE9lection.","Couleur du r\xE9sultat de recherche actif.","Couleur des autres correspondances de recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de la plage limitant la recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure du r\xE9sultat de recherche actif.","Couleur de bordure des autres r\xE9sultats de recherche.","Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Surlignage sous le mot s\xE9lectionn\xE9 par pointage. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan du pointage de l'\xE9diteur.","Couleur de premier plan du pointage de l'\xE9diteur.","Couleur de bordure du pointage de l'\xE9diteur.","Couleur d'arri\xE8re-plan de la barre d'\xE9tat du pointage de l'\xE9diteur.","Couleur des liens actifs.","Couleur de premier plan des indicateurs inline","Couleur d'arri\xE8re-plan des indicateurs inline","Couleur utilis\xE9e pour l'ic\xF4ne d'ampoule sugg\xE9rant des actions.","Couleur utilis\xE9e pour l'ic\xF4ne d'ampoule sugg\xE9rant des actions de correction automatique.","Couleur d'arri\xE8re-plan du texte ins\xE9r\xE9. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan du texte supprim\xE9. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de contour du texte ins\xE9r\xE9.","Couleur de contour du texte supprim\xE9.","Couleur de bordure entre les deux \xE9diteurs de texte.","Couleur du remplissage diagonal de l'\xE9diteur de diff\xE9rences. Le remplissage diagonal est utilis\xE9 dans les vues de diff\xE9rences c\xF4te \xE0 c\xF4te.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de contour de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active a le focus clavier, contrairement \xE0 une liste/arborescence inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence de l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est inactive. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est inactive. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier (elle ne l'est pas quand elle est inactive).","Couleur de contour de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est inactive. Une liste/arborescence active a le focus clavier, contrairement \xE0 une liste/arborescence inactive.","Arri\xE8re-plan de la liste/l'arborescence pendant le pointage sur des \xE9l\xE9ments avec la souris.","Premier plan de la liste/l'arborescence pendant le pointage sur des \xE9l\xE9ments avec la souris.","Arri\xE8re-plan de l'op\xE9ration de glisser-d\xE9placer dans une liste/arborescence pendant le d\xE9placement d'\xE9l\xE9ments avec la souris.","Couleur de premier plan dans la liste/l'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.","Couleur d'arri\xE8re-plan du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l'absence de correspondance.","Couleur de trait de l'arborescence pour les rep\xE8res de mise en retrait.","Couleur de trait de l'arborescence pour les rep\xE8res de mise en retrait.","Couleur de bordure des menus.","Couleur de premier plan des \xE9l\xE9ments de menu.","Couleur d'arri\xE8re-plan des \xE9l\xE9ments de menu.","Couleur de premier plan de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur d'arri\xE8re-plan de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur de bordure de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur d'un \xE9l\xE9ment de menu s\xE9parateur dans les menus.","Couleur d\u2019arri\xE8re-plan de mise en surbrillance d\u2019un extrait tabstop.","Couleur de bordure de mise en surbrillance d\u2019un extrait tabstop.","Couleur d\u2019arri\xE8re-plan de mise en surbrillance du tabstop final d\u2019un extrait.","Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d'un extrait de code.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour rechercher les correspondances. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des s\xE9lections. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la minimap pour les correspondances.","Couleur de marqueur du minimap pour la s\xE9lection de l'\xE9diteur.","Couleur de marqueur de minimap pour les erreurs.","Couleur de marqueur de minimap pour les avertissements.","Couleur d'arri\xE8re-plan du minimap.","Couleur d'arri\xE8re-plan du curseur de minimap.","Couleur d'arri\xE8re-plan du curseur de minimap pendant le survol.","Couleur d'arri\xE8re-plan du curseur de minimap pendant un clic.","Couleur utilis\xE9e pour l'ic\xF4ne d'erreur des probl\xE8mes.","Couleur utilis\xE9e pour l'ic\xF4ne d'avertissement des probl\xE8mes.","Couleur utilis\xE9e pour l'ic\xF4ne d'informations des probl\xE8mes."],"vs/platform/theme/common/iconRegistry":["ID de la police \xE0 utiliser. Si aucune valeur n'est d\xE9finie, la police d\xE9finie en premier est utilis\xE9e.","Caract\xE8re de police associ\xE9 \xE0 la d\xE9finition d'ic\xF4ne.","Ic\xF4ne de l'action de fermeture dans les widgets."],"vs/platform/undoRedo/common/undoRedoService":["Les fichiers suivants ont \xE9t\xE9 ferm\xE9s et modifi\xE9s sur le disque\xA0: {0}.","Les fichiers suivants ont \xE9t\xE9 modifi\xE9s de mani\xE8re incompatible : {0}.","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers, car des modifications ont \xE9t\xE9 apport\xE9es \xE0 {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution sur {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement s'est produite dans l'intervalle","Souhaitez-vous annuler '{0}' dans tous les fichiers\xA0?","Annuler dans {0} fichiers","Annuler ce fichier","Annuler","Impossible d'annuler '{0}', car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution.","Voulez-vous annuler '{0}'\xA0?","Annuler","Annuler","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers. {1}","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers. {1}","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers, car des modifications ont \xE9t\xE9 apport\xE9es \xE0 {1}","Impossible de r\xE9tablir '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution pour {1}","Impossible de r\xE9tablir '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement s'est produite dans l'intervalle","Impossible de r\xE9tablir '{0}', car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution."]});
