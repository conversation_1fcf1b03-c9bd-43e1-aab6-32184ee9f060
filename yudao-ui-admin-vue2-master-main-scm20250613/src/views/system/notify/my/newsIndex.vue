<template>
  <div class="app-container">
    <!--    &lt;!&ndash; 操作工具栏 &ndash;&gt;-->
    <!--    <el-row :gutter="10" class="mb8">-->
    <!--      <el-col :span="1.5">-->
    <!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleUpdateList">标记已读</el-button>-->
    <!--      </el-col>-->
    <!--      <el-col :span="1.5">-->
    <!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleUpdateAll">全部已读</el-button>-->
    <!--      </el-col>-->
    <!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
    <!--    </el-row>-->

    <!-- 列表 -->
    <el-table v-loading="loading" ref="tables" :data="list">
      <!--      <el-table-column type="selection" width="55" />-->
      <!--      <el-table-column label="发送人" align="center" prop="templateNickname" width="100" />-->

      <!--      <el-table-column label="类型" align="center" prop="templateType" width="80">-->
      <!--        <template v-slot="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.templateType" />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column type="index" label="序号"/>
      <el-table-column label="内容" align="left" prop="templateContent" :show-overflow-tooltip="true"
                       min-width="550px"/>
      <el-table-column label="发送时间" align="center" prop="createTime" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="是否已读" align="center" prop="readStatus" width="80">-->
      <!--        <template v-slot="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.INFRA_YES_NO_READ" :value="scope.row.readStatus"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="70">-->
      <!--        <template v-slot="scope">-->
      <!--          <el-button v-show="!scope.row.readStatus" size="mini" type="text" icon="el-icon-check" @click="handleUpdateSingle(scope.row)">已读</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <a target="_blank" style="font-size: 14px" v-if="docShow === '1'" :href="docUrl">点击下载《操作手册》</a>
    <!--    <div slot="footer" class="dialog-footer">-->
    <!--      <el-button type="default" size="mini" @click="goMyList">更多>></el-button>-->
    <!--    </div>-->
    <!-- 分页组件 -->
    <!--    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"-->
    <!--                @pagination="getList"/>-->
  </div>
</template>

<script>
import {
  getMyNotifyMessagePage,
  getPublicNotifyMessagePage,
  updateAllNotifyMessageRead,
  updateNotifyMessageRead
} from "@/api/system/notify/message";
import {getConfigValue, getQRcode} from "@/api/login";

export default {
  name: "SystemMyNotifyNews",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的站内信列表
      list: [],
      docUrl: "",
      docShow: '0',
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 6,
        readStatus: null,
        templateType: "1",
        createTime: []
      },
    };
  },
  created() {
    getConfigValue("url.operation.documents").then((res) => {
      this.docUrl = process.env.VUE_APP_BASE_API + "/admin-api/" + res.data;
    });
    getConfigValue("visable.operation.documents").then((res) => {
      this.docShow = res.data;
    });
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getPublicNotifyMessagePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.handleQuery();
    },
    handleUpdateList() {
      let list = this.$refs["tables"].selection;
      if (list.length === 0) {
        return;
      }
      this.handleUpdate(list.map(v => v.id))
    },
    handleUpdateSingle(row) {
      this.handleUpdate([row.id])
    },
    handleUpdate(ids) {
      updateNotifyMessageRead(ids).then(response => {
        this.$modal.msgSuccess("标记已读成功！");
        this.getList();
      });
    },
    handleUpdateAll() {
      updateAllNotifyMessageRead().then(response => {
        this.$modal.msgSuccess("全部已读成功！");
        this.getList();
      });
    },
    goMyList: function () {
      this.$router.push({
        name: 'NoticeMessage'
      });
    }
  }
}
</script>

<style scoped>
a {
  color: #0097fb;
  text-decoration: underline;
}
</style>

