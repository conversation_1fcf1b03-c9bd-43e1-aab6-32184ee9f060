<template>
  <div class="app-container">
    <!--    &lt;!&ndash; 操作工具栏 &ndash;&gt;-->
    <!--    <el-row :gutter="10" class="mb8">-->
    <!--      <el-col :span="1.5">-->
    <!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleUpdateList">标记已读</el-button>-->
    <!--      </el-col>-->
    <!--      <el-col :span="1.5">-->
    <!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleUpdateAll">全部已读</el-button>-->
    <!--      </el-col>-->
    <!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
    <!--    </el-row>-->

    <!-- 列表 -->
    <el-table v-loading="loading" ref="tables" :data="list">
      <!--      <el-table-column type="selection" width="55" :show-header="false"/>-->
      <el-table-column type="index" label="序号"/>
      <el-table-column label="消息来源" align="center" prop="templateNickname" width="100"/>

      <!--      <el-table-column label="类型" align="center" prop="templateType" width="80">-->
      <!--        <template v-slot="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.templateType" />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="内容" align="left" prop="templateContent" :show-overflow-tooltip="true"
                       min-width="150px">
        <template slot-scope="scope">
          <el-button type="text" @click="openForm(scope.row)">
            <span>{{ scope.row.templateContent }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="发送时间" align="center" prop="createTime" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否已读" align="center" prop="readStatus" width="80">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.readStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="70" >
        <template v-slot="scope">
          <el-button v-show="!scope.row.readStatus" size="mini" type="text" icon="el-icon-check" style="padding: 2px 15px!important"
                     @click="handleUpdateSingle(scope.row)">已读
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="default" size="mini" @click="goMyList">更多>></el-button>
    </div>
    <!-- 分页组件 -->
    <!--    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"-->
    <!--                @pagination="getList"/>-->

  </div>
</template>

<script>
import {getMyNotifyMessagePage, updateAllNotifyMessageRead, updateNotifyMessageRead} from "@/api/system/notify/message";

export default {
  name: "SystemMyNotifyMessage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的站内信列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 5,
        readStatus: null,
        templateType: "2",
        createTime: []
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getMyNotifyMessagePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    openForm: function (row) {
      let url = row.url;
      let params = "";
      const map = new Map(Object.entries(row.templateParams));
      if (map) {
        for (const [key, value] of map) {
          params = params + "&" + key + "=" + value;
        }
      }
      if(url){
        if(url.includes("=")){
          url = url + params;
        }else {
          url = url + params.substr(1);
        }
        if (this.ishttp(url)) {
          // http(s):// 路径新窗口打开
          window.open(url, "_blank");
        } else {
          this.$router.push(url)
        }
      }
    },
    ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.handleQuery();
    },
    handleUpdateList() {
      let list = this.$refs["tables"].selection;
      if (list.length === 0) {
        return;
      }
      this.handleUpdate(list.map(v => v.id))
    },
    handleUpdateSingle(row) {
      this.handleUpdate([row.id])
    },
    handleUpdate(ids) {
      updateNotifyMessageRead(ids).then(response => {
        this.$modal.msgSuccess("标记已读成功！");
        this.getList();
      });
    },
    handleUpdateAll() {
      updateAllNotifyMessageRead().then(response => {
        this.$modal.msgSuccess("全部已读成功！");
        this.getList();
      });
    },
    goMyList: function () {
      this.$router.push({
        name: 'MyNotifyMessage'
      });
    }
  }
}
</script>
