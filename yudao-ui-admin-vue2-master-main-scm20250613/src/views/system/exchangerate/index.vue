<template>
  <div class="app-container">
    <div id="app" class="exchange-calendar">
      <!-- 货币选择标签 -->
      <div class="currency-tabs" v-if="queryParams.currencies.length > 0">
        <div class="currency-tab"
             v-for="currency in queryParams.currencies"
             :class="{active: activeCurrency === currency}"
             @click="activeCurrency = currency">
          {{ currency }}
        </div>
      </div>

      <!-- 日历区域 -->
      <div class="calendar-container" v-if="queryParams.currencies.length > 0">
        <div class="calendar-header">
          <button class="nav-button prev-month" @click="prevMonth">
            <span class="nav-icon">←</span>
            <span class="nav-text">上个月</span>
          </button>

          <!-- 月份筛选条件 -->
          <el-date-picker
            v-model="queryParams.yearMonth"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            @change="handleMonthChange"
            style="width: 150px; margin: 0 10px;"
          />

          <!-- 类型筛选条件 -->
          <el-select
            v-model="queryParams.type"
            placeholder="选择汇率类型"
            clearable
            style="width: 320px; margin: 0 10px;"
            @change="fetchData"
          >
            <el-option
              v-for="item in rateTypeList"
              :key="item.value"
              :label="item.value + ' ' + item.label"
              :value="item.value"
            />
          </el-select>

          <button class="nav-button next-month" @click="nextMonth">
            <span class="nav-text">下个月</span>
            <span class="nav-icon">→</span>
          </button>
        </div>

        <div class="calendar-month">
          <div class="weekdays">
            <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
          </div>
          <div class="days">
            <div
              v-for="(day, index) in calendarDates"
              :key="index"
              class="day"
              :class="{
              'current-month': day.isCurrentMonth,
              'today': isToday(day.date)
            }"
              @click="showEditModal(day.date)"
            >
              <div class="date-number">{{ day.date.getDate() }}</div>
              <div v-if="hasRateData(day.date)" class="rate-value">
                {{ getRateValue(day.date) }}
              </div>
              <div v-else class="no-data" v-show="day.isCurrentMonth">-</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑汇率模态框 - 改为按行编辑模式 -->
      <el-dialog :title="`编辑 ${selectedDate} 汇率`" :visible.sync="editModalVisible" width="1000px"
                 :close-on-click-modal="false">
        <el-table :data="editForm.rates" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column label="币别" prop="crcy" width="200">
            <template slot-scope="scope">
              <el-select v-model="scope.row.crcy" placeholder="请选择币别" :disabled="!!scope.row.id">
                <el-option v-for="dict in crcyTypeList" :key="dict.value" :label="dict.value + ' ' + dict.label"
                           :value="dict.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="类型" prop="type" width="350">
            <template slot-scope="scope">
              <el-select v-model="scope.row.type" placeholder="请选择类型" :disabled="!!scope.row.id" style="width: 100%">
                <el-option v-for="dict in rateTypeList" :key="dict.value" :label="dict.value + ' ' + dict.label"
                           :value="dict.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="汇率值" prop="rateValue">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.rateValue" :precision="6" :step="0.0001" :min="0"
                               controls-position="right" style="width: 100%;"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" circle size="mini"
                         @click="removeRate(scope.row, scope.$index)"/>
            </template>
          </el-table-column>
        </el-table>

        <!-- 添加新汇率的表单 -->
        <el-divider>添加新汇率</el-divider>
        <el-form :model="newRateForm" label-width="80px" inline class="add-rate-form">
          <el-form-item label="币别">
            <el-select v-model="newRateForm.crcy" placeholder="请选择币别" style="width: 200px;">
              <el-option
                v-for="dict in crcyTypeList"
                :key="dict.value"
                :label="dict.value + ' ' + dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="newRateForm.type" placeholder="请选择类型" style="width: 320px;">
              <el-option
                v-for="dict in rateTypeList"
                :key="dict.value"
                :label="dict.value + ' ' + dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="汇率值">
            <el-input-number
              v-model="newRateForm.rateValue"
              :precision="6"
              :step="0.0001"
              :min="0"
              controls-position="right"
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="confirmAddRate">添加</el-button>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="editModalVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRates" :loading="saving">保存</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import * as ExchangeRateApi from '@/api/system/exchangerate';
import {getCurrentYearMon} from "@/utils/dateUtils";
import {DICT_TYPE, getDictDatas} from "@/utils/dict";

export default {
  name: "ExchangeRate",
  data() {
    return {
      // 查询参数
      queryParams: {
        type: null,
        currencies: [],
        yearMonth: getCurrentYearMon(),
      },
      activeCurrency: '',
      rateData: {},
      editModalVisible: false,
      saving: false,
      selectedDate: '',
      editForm: {
        rates: []
      },
      // 修改newRateForm结构
      newRateForm: {
        crcy: '',
        type: '',
        rateValue: null
      },
      // 移除 showAddForm 变量
      calendarDates: [],
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      currentDate: new Date(),
      showModal: false,
      detailData: [],
      rateTypeList: [],
      crcyTypeList: [],
      isInitialized: false,
    };
  },
  mounted() {
    this.rateTypeList = getDictDatas(DICT_TYPE.SYSTEM_RATE_TYPE);
    this.crcyTypeList = getDictDatas(DICT_TYPE.CURRENCY_TYPE);

    // 初始化货币列表
    if (this.crcyTypeList.length > 0) {
      this.queryParams.currencies = this.crcyTypeList.map(item => item.value);
      this.activeCurrency = this.queryParams.currencies[0] || '';
    } else {
      // 如果没有字典数据，使用默认值
      this.queryParams.currencies = ['USD'];
      this.activeCurrency = 'USD';
    }

    // 初始化汇率类型
    if (this.rateTypeList.length > 0) {
      this.queryParams.type = this.rateTypeList[0].value;
    }

    // 标记初始化完成
    this.isInitialized = true;

    // 初始化完成后获取数据
    this.fetchData();
  },
  watch: {
    'queryParams.yearMonth': function (newVal) {
      if (newVal) {
        const [year, month] = newVal.split('-').map(Number);
        this.currentDate = new Date(year, month - 1, 1);
        this.generateCalendar();
      }
    },
    'queryParams.currencies': function (newVal) {
      if (newVal.length > 0) {
        this.activeCurrency = newVal[0];
      }
    }
  },
  created() {
    this.generateCalendar();
  },
  methods: {
    // 保存汇率
    async saveRates() {
      this.saving = true;
      try {
        // 只处理仍然存在于editForm.rates中的记录
        const updatePromises = this.editForm.rates
          .filter(rate => rate.id)
          .map(rate => ExchangeRateApi.updateExchangeRate(rate));

        const createPromises = this.editForm.rates
          .filter(rate => !rate.id)
          .map(rate => ExchangeRateApi.createExchangeRate(rate));

        await Promise.all([...updatePromises, ...createPromises]);

        this.$message.success('保存成功');
        this.editModalVisible = false;
        this.fetchData(); // 刷新日历数据
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.saving = false;
      }
    },
    // 删除汇率
    async removeRate(rate, index) {
      try {
        // 如果有ID，说明是已存在的记录，需要调用API删除
        if (rate.id) {
          await ExchangeRateApi.deleteExchangeRate(rate.id);
          this.$message.success('删除成功');
        }
        // 从前端移除
        this.editForm.rates.splice(index, 1);

      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      }
    },
    // 显示编辑模态框 - 简化，移除 showAddForm 相关逻辑
    async showEditModal(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      this.selectedDate = `${year}-${month}-${day}`;

      // 重置添加表单
      this.resetNewRateForm();

      try {
        // 获取该日期的所有汇率数据
        const params = {
          date: this.selectedDate,
          currencies: this.queryParams.currencies,
          type: this.queryParams.type
        };
        const res = await ExchangeRateApi.getExchangeRateDetails(params);

        if (res.code === 0) {
          this.editForm.rates = res.data.map(item => ({
            id: item.id,
            crcy: item.crcy,
            type: item.type,
            rateValue: item.rateValue,
            rateDate: this.selectedDate
          }));
        } else {
          // 如果没有数据，初始化空数组
          this.editForm.rates = [];
        }
      } catch (error) {
        console.error('获取汇率数据失败:', error);
        this.editForm.rates = [];
      }

      this.editModalVisible = true;
    },

    // 重置新汇率表单
    resetNewRateForm() {
      this.newRateForm = {
        crcy: this.activeCurrency,
        type: this.queryParams.type || '',
        rateValue: null
      };
    },

    // 确认添加汇率 - 简化，移除取消添加的逻辑
    confirmAddRate() {
      if (!this.newRateForm.crcy || !this.newRateForm.type || this.newRateForm.rateValue === null) {
        this.$message.warning('请填写完整的汇率信息');
        return;
      }

      // 检查是否已存在相同的货币和类型
      const exists = this.editForm.rates.some(rate =>
        rate.crcy === this.newRateForm.crcy && rate.type === this.newRateForm.type
      );

      if (exists) {
        this.$message.warning('该货币和类型的汇率已存在');
        return;
      }

      this.editForm.rates.push({
        id: null,
        crcy: this.newRateForm.crcy,
        type: this.newRateForm.type,
        rateValue: this.newRateForm.rateValue,
        rateDate: this.selectedDate
      });

      this.resetNewRateForm();
      this.$message.success('添加成功');
    },
    handleMonthChange() {
      if (this.queryParams.yearMonth) {
        const [year, month] = this.queryParams.yearMonth.split('-').map(Number);
        this.currentDate = new Date(year, month - 1, 1);
        this.generateCalendar();
        this.fetchData();
      }
    },
    generateCalendar() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()

      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)

      const days = []

      // 上个月的最后几天
      const prevMonthDays = firstDay.getDay()
      for (let i = prevMonthDays - 1; i >= 0; i--) {
        const date = new Date(year, month, -i)
        days.push({
          date,
          isCurrentMonth: false
        })
      }

      // 当月日期
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const date = new Date(year, month, i)
        days.push({
          date,
          isCurrentMonth: true
        })
      }

      // 下个月的前几天
      const nextMonthDays = 42 - days.length // 6行x7天
      for (let i = 1; i <= nextMonthDays; i++) {
        const date = new Date(year, month + 1, i)
        days.push({
          date,
          isCurrentMonth: false
        })
      }

      this.calendarDates = days;
    },
    async fetchData() {
      // 添加初始化状态检查
      if (!this.isInitialized) {
        return;
      }

      if (!this.queryParams.yearMonth || this.queryParams.currencies.length === 0) {
        // 只有在用户主动操作时才显示警告，而不是初始化时
        if (this.isInitialized) {
          this.$message.warning('请选择年月和至少一种货币');
        }
        return;
      }

      try {
        // 构建API请求参数
        const params = {
          yearMonth: this.queryParams.yearMonth,
          type: this.queryParams.type,
          currencies: this.queryParams.currencies.join(',')
        };

        // 调用API获取数据
        const response = await ExchangeRateApi.getExchangeRatesByMonth(params);
        if (response.code === 0) {
          this.rateData = this.assembleRateData(response.data);
        } else {
          this.$message.error('获取汇率数据失败');
        }
      } catch (error) {
        console.error('获取汇率数据失败:', error);
        this.$message.error('获取汇率数据失败');
      }
    },
    // 组装汇率数据到前端需要的格式
    assembleRateData(apiData) {
      const assembledData = {};

      // 初始化每个货币的空对象
      this.queryParams.currencies.forEach(currency => {
        assembledData[currency] = {};
      });

      // 填充数据
      apiData.forEach(item => {
        const day = new Date(item.rateDate).getDate();
        if (assembledData[item.crcy]) {
          assembledData[item.crcy][day] = item.rateValue;
        }
      });

      return assembledData;
    },
    prevMonth() {
      const newDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() - 1,
        1
      );
      this.currentDate = newDate;
      this.queryParams.yearMonth = `${newDate.getFullYear()}-${(newDate.getMonth() + 1).toString().padStart(2, '0')}`;
      this.generateCalendar();
      this.fetchData();
    },
    nextMonth() {
      const newDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() + 1,
        1
      );
      this.currentDate = newDate;
      this.queryParams.yearMonth = `${newDate.getFullYear()}-${(newDate.getMonth() + 1).toString().padStart(2, '0')}`;
      this.generateCalendar();
      this.fetchData();
    },
    hasRateData(date) {
      if (!date || !this.rateData[this.activeCurrency]) return false;

      const day = date.getDate();
      return this.rateData[this.activeCurrency][day] !== undefined;
    },
    getRateValue(date) {
      if (!this.hasRateData(date)) return null;

      const day = date.getDate();
      return this.rateData[this.activeCurrency][day];
    },
    isToday(date) {
      const today = new Date();
      return date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear();
    },
    async showDateDetails(date) {
      if (!this.hasRateData(date)) return;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      this.selectedDate = `${year}-${month}-${day}`;
      try {
        const params = {
          date: this.selectedDate,
          currencies: this.queryParams.currencies,
          type: this.queryParams.type
        };
        const res = await ExchangeRateApi.getExchangeRateDetails(params);
        if (res.code === 0) {
          this.detailData = res.data;
        } else {
          this.$message.error('获取详细汇率数据失败');
        }
      } catch (error) {
        console.error('获取详细汇率数据失败:', error);
      }
      this.showModal = true;
    },
  },
};
</script>

<style scoped>
.exchange-calendar {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  max-width: 1400px;
  margin: 20px auto;
  padding: 0 15px;
}

.filter-section {
  background: #fff;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  border: 1px solid #e8e8e8;
}

.calendar-container {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.rate-value {
  font-weight: 500;
  font-size: 13px;
}

.currency-tabs {
  margin-bottom: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.currency-tab {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 4px 4px 0 0;
}

.currency-tab.active {
  background: #1890ff;
  color: white;
}

.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background: white;
  border-radius: 6px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.detail-close {
  font-size: 20px;
  cursor: pointer;
}

.detail-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.no-data {
  color: #bfbfbf;
  font-style: italic;
}

.filter-row {
  margin-bottom: 15px;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.current-month {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.nav-button {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.weekdays {
  display: flex;
  background: #f5f5f5;
}

.weekday {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.days {
  display: flex;
  flex-wrap: wrap;
}

.day {
  flex: 0 0 calc(100% / 7);
  min-height: 80px;
  padding: 4px;
  border: 1px solid #f0f0f0;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  display: flex;
  flex-direction: column;
}

.day.current-month {
  background: white;
}

.day:not(.current-month) {
  background: #fafafa;
  color: #bfbfbf;
}

.day:hover {
  background: #f5f9ff;
  z-index: 1;
}

.date-number {
  font-size: 12px;
  font-weight: 500;
  text-align: right;
  margin-bottom: 2px;
}

.today {
  background-color: #e6f7ff !important;
}

.today .date-number {
  color: #1890ff;
  font-weight: bold;
}

.button-group {
  text-align: center;
  margin-top: 15px;
}

@media (max-width: 768px) {
  .day {
    min-height: 60px;
  }

  .rate-value {
    font-size: 11px;
  }

  .filter-row {
    flex-direction: column;
  }

  .col-md-4 {
    margin-bottom: 15px;
    width: 100%;
  }

  .add-rate-form {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed #ddd;
  }

  .add-rate-form .el-form-item {
    margin-bottom: 15px;
  }
}

.add-rate-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.add-rate-form >>> .el-form-item {
  margin-bottom: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .add-rate-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .add-rate-form >>> .el-form-item {
    width: 100%;
  }
}
</style>
