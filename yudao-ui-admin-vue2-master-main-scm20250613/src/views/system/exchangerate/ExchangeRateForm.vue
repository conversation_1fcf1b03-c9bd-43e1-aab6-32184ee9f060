<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="汇率日期" prop="rateDate">
          <el-date-picker clearable v-model="formData.rateDate" type="date" value-format="yyyy-MM-dd"
                          placeholder="选择汇率日期"/>
        </el-form-item>
        <el-form-item label="汇率类别" prop="type">
          <el-select v-model="formData.type" placeholder="请选择汇率类别">
            <el-option label="请选择字典生成" value=""/>
          </el-select>
        </el-form-item>
        <el-form-item label="货币" prop="crcy">
          <el-select v-model="formData.crcy" placeholder="请选择货币">
            <el-option label="请选择字典生成" value=""/>
          </el-select>
        </el-form-item>
        <el-form-item label="利率/汇率值" prop="rateValue">
          <el-input v-model="formData.rateValue" placeholder="请输入利率/汇率值"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as ExchangeRateApi from '@/api/system/exchangerate';

export default {
  name: "ExchangeRateForm",
  components: {},
  data() {
    return {
      // 弹出层标题
      dialogTitle: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
      formLoading: false,
      // 表单参数
      formData: {
        id: undefined,
        rateDate: undefined,
        type: undefined,
        crcy: undefined,
        rateValue: undefined,
      },
      // 表单校验
      formRules: {
        rateDate: [{required: true, message: '汇率日期不能为空', trigger: 'blur'}],
        type: [{required: true, message: '汇率类别代码不能为空', trigger: 'change'}],
        crcy: [{required: true, message: '货币代码（关联字典DICT_CRCY）不能为空', trigger: 'change'}],
        rateValue: [{required: true, message: '利率/汇率值不能为空', trigger: 'blur'}],
      },
    };
  },
  methods: {
    /** 打开弹窗 */
    async open(id) {
      this.dialogVisible = true;
      this.reset();
      // 修改时，设置数据
      if (id) {
        this.formLoading = true;
        try {
          const res = await ExchangeRateApi.getExchangeRate(id);
          this.formData = res.data;
          this.title = "修改汇率";
        } finally {
          this.formLoading = false;
        }
      }
      this.title = "新增汇率";
    },
    /** 提交按钮 */
    async submitForm() {
      // 校验主表
      await this.$refs["formRef"].validate();
      this.formLoading = true;
      try {
        const data = this.formData;
        // 修改的提交
        if (data.id) {
          await ExchangeRateApi.updateExchangeRate(data);
          this.$modal.msgSuccess("修改成功");
          this.dialogVisible = false;
          this.$emit('success');
          return;
        }
        // 添加的提交
        await ExchangeRateApi.createExchangeRate(data);
        this.$modal.msgSuccess("新增成功");
        this.dialogVisible = false;
        this.$emit('success');
      } finally {
        this.formLoading = false;
      }
    },
    /** 表单重置 */
    reset() {
      this.formData = {
        id: undefined,
        rateDate: undefined,
        type: undefined,
        crcy: undefined,
        rateValue: undefined,
      };
      this.resetForm("formRef");
    }
  }
};
</script>
