<template>
  <!-- 客户基本信息 详情、编辑页面 -->
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-row>
          <el-col :span="8">
          <el-form-item label="客户编号" prop="custNo">
            <el-input v-model="formData.custNo" :disabled="true" style="width: 180px" />
          </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="客户单位" prop="supplierName">
             <el-input v-model="formData.supplierName" :disabled="true" style="width: 180px" />
          </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="客户等级" prop="custLevel">
            <el-select v-model="formData.custLevel" placeholder="请选择客户等级" :disabled="viewFlag" style="width: 180px" >
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUSTOM_LEVEL)"
                             :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户状态" prop="custState">
              <el-select v-model="formData.custState" placeholder="请选择客户状态" :disabled="viewFlag" style="width: 180px" >
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUST_STATE)"
                           :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="联系人" prop="contactName">
           <el-input v-model="formData.contactName" :disabled="true" style="width: 180px"/>
          </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="联系电话" prop="contactMobile">
            <el-input v-model="formData.contactMobile" :disabled="true" style="width: 180px"/>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入邮箱" :disabled="viewFlag" style="width: 180px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="微信" prop="wchat">
            <el-input v-model="formData.wchat" placeholder="请输入微信" :disabled="viewFlag" style="width: 180px"/>
          </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="QQ" prop="qq">
            <el-input v-model="formData.qq" placeholder="请输入QQ" :disabled="viewFlag" style="width: 180px" />
          </el-form-item>
          </el-col>
        </el-row>
          <el-form-item label="网址" prop="url">
            <el-input v-model="formData.url" placeholder="请输入网址" :disabled="viewFlag" style="width: 540px"/>
          </el-form-item>
        <el-form-item label="客户评价" prop="custEvaluate">
          <el-input v-model="formData.custEvaluate" placeholder="请输入客户评价" :disabled="viewFlag" />
        </el-form-item>
        <el-form-item label="联系地址" prop="contactAddress">
            <el-input v-model="formData.contactAddress" :disabled="true" />
        </el-form-item>
        <el-form-item label="公司简介" prop="companyProfile">
          <el-input v-model="formData.companyProfile" :disabled="true" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
           <el-input v-model="formData.remark" placeholder="请输入备注" :disabled="viewFlag" />
        </el-form-item>
        <el-form-item label="交易产品种类" prop="productSpecies">
          <el-input v-model="formData.productSpecies" :disabled="viewFlag" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="销售负责人" prop="salesman">
              <el-input v-model="formData.salesman" style="width: 180px" :disabled="viewFlag"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人电话" prop="salesmanPhoneno">
              <el-input v-model="formData.salesmanPhoneno" style="width: 180px" :disabled="viewFlag"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件" prop="annex">
          <FileUpload v-model="formData.annex" :readonlyFlag="viewFlag"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading" v-show="!viewFlag">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as CustBaseInfoApi from '@/api/cust/custbaseinfo';
  import FileUpload from '@/components/FileUpload';

      export default {
    name: "EvaluateForm",
    components: {
          FileUpload,
                    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "客戶基本信息",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        viewFlag: true, //是否灰显
        //是否修改
        ismodeify:false,
        // 客户基本信息列表
        list: [],
        // 表单参数
        formData: {
          id: undefined,
          custNo: undefined,
          supplierName: undefined,
          custLevel: undefined,
          custState: undefined,
          custEvaluate: undefined,
          contactName: undefined,
          contactMobile: undefined,
          contactAddress: undefined,
          companyProfile: undefined,
          email: undefined,
          wchat: undefined,
          qq: undefined,
          url: undefined,
          remark: undefined,
          productSpecies: undefined,
          salesman: undefined,
          salesmanPhoneno: undefined,
          annex: undefined,
        },
        // 表单校验
        formRules: {
                        custNo: [{ required: true, message: '客户编号不能为空', trigger: 'change' }],
                        custLevel: [{ required: true, message: '客户等级不能为空', trigger: 'change' }],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
     async open(custNo,type,list) {
        this.dialogVisible = true;
        this.reset();
        this.list = list;
        this.ismodeify = true;
        if(type == 'view'){
          this.viewFlag = true;
          this.dialogTitle = "查看客戶基本信息";
        }else if(type == 'edit'){
          this.viewFlag = false;
          this.dialogTitle = "修改客戶基本信息";
        }
        // 修改时，设置数据
        this.formLoading = true;
        try {
          const res = await CustBaseInfoApi.getCustBaseInfo(custNo);
          this.formData = res.data;
        } finally {
          this.formLoading = false;
        }
      },

      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await CustBaseInfoApi.updateCustBaseInfo(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
          } else {
            // 添加的提交
            await CustBaseInfoApi.createCustBaseInfo(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
          }
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          custNo: undefined,
          supplierName: undefined,
          custLevel: undefined,
          custState: undefined,
          custEvaluate: undefined,
          contactName: undefined,
          contactMobile: undefined,
          contactAddress: undefined,
          companyProfile: undefined,
          email: undefined,
          wchat: undefined,
          qq: undefined,
          url: undefined,
          remark: undefined,
          productSpecies: undefined,
          salesman: undefined,
          salesmanPhoneno: undefined,
          annex: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
