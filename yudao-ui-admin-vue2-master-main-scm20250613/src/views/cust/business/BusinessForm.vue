<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-row>
          <el-col :span="8">
                    <el-form-item label="商机名称" prop="businessName">
                      <el-input v-model="formData.businessName" placeholder="请输入商机名称"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="商机状态" prop="businessState">
                      <el-select v-model="formData.businessState" placeholder="请选择商机状态" style="width: 220px">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BUSINESS_STATE)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="销售负责人" prop="salesman">
                      <el-input v-model="formData.salesman" placeholder="请输入销售负责人"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="销售负责人电话" prop="salesmanPhoneno" label-width="130px">
                      <el-input v-model="formData.salesmanPhoneno" placeholder="请输入销售负责人电话" style="width: 190px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="了解途径" prop="awareWay">
                      <el-input v-model="formData.awareWay" placeholder="请输入了解途径" style="width: 220px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="介绍人" prop="awarePerson">
                      <el-input v-model="formData.awarePerson" placeholder="请输入介绍人" style="width: 220px" />
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="客户姓名" prop="custName">
                      <el-input v-model="formData.custName" placeholder="请输入客户姓名"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="客户性别" prop="custSex">
                      <el-select v-model="formData.custSex" placeholder="请选择客户性别" style="width: 220px">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="联系电话" prop="phoneNo">
                      <el-input v-model="formData.phoneNo" placeholder="请输入联系电话" style="width: 220px" />
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="formData.email" placeholder="请输入邮箱"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="微信" prop="wchat">
                      <el-input v-model="formData.wchat" placeholder="请输入微信" style="width: 220px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="QQ" prop="qq">
                      <el-input v-model="formData.qq" placeholder="请输入QQ" style="width: 220px" />
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="公司名称" prop="comp">
                      <el-input v-model="formData.comp" placeholder="请输入公司名称"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="公司简介" prop="compIntroduce">
                      <el-input v-model="formData.compIntroduce" placeholder="请输入公司简介"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="任职" prop="post">
                      <el-input v-model="formData.post" placeholder="请输入任职"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
                    <el-form-item label="地址" prop="adress">
                      <el-input v-model="formData.adress" placeholder="请输入地址"  style="width: 415px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="12">
                    <el-form-item label="网址" prop="url">
                      <el-input v-model="formData.url" placeholder="请输入网址"  style="width: 415px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="产品名称" prop="products">
                      <el-input v-model="formData.products" placeholder="请输入产品名称" style="width: 220px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="数量" prop="num">
                      <el-input v-model="formData.num" placeholder="请输入数量"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="单价" prop="price">
                      <el-input v-model="formData.price" placeholder="请输入单价" style="width: 220px" />
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="商机金额" prop="businessAmount">
                      <el-input v-model="formData.businessAmount" placeholder="请输入商机金额" style="width: 220px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="最后联系时间" prop="lastContactTime" label-width="130px">
                      <el-date-picker clearable v-model="formData.lastContactTime" type="date" value-format="timestamp" placeholder="选择最后联系时间" style="width: 190px" />
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="下次联系时间" prop="nextContactTime" label-width="130px">
                      <el-date-picker clearable v-model="formData.nextContactTime" type="date" value-format="timestamp" placeholder="选择下次联系时间" style="width: 190px"/>
                    </el-form-item>
          </el-col>
        </el-row>
                    <el-form-item label="跟进内容">
                      <Editor v-model="formData.businessContent" :min-height="192" style="width: 1000px"/>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注"  style="width: 1000px"/>
                    </el-form-item>
                    <el-form-item label="附件" prop="annex">
                      <FileUpload v-model="formData.annex" :readonlyFlag="true"/>
                    </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as BusinessApi from '@/api/cust/business';
  import Editor from '@/components/Editor';
  import FileUpload from '@/components/FileUpload';

      export default {
    name: "BusinessForm",
    components: {
          Editor,
      FileUpload
    },
    data() {
      return {
        dialogTitle: "", // 弹出层标题
        dialogVisible: false, // 是否显示弹出层
        formLoading: false,  // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        // 表单参数
        formData: {
                            id: undefined,
                            businessName: undefined,
                            businessState: undefined,
                            businessContent: undefined,
                            custName: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            awareWay: undefined,
                            awarePerson: undefined,
                            comp: undefined,
                            compIntroduce: undefined,
                            post: undefined,
                            adress: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            products: undefined,
                            num: undefined,
                            price: undefined,
                            businessAmount: undefined,
                            lastContactTime: undefined,
                            nextContactTime: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        },
        // 表单校验
        formRules: {
                        businessName: [{ required: true, message: '商机名称不能为空', trigger: 'blur' }],
                        businessState: [{ required: true, message: '商机状态不能为空', trigger: 'change' }],
                        custName: [{ required: true, message: '客户姓名不能为空', trigger: 'blur' }],
                        custSex: [{ required: true, message: '客户性别不能为空', trigger: 'change' }],
                        phoneNo: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
                        comp: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
                        adress: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
                        products: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
                        lastContactTime: [{ required: true, message: '最后联系时间不能为空', trigger: 'blur' }],
                        nextContactTime: [{ required: true, message: '下次联系时间不能为空', trigger: 'blur' }],
                        salesman: [{ required: true, message: '销售负责人不能为空', trigger: 'blur' }],
                        salesmanPhoneno: [{ required: true, message: '销售负责人电话不能为空', trigger: 'blur' }],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await BusinessApi.getBusiness(id);
            this.formData = res.data;
            this.dialogTitle =  "修改商机";
          } finally {
            this.formLoading = false;
          }
        } else {
          this.dialogTitle =  "新增商机";
        }
              },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await BusinessApi.updateBusiness(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          data.businessState = '1';  //新增的商机只能是已录入
          await BusinessApi.createBusiness(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            businessName: undefined,
                            businessState: undefined,
                            businessContent: undefined,
                            custName: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            awareWay: undefined,
                            awarePerson: undefined,
                            comp: undefined,
                            compIntroduce: undefined,
                            post: undefined,
                            adress: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            products: undefined,
                            num: undefined,
                            price: undefined,
                            businessAmount: undefined,
                            lastContactTime: undefined,
                            nextContactTime: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
