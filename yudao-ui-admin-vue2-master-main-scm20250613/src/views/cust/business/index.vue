<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户姓名" prop="custName">
        <el-input v-model="queryParams.custName" placeholder="请输入客户姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="客户性别" prop="custSex" v-show="showField">
        <el-select v-model="queryParams.custSex" placeholder="请选择客户性别" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="商机名称" prop="businessName">
        <el-input v-model="queryParams.businessName" placeholder="请输入商机名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="商机状态" prop="businessState">
        <el-select v-model="queryParams.businessState" placeholder="请选择商机状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BUSINESS_STATE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="销售负责人" prop="salesman" v-show="showField" label-width="100px">
        <el-input v-model="queryParams.salesman" placeholder="请输入销售负责人" style="width: 160px" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="了解途径" prop="awareWay" v-show="showField">
        <el-input v-model="queryParams.awareWay" placeholder="请输入了解途径" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="介绍人" prop="awarePerson" v-show="showField">
        <el-input v-model="queryParams.awarePerson" placeholder="请输入介绍人" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="公司名称" prop="comp" v-show="showField">
        <el-input v-model="queryParams.comp" placeholder="请输入公司名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="地址" prop="adress" v-show="showField">
        <el-input v-model="queryParams.adress" placeholder="请输入地址" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="产品名称" prop="products" v-show="showField">
        <el-input v-model="queryParams.products" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark" v-show="showField">
        <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="最后联系时间" prop="lastContactTime" v-show="showField" label-width="100px">
        <el-date-picker v-model="queryParams.lastContactTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="下次联系时间" prop="nextContactTime" v-show="showField" label-width="100px">
        <el-date-picker v-model="queryParams.nextContactTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="新增时间" prop="createTime" v-show="showField">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button :icon="termIcon" @click="changeShowField">条件栏</el-button>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['cust:business:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['cust:business:export']">导出</el-button>
      </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" prop="id" />
      <el-table-column label="商机名称" align="center" prop="businessName"  width="120"/>
      <el-table-column label="商机状态" align="center" prop="businessState"  width="120">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.BUSINESS_STATE" :value="scope.row.businessState" />
        </template>
      </el-table-column>
      <el-table-column label="客户姓名" align="center" prop="custName" width="120"/>
      <el-table-column label="客户性别" align="center" prop="custSex" width="120">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.custSex" />
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phoneNo"  width="120"/>
      <el-table-column label="产品名称" align="center" prop="products"  width="200"/>
      <el-table-column label="数量" align="center" prop="num"  width="120"/>
      <el-table-column label="单价" align="center" prop="price" width="120" />
      <el-table-column label="商机金额" align="center" prop="businessAmount"  width="120"/>
      <el-table-column label="跟进内容" align="center" prop="businessContent" width="200"/>
      <el-table-column label="公司名称" align="center" prop="comp"  width="120"/>
      <el-table-column label="公司简介" align="center" prop="compIntroduce"  width="200"/>
      <el-table-column label="任职" align="center" prop="post"  width="120"/>
      <el-table-column label="地址" align="center" prop="adress"  width="200"/>
      <el-table-column label="邮箱" align="center" prop="email"  width="120"/>
      <el-table-column label="微信" align="center" prop="wchat"  width="120"/>
      <el-table-column label="QQ" align="center" prop="qq"  width="120"/>
      <el-table-column label="网址" align="center" prop="url" width="200"/>
      <el-table-column label="最后联系时间" align="center" prop="lastContactTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.lastContactTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下次联系时间" align="center" prop="nextContactTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.nextContactTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售负责人" align="center" prop="salesman"  width="120"/>
      <el-table-column label="销售负责人电话" align="center" prop="salesmanPhoneno" width="120" />
      <el-table-column label="备注" align="center" prop="remark" width="200" />
      <el-table-column label="了解途径" align="center" prop="awareWay"  width="200"/>
      <el-table-column label="介绍人" align="center" prop="awarePerson"  width="120"/>
      <el-table-column label="新增时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150px" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id)"
                     v-hasPermi="['cust:business:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['cust:business:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <BusinessForm ref="formRef" @success="getList" />
    </div>
</template>

<script>
import * as BusinessApi from '@/api/cust/business';
import BusinessForm from './BusinessForm.vue';
export default {
  name: "Business",
  components: {
          BusinessForm,
  },
  data() {
    return {
      loading: true,      // 遮罩层
      exportLoading: false,      // 导出遮罩层
      showSearch: true,      // 显示搜索条件
      showField: false, //显示多余的搜索条件
      termIcon: "el-icon-d-arrow-right", //条件栏样式
      total: 0,   // 总条数
      list: [],      // 商机列表
      isExpandAll: true,      // 是否展开，默认全部展开
      refreshTable: true,      // 重新渲染表格状态
      currentRow: {},      // 选中行
      // 查询参数
      queryParams: {
                    pageNo: 1,
            pageSize: 10,
        businessName: null,
        businessState: null,
        businessContent: null,
        custName: null,
        custSex: null,
        awareWay: null,
        awarePerson: null,
        comp: null,
        adress: null,
        products: null,
        lastContactTime: [],
        nextContactTime: [],
        salesman: null,
        remark: null,
        createTime: [],
      },
            };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
      this.loading = true;
              const res = await BusinessApi.getBusinessPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 点击条件栏操作 */
    changeShowField() {
      this.showField = !this.showField;
      if (this.showField) {
        this.termIcon = "el-icon-d-arrow-left";
      } else {
        this.termIcon = "el-icon-d-arrow-right";
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id) {
      this.$refs["formRef"].open(id);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除商机编号为"' + id + '"的数据项?')
      try {
       await BusinessApi.deleteBusiness(id);
       await this.getList();
       this.$modal.msgSuccess("删除成功");
      } catch {}
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有商机数据项?');
      try {
        this.exportLoading = true;
        const data = await BusinessApi.exportBusinessExcel(this.queryParams);
        this.$download.excel(data, '商机.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
              }
};
</script>
