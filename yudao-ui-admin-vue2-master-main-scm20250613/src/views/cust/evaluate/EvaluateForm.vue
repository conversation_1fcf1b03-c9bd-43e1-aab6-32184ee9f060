<template>
  <!-- 客商评价 详情、编辑页面 -->
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
          <el-form-item label="客户编号" prop="custNo">
            <el-input v-model="formData.custNo"  style="width: 206px" :disabled="true" />
          </el-form-item>
          <el-form-item label="客户等级" prop="custLevel">
            <el-select v-model="formData.custLevel" placeholder="请选择客户等级" :disabled="viewFlag" >
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUSTOM_LEVEL)"
                             :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="客户状态" prop="custState">
            <el-select v-model="formData.custState" placeholder="请选择客户状态" :disabled="viewFlag" >
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUST_STATE)"
                         :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="客户评价" prop="custEvaluate">
            <el-input v-model="formData.custEvaluate" placeholder="请输入客户评价" :disabled="viewFlag" />
          </el-form-item>
          <el-form-item label="附件" prop="annex">
            <FileUpload v-model="formData.annex" :readonlyFlag="true"/>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading" v-show="!viewFlag">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as EvaluateApi from '@/api/cust/evaluate';
  import FileUpload from '@/components/FileUpload';

      export default {
    name: "EvaluateForm",
    components: {
          FileUpload,
                    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "新增客商管理评价",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        viewFlag: true, //是否灰显
        //是否修改
        ismodeify:false,
        // 客商评价列表
        list: [],
        // 表单参数
        formData: {
                            id: undefined,
                            custNo: undefined,
                            custLevel: undefined,
                            custState: undefined,
                            custEvaluate: undefined,
                            annex: undefined,
        },
        // 表单校验
        formRules: {
                        custNo: [{ required: true, message: '客户编号不能为空', trigger: 'change' }],
        },
                        };
    },
    methods: {
      /** 打开弹窗 */
     async open(id,custNo,type,list) {
        this.dialogVisible = true;
        this.reset();
        this.list = list;
        this.ismodeify = true;
        if(type == 'view'){
          this.viewFlag = true;
          this.dialogTitle = "查看客商评价";
        }else{
          this.viewFlag = false;
          if(type == 'add'){
            this.dialogTitle = "新增客商评价";
            this.ismodeify = false;
          }else{
            this.dialogTitle = "修改客商评价";
          }
        }
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await EvaluateApi.getEvaluate(id);
            this.formData = res.data;
          } finally {
            this.formLoading = false;
          }
        }
        this.formData.custNo = custNo;
      },

      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await EvaluateApi.updateEvaluate(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
          } else {
            // 添加的提交
            await EvaluateApi.createEvaluate(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
          }
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            custNo: undefined,
                            custLevel: undefined,
                            custState: undefined,
                            custEvaluate: undefined,
                            annex: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
