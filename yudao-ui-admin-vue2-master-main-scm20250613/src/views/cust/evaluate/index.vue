<template>
  <!-- 客商评价 页面 -->
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户编号" prop="custNo">
        <el-input v-model="queryParams.custNo" placeholder="请选择客户编号" style="width: 130px;" :disabled="true"/>
        {{custNoName}}
        <el-button type="primary" icon="el-icon-search" size="mini" style="width: 75px" @click="openCustNoForm">
          选择
        </el-button>
      </el-form-item>
      <el-form-item label="客户等级" prop="custLevel">
        <el-select v-model="queryParams.custLevel" placeholder="请选择客户等级" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUSTOM_LEVEL)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="客户状态" prop="custState">
        <el-select v-model="queryParams.custState" placeholder="请选择客户状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUST_STATE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="客户评价" prop="custEvaluate">
        <el-input v-model="queryParams.custEvaluate" placeholder="请输入客户评价" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
     <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['cust:evaluate:export']">导出</el-button>
      </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" prop="id" />
      <el-table-column label="客户编号" align="center" prop="custNo" />
      <el-table-column label="客户单位" align="center" prop="supplierName" />
      <el-table-column label="客户等级" align="center" prop="custLevel">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.CUSTOM_LEVEL" :value="scope.row.custLevel" />
        </template>
      </el-table-column>
      <el-table-column label="客户状态" align="center" prop="custState">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.CUST_STATE" :value="scope.row.custState" />
        </template>
      </el-table-column>
      <el-table-column label="客户评价" align="center" prop="custEvaluate" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="openForm(scope.row.id,scope.row.custNo,'view')">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,scope.row.custNo,'edit')"
                     v-hasPermi="['cust:evaluate:update']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <EvaluateForm ref="formRef" @success="getList" />
    <ExternalUnitSearch ref="formRef2" @success="handleMainChange"/>
    </div>
</template>

<script>
import * as EvaluateApi from '@/api/cust/evaluate';
import EvaluateForm from './EvaluateForm.vue';
import ExternalUnitSearch from "@/views/sms/loadwgt/other/ExternalUnitSearch.vue";

export default {
  name: "Evaluate",
  components: {
    ExternalUnitSearch,
          EvaluateForm,
  },
  data() {
    return {
      //显示选择的客户编号的字串
      custNoName:"",
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
              // 总条数
        total: 0,
      // 客商评价列表
      list: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryParams: {
                    pageNo: 1,
            pageSize: 10,
        custNo: null,
        custLevel: null,
        custState: null,
        custEvaluate: null,
      },
            };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
      this.loading = true;
              const res = await EvaluateApi.getEvaluatePage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 选择客户编号操作 */
    openCustNoForm() {
      this.$refs["formRef2"].open();
    },
    handleMainChange(row) {
      this.queryParams.custNo = row.supplierCode;
      this.custNoName = row.supplierName;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.custNoName="";
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id, custNo, type) {
      this.$refs["formRef"].open(id, custNo, type, this.list);
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有客商评价数据项?');
      try {
        this.exportLoading = true;
        this.resetQuery();
        const data = await EvaluateApi.exportEvaluateExcel(this.queryParams);
        this.$download.excel(data, '客商评价.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
              }
};
</script>
