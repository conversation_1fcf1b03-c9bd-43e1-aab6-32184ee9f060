<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-row>
          <el-col :span="8">
                    <el-form-item label="沟通主题" prop="commnicateTheme">
                      <el-input v-model="formData.commnicateTheme" placeholder="请输入沟通主题" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="客户分类" prop="custKind">
                      <el-select v-model="formData.custKind" placeholder="请选择客户分类" style="width: 220px">
                        <el-option v-for="dict in this.getDictDatas(DICT_TYPE.CUST_KIND)"
                                   :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="沟通时间" prop="commnicateTime">
                      <el-date-picker clearable v-model="formData.commnicateTime" type="date" value-format="timestamp" placeholder="选择沟通时间"  style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="沟通地点" prop="commnicateAdress">
                      <el-input v-model="formData.commnicateAdress" placeholder="请输入沟通地点" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="16">
                    <el-form-item label="沟通内容" prop="commnicateDetails">
                      <el-input v-model="formData.commnicateDetails" placeholder="请输入沟通内容" style="width: 660px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="沟通方式" prop="commnicateMode">
                      <el-input v-model="formData.commnicateMode" placeholder="请输入沟通方式" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="销售负责人" prop="salesman">
                      <el-input v-model="formData.salesman" placeholder="请输入销售负责人" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="销售负责人电话" prop="salesmanPhoneno" label-width="130px">
                      <el-input v-model="formData.salesmanPhoneno" placeholder="请输入销售负责人电话" style="width: 190px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="客户姓名" prop="custName">
                      <el-input v-model="formData.custName" placeholder="请输入客户姓名" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="客户性别" prop="custSex">
                      <el-select v-model="formData.custSex" placeholder="请选择客户性别" style="width: 220px">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="联系电话" prop="phoneNo">
                      <el-input v-model="formData.phoneNo" placeholder="请输入联系电话" style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="formData.email" placeholder="请输入邮箱" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="微信" prop="wchat">
                      <el-input v-model="formData.wchat" placeholder="请输入微信" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="QQ" prop="qq">
                      <el-input v-model="formData.qq" placeholder="请输入QQ" style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
                    <el-form-item label="地址" prop="adress">
                      <el-input v-model="formData.adress" placeholder="请输入地址" />
                    </el-form-item>
          </el-col>
          <el-col :span="12">
                    <el-form-item label="网址" prop="url">
                      <el-input v-model="formData.url" placeholder="请输入网址" />
                    </el-form-item>
          </el-col>
        </el-row>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注" />
                    </el-form-item>
                    <el-form-item label="附件" prop="annex">
                      <FileUpload v-model="formData.annex" :readonlyFlag="true"/>
                    </el-form-item>
      </el-form>
              <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as CommunicateApi from '@/api/cust/communicate';
  import FileUpload from '@/components/FileUpload';

      export default {
    name: "CommunicateForm",
    components: {
      FileUpload    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            id: undefined,
                            commnicateTheme: undefined,
                            custKind: undefined,
                            commnicateTime: undefined,
                            commnicateAdress: undefined,
                            commnicateMode: undefined,
                            commnicateDetails: undefined,
                            custName: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            adress: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        },
        // 表单校验
        formRules: {
                        commnicateTheme: [{ required: true, message: '沟通主题不能为空', trigger: 'blur' }],
                        custKind: [{ required: true, message: '客户分类不能为空', trigger: 'change' }],
                        commnicateTime: [{ required: true, message: '沟通时间不能为空', trigger: 'blur' }],
                        commnicateAdress: [{ required: true, message: '沟通地点不能为空', trigger: 'blur' }],
                        commnicateMode: [{ required: true, message: '沟通方式不能为空', trigger: 'blur' }],
                        custName: [{ required: true, message: '客户姓名不能为空', trigger: 'blur' }],
                        custSex: [{ required: true, message: '客户性别不能为空', trigger: 'change' }],
                        phoneNo: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
                        salesman: [{ required: true, message: '销售负责人不能为空', trigger: 'blur' }],
                        salesmanPhoneno: [{ required: true, message: '销售负责人电话不能为空', trigger: 'blur' }],
        },
                        };
    },
    methods: {
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await CommunicateApi.getCommunicate(id);
            this.formData = res.data;
            this.dialogTitle = "修改沟通记录";
          } finally {
            this.formLoading = false;
          }
        } else {
          this.dialogTitle = "新增沟通记录";
        }
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await CommunicateApi.updateCommunicate(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await CommunicateApi.createCommunicate(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
                      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            commnicateTheme: undefined,
                            custKind: undefined,
                            commnicateTime: undefined,
                            commnicateAdress: undefined,
                            commnicateMode: undefined,
                            commnicateDetails: undefined,
                            custName: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            adress: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
