<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户编号" prop="custNo">
        <el-input v-model="queryParams.custNo" placeholder="请选择客户编号" style="width: 130px;" :disabled="true"/>
        {{custNoName}}
        <el-button type="primary" icon="el-icon-search" size="mini" style="width: 75px" @click="openCustNoForm">
          选择
        </el-button>
      </el-form-item>
      <el-form-item label="客户性别" prop="custSex">
        <el-select v-model="queryParams.custSex" placeholder="请选择客户性别" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="意见处理状态" prop="custKind" label-width="100px">
        <el-select v-model="queryParams.custKind" placeholder="请选择意见处理状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ADVICE_STATE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈意见" prop="feedbackDetails" v-show="showField">
        <el-input v-model="queryParams.feedbackDetails" placeholder="请输入反馈意见" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="销售负责人" prop="salesman" v-show="showField" label-width="100px">
        <el-input v-model="queryParams.salesman" placeholder="请输入销售负责人" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark" v-show="showField">
        <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="新增时间" prop="createTime" v-show="showField">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button :icon="termIcon" @click="changeShowField">条件栏</el-button>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['cust:feedback:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['cust:feedback:export']">导出</el-button>
      </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" prop="id" />
      <el-table-column label="客户姓名" align="center" prop="custName" width="120"/>
      <el-table-column label="客户编号" align="center" prop="custNo" width="120"/>
      <el-table-column label="客户性别" align="center" prop="custSex" width="120">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.custSex" />
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phoneNo" width="120"/>
      <el-table-column label="邮箱" align="center" prop="email" width="120"/>
      <el-table-column label="微信" align="center" prop="wchat" width="120"/>
      <el-table-column label="QQ" align="center" prop="qq" width="120"/>
      <el-table-column label="网址" align="center" prop="url" width="120"/>
      <el-table-column label="反馈意见" align="center" prop="feedbackDetails" width="200"/>
      <el-table-column label="意见处理状态" align="center" prop="custKind" width="120"/>
      <el-table-column label="销售负责人" align="center" prop="salesman" width="120"/>
      <el-table-column label="销售负责人电话" align="center" prop="salesmanPhoneno" width="120"/>
      <el-table-column label="备注" align="center" prop="remark" width="200"/>
      <el-table-column label="新增时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150px" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id)"
                     v-hasPermi="['cust:feedback:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['cust:feedback:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize" @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <FeedbackForm ref="formRef" @success="getList" />
    <ExternalUnitSearch ref="formRef2" @success="handleMainChange"/>
    </div>
</template>

<script>
import * as FeedbackApi from '@/api/cust/feedback';
import FeedbackForm from './FeedbackForm.vue';
import ExternalUnitSearch from "@/views/sms/loadwgt/other/ExternalUnitSearch.vue";

export default {
  name: "Feedback",
  components: {
    ExternalUnitSearch,
          FeedbackForm,
  },
  data() {
    return {
      custNoName:"",
      loading: true,      // 遮罩层
      exportLoading: false,      // 导出遮罩层
      showSearch: true,     // 显示搜索条件
      showField: false, //显示多余的搜索条件
      termIcon: "el-icon-d-arrow-right", //条件栏样式
      total: 0, // 总条数
      list: [],    // 反馈意见列表
      isExpandAll: true,      // 是否展开，默认全部展开
      refreshTable: true,      // 重新渲染表格状态
      currentRow: {},      // 选中行
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        custName: null,
        custNo: null,
        custSex: null,
        feedbackDetails: null,
        custKind: null,
        salesman: null,
        salesmanPhoneno: null,
        remark: null,
        createTime: [],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
      this.loading = true;
              const res = await FeedbackApi.getFeedbackPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    openCustNoForm() {
      this.$refs["formRef2"].open();
    },
    handleMainChange(row) {
      this.queryParams.custNo = row.supplierCode;
      this.custNoName = row.supplierName;
    },
    /** 点击条件栏操作 */
    changeShowField() {
      this.showField = !this.showField;
      if (this.showField) {
        this.termIcon = "el-icon-d-arrow-left";
      } else {
        this.termIcon = "el-icon-d-arrow-right";
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.custNoName="";
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id) {
      this.$refs["formRef"].open(id);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除反馈意见编号为"' + id + '"的数据项?')
      try {
       await FeedbackApi.deleteFeedback(id);
       await this.getList();
       this.$modal.msgSuccess("删除成功");
      } catch {}
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有反馈意见数据项?');
      try {
        this.exportLoading = true;
        const data = await FeedbackApi.exportFeedbackExcel(this.queryParams);
        this.$download.excel(data, '反馈意见.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
              }
};
</script>
