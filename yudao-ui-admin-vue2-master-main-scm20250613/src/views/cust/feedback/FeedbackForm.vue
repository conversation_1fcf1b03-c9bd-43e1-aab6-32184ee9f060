<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="70%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-row>
          <el-col :span="8">
                    <el-form-item label="客户姓名" prop="custName">
                      <el-input v-model="formData.custName" placeholder="请输入客户姓名" style="width: 220px" :disabled="true"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户编号" prop="custNo">
              <el-input v-model="formData.custNo" placeholder="请选择客户编号" style="width: 145px;" :disabled="true"/>
              <el-button type="primary" icon="el-icon-search" size="mini" style="width: 75px" @click="openCustNoForm">选择</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="客户性别" prop="custSex">
                      <el-select v-model="formData.custSex" placeholder="请选择客户性别" style="width: 220px">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="联系电话" prop="phoneNo">
                      <el-input v-model="formData.phoneNo" placeholder="请输入联系电话" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="formData.email" placeholder="请输入邮箱" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="微信" prop="wchat">
                      <el-input v-model="formData.wchat" placeholder="请输入微信" style="width: 220px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="QQ" prop="qq">
                      <el-input v-model="formData.qq" placeholder="请输入QQ" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="16">
                    <el-form-item label="网址" prop="url">
                      <el-input v-model="formData.url" placeholder="请输入网址"  style="width: 560px"/>
                    </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
                    <el-form-item label="意见处理状态" prop="custKind" label-width="130px">
                      <el-select v-model="formData.custKind" placeholder="请选择意见处理状态" style="width: 190px">
                        <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ADVICE_STATE)"
                                   :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="销售负责人" prop="salesman">
                      <el-input v-model="formData.salesman" placeholder="请输入销售负责人" style="width: 220px"/>
                    </el-form-item>
          </el-col>
          <el-col :span="8">
                    <el-form-item label="销售负责人电话" prop="salesmanPhoneno" label-width="130px">
                      <el-input v-model="formData.salesmanPhoneno" placeholder="请输入销售负责人电话" style="width: 190px"/>
                    </el-form-item>
          </el-col>
        </el-row>
                    <el-form-item label="反馈意见" prop="feedbackDetails">
                      <el-input v-model="formData.feedbackDetails" placeholder="请输入反馈意见" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注" />
                    </el-form-item>
                    <el-form-item label="附件" prop="annex">
                      <FileUpload v-model="formData.annex" :readonlyFlag="true"/>
                    </el-form-item>
      </el-form>
              <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <ExternalUnitSearch ref="formRef2" @success="handleMainChange"/>
  </div>
</template>

<script>
  import * as FeedbackApi from '@/api/cust/feedback';
  import ExternalUnitSearch from "@/views/sms/loadwgt/other/ExternalUnitSearch.vue";
  import FileUpload from '@/components/FileUpload';

  export default {
    name: "FeedbackForm",
    components: {
      FileUpload,
      ExternalUnitSearch
    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            id: undefined,
                            custName: undefined,
                            custNo: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            feedbackDetails: undefined,
                            custKind: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        },
        // 表单校验
        formRules: {
                        custName: [{ required: true, message: '客户姓名不能为空', trigger: 'blur' }],
                        custNo: [{ required: true, message: '客户编号不能为空', trigger: 'blur' }],
                        custSex: [{ required: true, message: '客户性别不能为空', trigger: 'change' }],
                        phoneNo: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
                        custKind: [{ required: true, message: '意见处理状态不能为空', trigger: 'change' }],
                        salesman: [{ required: true, message: '销售负责人不能为空', trigger: 'blur' }],
                        salesmanPhoneno: [{ required: true, message: '销售负责人电话不能为空', trigger: 'blur' }],
        },
                        };
    },
    methods: {
      openCustNoForm() {
        this.$refs["formRef2"].open();
      },
      handleMainChange(row) {
        this.formData.custNo = row.supplierCode;
        this.formData.custName = row.supplierName;
        this.formData.phoneNo = row.contactMobile;
      },
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await FeedbackApi.getFeedback(id);
            this.formData = res.data;
            this.dialogTitle = "修改反馈意见";
          } finally {
            this.formLoading = false;
          }
        } else {
          this.dialogTitle = "新增反馈意见";
        }
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await FeedbackApi.updateFeedback(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await FeedbackApi.createFeedback(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            custName: undefined,
                            custNo: undefined,
                            custSex: undefined,
                            phoneNo: undefined,
                            email: undefined,
                            wchat: undefined,
                            qq: undefined,
                            url: undefined,
                            feedbackDetails: undefined,
                            custKind: undefined,
                            salesman: undefined,
                            salesmanPhoneno: undefined,
                            remark: undefined,
                            annex: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
