<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['srm:external-unit:create']">新增
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"  highlight-current-row>
      <el-table-column label="银行序号" align="center" prop="id" width="180"/>
      <el-table-column label="供应商编号" align="center" prop="supplierCode" width="180"/>
      <el-table-column label="开户行" align="center" prop="bankDeposit" width="180"/>
      <el-table-column label="联行号" align="center" prop="bankNo" width="180"/>
      <el-table-column label="银行账号" align="center" prop="bankAccount" width="180"/>
      <el-table-column label="开户名称" align="center" prop="accountName" width="180"/>
      <el-table-column label="大类银行代码" align="center" prop="majorBankCodes" width="180"/>
      <el-table-column label="大类银行名称" align="center" prop="majorBankName" width="180"/>
      <el-table-column label="供应商使用" align="center" prop="ifSupplier" width="180">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SRM_IF_SUPPLIER" :value="scope.row.ifSupplier"/>
        </template>
      </el-table-column>
      <el-table-column label="客商使用" align="center" prop="ifCustomer" width="180">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SRM_IF_CUSTOMER" :value="scope.row.ifCustomer"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目号" align="center" prop="project" width="180"/>
      <el-table-column label="公司别" align="center" prop="compId" width="180"/>
      <el-table-column label="银行账户状态" align="center" prop="accountStatus" width="180">
        <template v-slot="scope">
          <!--                    <dict-tag :type="DICT_TYPE.INFRA_JOB_STATUS" :value="scope.row.accountStatus" />-->
          <dict-tag :type="DICT_TYPE.SRM_BANK_STSTUS" :value="scope.row.accountStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id)"
                     v-hasPermi="['srm:external-unit:update']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['srm:external-unit:delete']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <BankInformationForm ref="formRef" @success="getList"/>
  </div>
</template>

<script>
import * as ExternalUnitApi from '@/api/srm/externalunit';
import BankInformationForm from './BankInformationForm.vue';

export default {
  name: "BankInformationList",
  components: {
    BankInformationForm
  },
  props: [
    'supplierCode'
  ],// 编码（主表的关联字段）
  data() {
    return {
      // 遮罩层
      loading: true,
      // 列表的数据
      list: [],
      // 列表的总页数
      total: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        supplierCode: undefined
      }
    };
  },
  watch: {
    /** 监听主表的关联字段的变化，加载对应的子表数据 */
    supplierCode: {
      handler(val) {
        this.queryParams.supplierCode = val;
        if (val) {
          this.handleQuery();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        const res = await ExternalUnitApi.getBankInformationPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 添加/修改操作 */
    openForm(id) {
      if (!this.supplierCode) {
        this.$modal.msgError('请选择一个外部单位');
        return;
      }
      this.$refs["formRef"].open(id, this.supplierCode);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除外部单位编号为"' + id + '"的数据项?');
      try {
        await ExternalUnitApi.deleteBankInformation(id);
        await this.getList();
        this.$modal.msgSuccess("删除成功");
      } catch {
      }
    },
  }
};
</script>
