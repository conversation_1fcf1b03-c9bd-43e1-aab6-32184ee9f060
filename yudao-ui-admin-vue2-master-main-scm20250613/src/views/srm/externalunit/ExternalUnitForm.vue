<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="180px">
        <el-row>
          <el-col :span="12">
        <el-form-item label="外部单位名称" prop="supplierName">
          <el-input v-model="formData.supplierName" placeholder="请输入外部单位名称"/>
        </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="socialCreditCode">
              <el-input v-model="formData.socialCreditCode" placeholder="请输入统一社会信用代码"/>
            </el-form-item>
          </el-col>




        </el-row>
        <el-row>
          <el-col :span="12">
        <el-form-item label="外部单位简称" prop="abbreviation">
          <el-input v-model="formData.abbreviation" placeholder="请输入外部单位简称"/>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="外部单位英文简称" prop="englishAbbreviation">
          <el-input v-model="formData.englishAbbreviation" placeholder="请输入外部单位英文简称"/>
        </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
        <el-form-item label="公司法人" prop="contactName">
          <el-input v-model="formData.contactName" placeholder="请输入联系人"/>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="联系电话" prop="contactMobile">
          <el-input v-model="formData.contactMobile" placeholder="请输入联系电话"/>
        </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
        <el-form-item label="联系地址" prop="contactAddress">
          <el-input v-model="formData.contactAddress" placeholder="请输入联系地址"/>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="单位性质" prop="unitNature">
          <el-select v-model="formData.unitNature" placeholder="请选择单位性质">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_UNIT_NATURE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
        <el-form-item label="是否客户" prop="ifCustomer">
          <el-select v-model="formData.ifCustomer" placeholder="请选择是否客户">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_IF_CUSTOMER)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="是否供应商" prop="ifSupplier">
          <el-select v-model="formData.ifSupplier" placeholder="请选择是否供应商">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_IF_SUPPLIER)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
          </el-col>
        </el-row>
<!--               <el-form-item label="统一社会信用代码" prop="socialCreditCode">-->
<!--          <el-input v-model="formData.socialCreditCode" placeholder="请输入统一社会信用代码"/>-->
<!--        </el-form-item>-->
        <el-row>
          <el-col :span="12">
        <el-form-item label="个人有效证件类型" prop="documentType">
          <el-select v-model="formData.documentType" placeholder="请选择个人有效证件类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_DOCUMENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="个人有效证件类型证件号" prop="certificateTypeCode">
          <el-input v-model="formData.certificateTypeCode" placeholder="请输入个人有效证件类型证件号"/>
        </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工商注册地址" prop="businessAddress">
          <el-input v-model="formData.businessAddress" placeholder="请输入工商注册地址"/>
        </el-form-item>
<!--        <el-row>-->
          <el-col :span="12">
        <el-form-item label="国家" prop="country">
          <el-select v-model="formData.country" placeholder="请选择国家">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_COUNTRY)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--        <el-form-item label="省份" prop="province">-->
<!--          <el-select v-model="formData.province" placeholder="请选择省份">-->
<!--            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_PROVINCE)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-form-item label="城市" prop="city">-->
<!--          <el-select v-model="formData.city" placeholder="请选择城市">-->
<!--            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SRM_CITY)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="公司简介" prop="companyProfile">
          <el-input v-model="formData.companyProfile" placeholder="请输入公司简介"/>
        </el-form-item>


<!--        <el-form-item label="银行账户" prop="bankAccount">-->
<!--          <el-input v-model="formData.bankAccount" placeholder="请输入银行账户"/>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="开户名称" prop="accountName">-->
<!--          <el-input v-model="formData.accountName" placeholder="请输入开户名称"/>-->
<!--        </el-form-item>-->
        <el-row>
          <el-col :span="12">
        <el-form-item label="旧厂商编号" prop="oldSupplierCode">
          <el-input v-model="formData.oldSupplierCode" placeholder="请输入旧厂商编号"/>
        </el-form-item>
          </el-col>
          <el-col :span="12">
        <el-form-item label="邮政编码" prop="postalCode">
          <el-input v-model="formData.postalCode" placeholder="请输入邮政编码"/>
        </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="formData.memo" placeholder="请输入备注"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态">
                <el-option :key="'1'" :label="'1 可用'" :value="'1'"/>
                <el-option :key="'0'" :label="'0 停用'" :value="'0'"/>
              </el-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--        <el-form-item label="是否审核通过" prop="ifApproved">-->
<!--          <el-select v-model="formData.ifApproved" placeholder="请选择是否审核通过">-->
<!--            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PMS_AUDIT_TYPE)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--          </el-col>-->
        </el-row>
<!--        <el-form-item label="项目号" prop="project">-->
<!--          <el-input v-model="formData.project" placeholder="请输入项目号"/>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="公司别" prop="compId">-->
<!--          <el-input v-model="formData.compId" placeholder="请输入公司别"/>-->
<!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as ExternalUnitApi from '@/api/srm/externalunit';
import * as GrMainApi from "@/api/pms/grmain";

export default {
  name: "ExternalUnitForm",
  components: {},
  data() {
    return {
      // 弹出层标题
      dialogTitle: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
      formLoading: false,
      // 表单参数
      formData: {
        supplierCode: undefined,
        supplierName: undefined,
        abbreviation: undefined,
        englishAbbreviation: undefined,
        contactName: undefined,
        contactMobile: undefined,
        contactAddress: undefined,
        unitNature: undefined,
        ifCustomer: undefined,
        ifSupplier: undefined,
        socialCreditCode: undefined,
        documentType: undefined,
        certificateTypeCode: undefined,
        country: undefined,
        province: undefined,
        businessAddress: undefined,
        city: undefined,
        companyProfile: undefined,
        oldSupplierCode: undefined,
        ifApproved: undefined,
        bankAccount: undefined,
        accountName: undefined,
        postalCode: undefined,
        memo: undefined,
        status: undefined,
        project: undefined,
        compId: undefined,
      },
      // 表单校验
      formRules: {
        supplierName: [{required: true, message: '外部单位名称不能为空', trigger: 'blur'}],
        socialCreditCode: [{required: true, message: '社会统一信用代码不能为空', trigger: 'blur'}],
        ifCustomer: [{required: true, message: '是否客商不能为空', trigger: 'blur'}],
        ifSupplier: [{required: true, message: '是否供应商不能为空', trigger: 'blur'}],
        // contactName: [{required: true, message: '法人不能为空', trigger: 'blur'}],
        // contactMobile: [{required: true, message: '法人不能为空', trigger: 'blur'}],
        // contactAddress: [{required: true, message: '法人不能为空', trigger: 'blur'}],
      },
    };
  },
  methods: {
    /** 打开弹窗 */
    // async open(id) {
    //   this.dialogVisible = true;
    //   this.reset();
    //   // 修改时，设置数据
    //   if (id) {
    //     this.formLoading = true;
    //     try {
    //       const res = await ExternalUnitApi.getExternalUnit(id);
    //       this.formData = res.data;
    //       this.title = "修改外部单位";
    //     } finally {
    //       this.formLoading = false;
    //     }
    //   }
    //   this.title = "新增外部单位";
    // },
    async open(id) {
      this.dialogVisible = true;
      this.reset(); // 确保 reset 方法不会覆盖 this.title

      // 修改时，设置数据
      if (id) {
        this.formLoading = true;
        try {
          const res = await ExternalUnitApi.getExternalUnit(id);
          this.formData = res.data;
          this.dialogTitle = "修改外部单位"; // 修改时的标题
        } finally {
          this.formLoading = false;
        }
      } else {
        this.dialogTitle = "新增外部单位"; // 新增时的标题
      }
    },


    /** 提交按钮 */
    async submitForm() {
      // 校验主表
      await this.$refs["formRef"].validate();
      this.formLoading = true;
      try {
        const data = this.formData;
        // 修改的提交
        if (data.id) {
          await ExternalUnitApi.updateExternalUnit(data);
          this.$modal.msgSuccess("修改成功");
          this.dialogVisible = false;
          this.$emit('success');
          return;
        }
        // 添加的提交
        await ExternalUnitApi.createExternalUnit(data);
        this.$modal.msgSuccess("新增成功");
        this.dialogVisible = false;
        this.$emit('success');
      } finally {
        this.formLoading = false;
      }
    },
    /** 表单重置 */
    reset() {
      this.formData = {
        supplierCode: undefined,
        supplierName: undefined,
        abbreviation: undefined,
        englishAbbreviation: undefined,
        contactName: undefined,
        contactMobile: undefined,
        contactAddress: undefined,
        unitNature: undefined,
        ifCustomer: undefined,
        ifSupplier: undefined,
        socialCreditCode: undefined,
        documentType: undefined,
        certificateTypeCode: undefined,
        country: undefined,
        province: undefined,
        businessAddress: undefined,
        city: undefined,
        companyProfile: undefined,
        oldSupplierCode: undefined,
        ifApproved: undefined,
        bankAccount: undefined,
        accountName: undefined,
        postalCode: undefined,
        memo: undefined,
        status: undefined,
        project: undefined,
        compId: undefined,
      };
      this.resetForm("formRef");
    }
  }
};
</script>
