<template>
  <el-row :gutter="10" class="panel-group">
    <!-- 左边两个公告面板 -->
    <el-col :lg="12" class="card-panel-col">
      <div class="card-panel">
        <div class="panel-header">
          <svg-icon icon-class="online" class-name="card-panel-icon"/>
          <span class="panel-title">公告</span>
        </div>
        <div class="panel-content">
          <SystemMyNotifyNews ref="newsRef1"/>
        </div>
      </div>
      <div class="card-panel" style="margin-top: 15px;">
        <div class="panel-header">
          <svg-icon icon-class="online" class-name="card-panel-icon"/>
          <span class="panel-title">通知</span>
        </div>
        <div class="panel-content">
          <SystemMyNotifyMessage ref="formRef"/>
        </div>
      </div>
    </el-col>

    <!-- 右边日历面板 -->
    <el-col :lg="12" class="card-panel-col">
      <div class="card-panel" style="height: 795px;">
        <div class="panel-header">
          <svg-icon icon-class="message" class-name="card-panel-icon"/>
          <span class="panel-title">收款计划</span>
        </div>
        <div class="panel-content">
          <PaymentCalendar ref="calendarRef"/>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import SystemMyNotifyNews from "@/views/system/notify/my/newsIndex.vue";
import SystemMyNotifyMessage from "@/views/system/notify/my/messageIndex.vue";
import PaymentCalendar from "@/views/pms/pomainio/components/paymentcalendar/PaymentCalendar.vue";

export default {
  components: {
    SystemMyNotifyNews,
    PaymentCalendar,
    SystemMyNotifyMessage
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 0px;

  .card-panel-col {
    margin-bottom: 15px;
  }

  .card-panel {
    height: 390px;
    position: relative;
    overflow: hidden;
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

    .panel-header {
      padding: 12px 16px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      align-items: center;
      background: #f9f9f9;

      .card-panel-icon {
        font-size: 16px;
        color: #666;
        margin-right: 8px;
      }

      .panel-title {
        font-size: 15px;
        font-weight: 500;
        color: #333;
      }
    }

    .panel-content {
      height: calc(100% - 46px);
      overflow: auto;
      padding: 12px;
    }
  }
}

@media (max-width: 992px) {
  .panel-group {
    .card-panel {
      height: auto;
      min-height: 300px;

      &[style*="height: 795px"] {
        height: auto !important;
        min-height: 600px;
      }
    }
  }
}

@media (max-width: 550px) {
  .panel-group {
    .card-panel {
      .panel-header {
        padding: 10px 12px;

        .card-panel-icon {
          font-size: 14px;
        }

        .panel-title {
          font-size: 14px;
        }
      }

      .panel-content {
        padding: 10px;
      }
    }
  }
}
</style>
