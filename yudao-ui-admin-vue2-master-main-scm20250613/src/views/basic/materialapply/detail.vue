<template>
  <div class="app-container">
    <el-form ref="formRef" :model="formData" size="small" :inline="true" :rules="formRules"
             v-loading="formLoading" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="状态" prop="matrlStatus">
            <el-select v-model="formData.matrlStatus" placeholder="请选择状态" disabled style="width: 190px">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BPM_TASK_STATUS)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="料号" prop="matrlno">
            <el-input v-model="formData.matrlno" placeholder="请输入料号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="中文品名" prop="cnmdesc">
            <el-input v-model="formData.cnmdesc" placeholder="请输入中文品名" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="物料类型" prop="invenTorytype">
            <el-select v-model="formData.invenTorytype" placeholder="请选择物料类型" disabled style="width: 190px">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16" :xs="24">
          <el-form-item label="短描述" prop="shortDesc">
            <el-input v-model="formData.shortDesc" type="textarea" placeholder="请输入内容" disabled style="width: 540px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="申请类别" prop="applyType">
            <el-select v-model="formData.applyType" placeholder="请选择申请类别" disabled style="width: 190px">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.APPLY_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="申请原因(简述）" prop="applyReason">
            <el-input v-model="formData.applyReason" type="textarea" :rows="1" placeholder="请输入内容" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="流程实例的编号" prop="processInstanceId">
            <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="申请人编号" prop="userId">
            <el-input v-model="formData.userId" placeholder="请输入申请人编号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="申请人姓名" prop="userName">
            <el-input v-model="formData.userName" placeholder="请输入申请人姓名" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import * as MaterialApplyApi from '@/api/basic/materialapply';

  export default {
    name: "MaterialApplyAuditDetail",
    components: {},
    props: {
      id: {
        type: [String, Number],
        default: undefined
      },
    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          id: undefined,
          parentid: undefined,
          matrlStatus: undefined,
          matrlno: undefined,
          cnmdesc: undefined,
          invenTorytype: undefined,
          shortDesc: undefined,
          applyType: undefined,
          applyReason: undefined,
          processInstanceId: undefined,
          userId: undefined,
          userName: undefined,
        },
        // 表单校验
        formRules: {},
      };
    },
    created() {
      this.open(this.id);
    },
    methods: {
      /** 打开弹窗 */
      async open(id) {
        this.reset();
        // 修改时，设置数据
        if (id) {
          try {
            const res = await MaterialApplyApi.getMaterialApply(id);
            this.formData = res.data;
          } finally {
          }
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          parentid: undefined,
          matrlStatus: undefined,
          matrlno: undefined,
          cnmdesc: undefined,
          invenTorytype: undefined,
          shortDesc: undefined,
          applyType: undefined,
          applyReason: undefined,
          processInstanceId: undefined,
          userId: undefined,
          userName: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
