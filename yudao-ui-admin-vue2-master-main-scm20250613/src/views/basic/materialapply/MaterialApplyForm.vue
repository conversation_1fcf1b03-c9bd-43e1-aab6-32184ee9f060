<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="PARENTID" prop="parentid">
          <el-input v-model="formData.parentid" placeholder="请输入PARENTID"/>
        </el-form-item>
        <el-form-item label="状态" prop="matrlStatus">
          <el-select v-model="formData.matrlStatus" placeholder="请选择状态">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BASIC_AUDIT_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="料号" prop="matrlno">
          <el-input v-model="formData.matrlno" placeholder="请输入料号"/>
        </el-form-item>
        <el-form-item label="中文品名" prop="cnmdesc">
          <el-input v-model="formData.cnmdesc" placeholder="请输入中文品名"/>
        </el-form-item>
        <el-form-item label="物料类型" prop="invenTorytype">
          <el-select v-model="formData.invenTorytype" placeholder="请选择物料类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label=" 短描述" prop="shortDesc">
          <el-input v-model="formData.shortDesc" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="申请类别" prop="applyType">
          <el-select v-model="formData.applyType" placeholder="请选择申请类别">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.APPLY_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label=" 申请原因(简述）" prop="applyReason">
          <el-input v-model="formData.applyReason" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="流程实例的编号" prop="processInstanceId">
          <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号"/>
        </el-form-item>
        <el-form-item label="申请人的用户编号" prop="userId">
          <el-input v-model="formData.userId" placeholder="请输入申请人的用户编号"/>
        </el-form-item>
        <el-form-item label="申请人的用户姓名" prop="userName">
          <el-input v-model="formData.userName" placeholder="请输入申请人的用户姓名"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialApplyApi from '@/api/basic/materialapply';

  export default {
    name: "MaterialApplyForm",
    components: {},
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          id: undefined,
          parentid: undefined,
          matrlStatus: undefined,
          matrlno: undefined,
          cnmdesc: undefined,
          invenTorytype: undefined,
          shortDesc: undefined,
          applyType: undefined,
          applyReason: undefined,
          processInstanceId: undefined,
          userId: undefined,
          userName: undefined,
        },
        // 表单校验
        formRules: {
          parentid: [{required: true, message: 'PARENTID不能为空', trigger: 'blur'}],
          userId: [{required: true, message: '申请人的用户编号不能为空', trigger: 'blur'}],
          userName: [{required: true, message: '申请人的用户姓名不能为空', trigger: 'blur'}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialApplyApi.getMaterialApply(id);
            this.formData = res.data;
            this.title = "修改物料申请";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增物料申请";
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialApplyApi.updateMaterialApply(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialApplyApi.createMaterialApply(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          parentid: undefined,
          matrlStatus: undefined,
          matrlno: undefined,
          cnmdesc: undefined,
          invenTorytype: undefined,
          shortDesc: undefined,
          applyType: undefined,
          applyReason: undefined,
          processInstanceId: undefined,
          userId: undefined,
          userName: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
