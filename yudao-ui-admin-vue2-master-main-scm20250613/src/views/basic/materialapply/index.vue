<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="状态" prop="matrlStatus">
        <el-select v-model="queryParams.matrlStatus" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BASIC_AUDIT_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="料号" prop="matrlno">
        <el-input v-model="queryParams.matrlno" placeholder="请输入料号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="中文品名" prop="cnmdesc">
        <el-input v-model="queryParams.cnmdesc" placeholder="请输入中文品名" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料类型" prop="invenTorytype">
        <el-select v-model="queryParams.invenTorytype" placeholder="请选择物料类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请类别" prop="applyType">
        <el-select v-model="queryParams.applyType" placeholder="请选择申请类别" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.APPLY_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
                        type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-finished" size="mini"
                   @click="openAuditForm"
                   v-hasPermi="['basic:material-apply:update']"
        >批量审批
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
              @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" fixed="left" :selectable="checkSelectable"/>
      <el-table-column label="状态" align="center" prop="matrlStatus" width="180" :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.BASIC_AUDIT_STATUS" :value="scope.row.matrlStatus"/>
        </template>
      </el-table-column>
      <!--      <el-table-column label="料号" align="center" prop="matrlno" width="120" :show-overflow-tooltip="true"/>-->
      <el-table-column label="料号" align="center" prop="matrlno" width="120" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <router-link :to="{ name: 'Material', params: { matrlno: row.matrlno } }"
                       style="color:#409EFF;text-decoration:underline;"
          > {{ row.matrlno }}
          </router-link>
        </template>
      </el-table-column>

      <el-table-column label="中文品名" align="center" prop="cnmdesc" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="物料类型" align="center" prop="invenTorytype" width="100" :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenTorytype"/>
        </template>
      </el-table-column>
      <el-table-column label="规格型号" align="center" prop="preText1" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="材质" align="center" prop="preText2" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="图号" align="center" prop="preText3" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="短描述" align="center" prop="shortDesc" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="申请类别" align="center" prop="applyType" width="120" :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.APPLY_TYPE" :value="scope.row.applyType"/>
        </template>
      </el-table-column>
      <el-table-column label="申请原因(简述）" align="center" prop="applyReason" width="250"
                       :show-overflow-tooltip="true"
      />
<!--      <el-table-column label="流程实例的编号" align="center" prop="processInstanceId" width="150"-->
<!--                       :show-overflow-tooltip="true"-->
<!--      />-->
<!--      <el-table-column label="申请人的用户编号" align="center" prop="userId" width="125" :show-overflow-tooltip="true"/>-->
      <el-table-column label="申请人的用户姓名" align="center" prop="userName" width="125"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row)"
                     v-hasPermi="['basic:material-apply:query']"
          >详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"
    />
    <!-- 对话框(添加 / 修改) -->
    <MaterialApplyForm ref="formRef" @success="getList"/>
    <BatchOperateAuditForm ref="formRef2" @success="getList"/>
  </div>
</template>

<script>
import * as MaterialApplyApi from '@/api/basic/materialapply'
import MaterialApplyForm from './MaterialApplyForm.vue'
import BatchOperateAuditForm from './other/batchOperateAuditForm.vue'
import {
  approveTask,
  rejectTask,
  getTaskListByProcessInstanceId
} from '@/api/bpm/task'
import store from '@/store'

export default {
  name: 'MaterialApply',
  components: {
    MaterialApplyForm, BatchOperateAuditForm
  },
  data() {
    return {
      idItems: [],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料申请列表
      list: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        parentid: null,
        matrlStatus: null,
        matrlno: null,
        cnmdesc: null,
        invenTorytype: null,
        shortDesc: null,
        applyType: null,
        applyReason: null,
        processInstanceId: null,
        userId: null,
        userName: null,
        createTime: []
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkSelectable(row, index) {
      const userId = store.getters.userId
      // 返回 true 表示可以选中，false 表示不可选中
      if (row && row.matrlStatus && (
        (row.matrlStatus == '2' && row.applyUserManagerId == userId)
        || (row.matrlStatus == '3' && row.applyUserLeaderId == userId)
        || (row.matrlStatus == '4' && row.planAcceptorId == userId)
        || (row.matrlStatus == '5' && row.planAcceptorLeaderId == userId)
        || (row.matrlStatus == '6' && row.approveProjkzId == userId)
      )) {
        return true
      } else {
        return false
      }
    },
    async openAuditForm() {
      if (this.idItems.length == 0) {
        this.$modal.msgWarning('请选择数据后再修改！')
        return
      }
      this.$refs['formRef2'].open(this.idItems)
    },
    handleSelectionChange(val) {
      this.idItems = val
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true
        const res = await MaterialApplyApi.getMaterialApplyPage(this.queryParams)
        this.list = res.data.list
        this.total = res.data.total
      } finally {
        this.loading = false
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 添加/修改操作 */
    openForm(row) {
      this.$router.push({ name: 'BpmProcessInstanceDetail', query: { id: row.processInstanceId, applyFlag: false } })
      // this.$refs["formRef"].open(id);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id
      await this.$modal.confirm('是否确认删除物料申请编号为"' + id + '"的数据项?')
      try {
        await MaterialApplyApi.deleteMaterialApply(id)
        await this.getList()
        this.$modal.msgSuccess('删除成功')
      } catch {
      }
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有物料申请数据项?')
      try {
        this.exportLoading = true
        const data = await MaterialApplyApi.exportMaterialApplyExcel(this.queryParams)
        this.$download.excel(data, '物料申请.xls')
      } catch {
      } finally {
        this.exportLoading = false
      }
    }
  }
}
</script>
