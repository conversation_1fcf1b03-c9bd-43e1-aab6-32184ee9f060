<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="30%" v-dialogDrag append-to-body>
      <el-form ref="formRef" size="small" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="审批意见" prop="reason" >
          <el-input v-model="formData.reason" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="submitForm(2)" :disabled="formLoading">审批通过</el-button>
        <el-button type="danger" @click="submitForm(3)" :disabled="formLoading">审批不通过</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    batchOperateAudit,
  } from "@/api/bpm/task";
  export default {
    name: "BatchOperateAuditForm",
    components: {
    },
    data() {
      return {
        dialogVisible: false,
        // 弹出层标题
        dialogTitle: "",
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        deptOptions: undefined,
        userList: null,
        // 表单参数
        formData: {
          type: undefined,
          reason: undefined,
          processInstanceIds: [],
        },
        // 表单校验
        formRules: {
          reason: [{required: true, message: "审批意见不能为空", trigger: "blur"}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(rows) {
        this.reset();
        this.dialogTitle = "批量审批";
        this.dialogVisible = true;
        let processInstanceIds = [];
        rows.forEach((el, index) => {
          processInstanceIds.push(el.processInstanceId);
        });
        this.formData.processInstanceIds = processInstanceIds;
      },
      /** 提交按钮 */
      async submitForm(type) {
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          this.formData.type = type;
          let res = await batchOperateAudit(this.formData);
          this.$alert(res.data,
            '信息', {
              dangerouslyUseHTMLString: true,
              type: 'info'
            });
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.typeName = undefined;
        this.formData = {
          type: undefined,
          reason: undefined,
          processInstanceIds: [],
        };
        this.resetForm("formRef");
      },
    }
  };
</script>
