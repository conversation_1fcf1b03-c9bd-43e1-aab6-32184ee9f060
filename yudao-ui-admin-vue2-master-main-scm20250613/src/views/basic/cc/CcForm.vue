<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
                    <el-form-item label="父ID" prop="parentId">
                      <TreeSelect
                        v-model="formData.parentId"
                        :options="ccTree"
                        :normalizer="normalizer"
                        placeholder="请选择父ID"
                      />
                    </el-form-item>
                    <el-form-item label="编码" prop="code">
                      <el-input v-model="formData.code" placeholder="请输入编码" />
                    </el-form-item>
                    <el-form-item label="名称" prop="name">
                      <el-input v-model="formData.name" placeholder="请输入名称" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注" />
                    </el-form-item>
      </el-form>
              <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as CcApi from '@/api/basic/cc';
    import TreeSelect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    export default {
    name: "CcForm",
    components: {
                  TreeSelect,
            },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            id: undefined,
                            parentId: undefined,
                            code: undefined,
                            name: undefined,
                            remark: undefined,
        },
        // 表单校验
        formRules: {
        },
                       ccTree: [], // 树形结构
              };
    },
    methods: {
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await CcApi.getCc(id);
            this.formData = res.data;
            this.title = "修改测试树";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增测试树";
                await this.getCcTree();
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await CcApi.updateCc(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await CcApi.createCc(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
                  /** 获得测试树树 */
         async getCcTree() {
            this.ccTree = [];
            const res = await CcApi.getCcList();
            const root = { id: 0, name: '顶级测试树', children: [] };
            root.children = this.handleTree(res.data, 'id', 'parentId')
                    console.log('root', root)
            this.ccTree.push(root)
          },
                  /** 转换测试树数据结构 */
          normalizer(node) {
            if (node.children && !node.children.length) {
              delete node.children;
            }
                return {
                  id: node.id,
                  label: node.name,
                  children: node.children
                };
          },
      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            parentId: undefined,
                            code: undefined,
                            name: undefined,
                            remark: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
