<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="PARENTID" prop="parentid">
          <el-input v-model="formData.parentid" placeholder="请输入PARENTID"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MATRL_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="变更类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择变更类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BASIC_EDIT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="修改原因" prop="editReason">
          <el-input v-model="formData.editReason" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="修改信息" prop="editInfo">
          <el-input v-model="formData.editInfo" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="修改字段具体信息" prop="editListJson">
          <el-input v-model="formData.editListJson" placeholder="请输入修改字段具体信息"/>
        </el-form-item>
        <el-form-item label="流程实例的编号" prop="processInstanceId">
          <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号"/>
        </el-form-item>
        <el-form-item label="审批状态" prop="auditStatus">
          <el-radio-group v-model="formData.auditStatus">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请人的用户编号" prop="userId">
          <el-input v-model="formData.userId" placeholder="请输入申请人的用户编号"/>
        </el-form-item>
        <el-form-item label="申请人的用户姓名" prop="userName">
          <el-input v-model="formData.userName" placeholder="请输入申请人的用户姓名"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialAuditApi from '@/api/basic/materialaudit';

  export default {
    name: "MaterialAuditForm",
    components: {},
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          id: undefined,
          parentid: undefined,
          status: undefined,
          type: undefined,
          editReason: undefined,
          editInfo: undefined,
          editListJson: undefined,
          processInstanceId: undefined,
          auditStatus: undefined,
          userId: undefined,
          userName: undefined,
        },
        // 表单校验
        formRules: {
          parentid: [{required: true, message: 'PARENTID不能为空', trigger: 'blur'}],
          userId: [{required: true, message: '申请人的用户编号不能为空', trigger: 'blur'}],
          userName: [{required: true, message: '申请人的用户姓名不能为空', trigger: 'blur'}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialAuditApi.getMaterialAudit(id);
            this.formData = res.data;
            this.title = "修改物料属性变更审核";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增物料属性变更审核";
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialAuditApi.updateMaterialAudit(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialAuditApi.createMaterialAudit(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          parentid: undefined,
          status: undefined,
          type: undefined,
          editReason: undefined,
          editInfo: undefined,
          editListJson: undefined,
          processInstanceId: undefined,
          auditStatus: undefined,
          userId: undefined,
          userName: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
