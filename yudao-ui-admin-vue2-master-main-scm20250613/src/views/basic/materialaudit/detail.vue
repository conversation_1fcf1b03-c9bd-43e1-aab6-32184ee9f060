<template>
  <div class="app-container">
    <el-form ref="formRef" :model="formData" :rules="formRules"
             size="small" :inline="true" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="料号" prop="preText1">
            <el-input v-model="formData.preText1" placeholder="请输入料号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="中文品名" prop="preText2">
            <el-input v-model="formData.preText2" placeholder="请输入中文品名" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="变更类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择变更类型" disabled style="width: 190px">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BASIC_EDIT_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="审批状态" prop="auditStatus">
            <el-select v-model="formData.auditStatus" placeholder="请选择审批状态" disabled style="width: 190px">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BPM_TASK_STATUS)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="流程实例的编号" prop="processInstanceId">
            <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form-item label="修改原因" prop="editReason">
            <el-input v-model="formData.editReason" type="textarea" placeholder="请输入内容" disabled style="width: 850px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form-item label="修改信息" prop="editInfo">
            <el-input v-model="formData.editInfo" type="textarea" placeholder="请输入内容" disabled style="width: 850px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <el-form-item label="申请人用户编号" prop="userId">
            <el-input v-model="formData.userId" placeholder="请输入申请人用户编号" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="申请人用户姓名" prop="userName">
            <el-input v-model="formData.userName" placeholder="请输入申请人用户姓名" disabled style="width: 190px"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import * as MaterialAuditApi from '@/api/basic/materialaudit';

  export default {
    name: "MaterialAuditForm",
    components: {},
    props: {
      id: {
        type: [String, Number],
        default: undefined
      },
    },
    data() {
      return {
        // 表单参数
        formData: {
          id: undefined,
          parentid: undefined,
          status: undefined,
          type: undefined,
          editReason: undefined,
          editInfo: undefined,
          editListJson: undefined,
          processInstanceId: undefined,
          auditStatus: undefined,
          userId: undefined,
          userName: undefined,
        },
        // 表单校验
        formRules: {
        },
      };
    },
    created() {
      this.open(this.id);
    },
    methods: {
      /** 打开弹窗 */
      async open(id) {
        // 修改时，设置数据
        if (id) {
          try {
            const res = await MaterialAuditApi.getMaterialAudit(id);
            this.formData = res.data;
          } finally {
          }
        }
      },
    }
  };
</script>
