<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="PARENTID" prop="parentid">
          <el-input v-model="formData.parentid" placeholder="请输入PARENTID"/>
        </el-form-item>
        <el-form-item label="技术附件">
          <FileUpload v-model="formData.techFile"/>
        </el-form-item>
        <el-form-item label="物料图档">
          <ImageUpload v-model="formData.picFile"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialFileApi from '@/api/basic/materialfile';
  import ImageUpload from '@/components/ImageUpload';
  import FileUpload from '@/components/FileUpload';

  export default {
    name: "MaterialFileForm",
    components: {
      ImageUpload,
      FileUpload,
    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          id: undefined,
          parentid: undefined,
          techFile: undefined,
          picFile: undefined,
        },
        // 表单校验
        formRules: {
          parentid: [{required: true, message: 'PARENTID不能为空', trigger: 'blur'}],
          techFile: [{required: true, message: '技术附件不能为空', trigger: 'blur'}],
          picFile: [{required: true, message: '物料图档不能为空', trigger: 'blur'}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialFileApi.getMaterialFile(id);
            this.formData = res.data;
            this.title = "修改料号附件";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增料号附件";
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialFileApi.updateMaterialFile(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialFileApi.createMaterialFile(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          parentid: undefined,
          techFile: undefined,
          picFile: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
