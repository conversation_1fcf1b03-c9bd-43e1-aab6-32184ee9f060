<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
                    <el-form-item label="公司别" prop="compid">
                      <el-input v-model="formData.compid" placeholder="请输入公司别" />
                    </el-form-item>
                    <el-form-item label="料号" prop="matrlno">
                      <el-input v-model="formData.matrlno" placeholder="请输入料号" />
                    </el-form-item>
                    <el-form-item label="料号分类" prop="matrlIndexid">
                      <el-input v-model="formData.matrlIndexid" placeholder="请输入料号分类" />
                    </el-form-item>
                    <el-form-item label="品别代码" prop="invenTorytype">
                      <el-select v-model="formData.invenTorytype" placeholder="请选择品别代码">
                            <el-option label="请选择字典生成" value="" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="采购员" prop="purchaser">
                      <el-input v-model="formData.purchaser" placeholder="请输入采购员" />
                    </el-form-item>
                    <el-form-item label="采购前置天数" prop="predays">
                      <el-input v-model="formData.predays" placeholder="请输入采购前置天数" />
                    </el-form-item>
                    <el-form-item label="物料采购天数" prop="daysofpur">
                      <el-input v-model="formData.daysofpur" placeholder="请输入物料采购天数" />
                    </el-form-item>
                    <el-form-item label="请购点" prop="rop">
                      <el-input v-model="formData.rop" placeholder="请输入请购点" />
                    </el-form-item>
                    <el-form-item label="安全存量" prop="minstkqty">
                      <el-input v-model="formData.minstkqty" placeholder="请输入安全存量" />
                    </el-form-item>
                    <el-form-item label="最大库存量" prop="maxstkqty">
                      <el-input v-model="formData.maxstkqty" placeholder="请输入最大库存量" />
                    </el-form-item>
                    <el-form-item label="消耗定额" prop="minconsumpqty">
                      <el-input v-model="formData.minconsumpqty" placeholder="请输入消耗定额" />
                    </el-form-item>
                    <el-form-item label="设备用量" prop="meuseqty">
                      <el-input v-model="formData.meuseqty" placeholder="请输入设备用量" />
                    </el-form-item>
                    <el-form-item label="平均日用量" prop="avgdailyqty">
                      <el-input v-model="formData.avgdailyqty" placeholder="请输入平均日用量" />
                    </el-form-item>
                    <el-form-item label="预计年用量" prop="yearuseqty">
                      <el-input v-model="formData.yearuseqty" placeholder="请输入预计年用量" />
                    </el-form-item>
                    <el-form-item label="有效日数" prop="availabydays">
                      <el-input v-model="formData.availabydays" placeholder="请输入有效日数" />
                    </el-form-item>
                    <el-form-item label="是否呆料" prop="isidle">
                      <el-select v-model="formData.isidle" placeholder="请选择是否呆料">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否批号管理" prop="lotchk">
                      <el-select v-model="formData.lotchk" placeholder="请选择是否批号管理">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否批价管理" prop="pricechk">
                      <el-select v-model="formData.pricechk" placeholder="请选择是否批价管理">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否时效控管" prop="expirechk">
                      <el-select v-model="formData.expirechk" placeholder="请选择是否时效控管">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否固定资产" prop="fixed">
                      <el-select v-model="formData.fixed" placeholder="请选择是否固定资产">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="固定资产大类" prop="fixedassetstype">
                      <el-input v-model="formData.fixedassetstype" placeholder="请输入固定资产大类" />
                    </el-form-item>
                    <el-form-item label="供货渠道" prop="selfproduce">
                      <el-input v-model="formData.selfproduce" placeholder="请输入供货渠道" />
                    </el-form-item>
                    <el-form-item label="产品代码" prop="prodcode">
                      <el-input v-model="formData.prodcode" placeholder="请输入产品代码" />
                    </el-form-item>
                    <el-form-item label="预设品级" prop="matrlGrade">
                      <el-input v-model="formData.matrlGrade" placeholder="请输入预设品级" />
                    </el-form-item>
                    <el-form-item label="预设库栋代号" prop="locno">
                      <el-input v-model="formData.locno" placeholder="请输入预设库栋代号" />
                    </el-form-item>
                    <el-form-item label="预设储位代号" prop="childlocno">
                      <el-input v-model="formData.childlocno" placeholder="请输入预设储位代号" />
                    </el-form-item>
                    <el-form-item label="预设排层位代号" prop="layer">
                      <el-input v-model="formData.layer" placeholder="请输入预设排层位代号" />
                    </el-form-item>
                    <el-form-item label="是否有替代料" prop="ownreplacement">
                      <el-select v-model="formData.ownreplacement" placeholder="请选择是否有替代料">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="最小订购量" prop="minorderqty">
                      <el-input v-model="formData.minorderqty" placeholder="请输入最小订购量" />
                    </el-form-item>
                    <el-form-item label="是否MPS主件" prop="mpschk">
                      <el-select v-model="formData.mpschk" placeholder="请选择是否MPS主件">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="超收比例" prop="overrecvrate">
                      <el-input v-model="formData.overrecvrate" placeholder="请输入超收比例" />
                    </el-form-item>
                    <el-form-item label="状态码" prop="stus">
                      <el-input v-model="formData.stus" placeholder="请输入状态码" />
                    </el-form-item>
                    <el-form-item label="MRP采购交期提前天数" prop="mrppurdate">
                      <el-date-picker clearable v-model="formData.mrppurdate" type="date" value-format="timestamp" placeholder="选择MRP采购交期提前天数" />
                    </el-form-item>
                    <el-form-item label="是否为主营" prop="longcno">
                      <el-select v-model="formData.longcno" placeholder="请选择是否为主营">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否允许负帐交易" prop="lastcno">
                      <el-select v-model="formData.lastcno" placeholder="请选择是否允许负帐交易">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否允许负单价交易" prop="pchkstd">
                      <el-select v-model="formData.pchkstd" placeholder="请选择是否允许负单价交易">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="是否受开票日限制" prop="cchkstd">
                      <el-select v-model="formData.cchkstd" placeholder="请选择是否受开票日限制">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="标准水分" prop="stardwaterrate">
                      <el-input v-model="formData.stardwaterrate" placeholder="请输入标准水分" />
                    </el-form-item>
                    <el-form-item label="堆位标准水分" prop="pilestardwaterrate">
                      <el-input v-model="formData.pilestardwaterrate" placeholder="请输入堆位标准水分" />
                    </el-form-item>
                    <el-form-item label="国外运费率" prop="blenDrate">
                      <el-input v-model="formData.blenDrate" placeholder="请输入国外运费率" />
                    </el-form-item>
                    <el-form-item label="国内运杂费率" prop="dmblendrate">
                      <el-input v-model="formData.dmblendrate" placeholder="请输入国内运杂费率" />
                    </el-form-item>
                    <el-form-item label="标准成本单价" prop="stdunitcost">
                      <el-input v-model="formData.stdunitcost" placeholder="请输入标准成本单价" />
                    </el-form-item>
                    <el-form-item label="移动平均成本单价" prop="avgunitcost">
                      <el-input v-model="formData.avgunitcost" placeholder="请输入移动平均成本单价" />
                    </el-form-item>
                    <el-form-item label="标准成本单价2" prop="stdunitcost2">
                      <el-input v-model="formData.stdunitcost2" placeholder="请输入标准成本单价2" />
                    </el-form-item>
                    <el-form-item label="标准价生效日" prop="stdunitcostdate">
                      <el-date-picker clearable v-model="formData.stdunitcostdate" type="date" value-format="timestamp" placeholder="选择标准价生效日" />
                    </el-form-item>
                    <el-form-item label="标准价生效日" prop="stdunitcostdate2">
                      <el-input v-model="formData.stdunitcostdate2" placeholder="请输入标准价生效日" />
                    </el-form-item>
                    <el-form-item label="产品规范" prop="specno">
                      <el-input v-model="formData.specno" placeholder="请输入产品规范" />
                    </el-form-item>
                    <el-form-item label="质量检测标准" prop="qcsno">
                      <el-input v-model="formData.qcsno" placeholder="请输入质量检测标准" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注" />
                    </el-form-item>
      </el-form>
              <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialDetailApi from '@/api/basic/materialdetail';
      export default {
    name: "MaterialDetailForm",
    components: {
                    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            id: undefined,
                            compid: undefined,
                            matrlno: undefined,
                            matrlIndexid: undefined,
                            invenTorytype: undefined,
                            purchaser: undefined,
                            predays: undefined,
                            daysofpur: undefined,
                            rop: undefined,
                            minstkqty: undefined,
                            maxstkqty: undefined,
                            minconsumpqty: undefined,
                            meuseqty: undefined,
                            avgdailyqty: undefined,
                            yearuseqty: undefined,
                            availabydays: undefined,
                            isidle: undefined,
                            lotchk: undefined,
                            pricechk: undefined,
                            expirechk: undefined,
                            fixed: undefined,
                            fixedassetstype: undefined,
                            selfproduce: undefined,
                            prodcode: undefined,
                            matrlGrade: undefined,
                            locno: undefined,
                            childlocno: undefined,
                            layer: undefined,
                            ownreplacement: undefined,
                            minorderqty: undefined,
                            mpschk: undefined,
                            overrecvrate: undefined,
                            stus: undefined,
                            mrppurdate: undefined,
                            longcno: undefined,
                            lastcno: undefined,
                            pchkstd: undefined,
                            cchkstd: undefined,
                            stardwaterrate: undefined,
                            pilestardwaterrate: undefined,
                            blenDrate: undefined,
                            dmblendrate: undefined,
                            stdunitcost: undefined,
                            avgunitcost: undefined,
                            stdunitcost2: undefined,
                            stdunitcostdate: undefined,
                            stdunitcostdate2: undefined,
                            specno: undefined,
                            qcsno: undefined,
                            remark: undefined,
        },
        // 表单校验
        formRules: {
                        matrlno: [{ required: true, message: '料号不能为空', trigger: 'blur' }],
        },
                        };
    },
    methods: {
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialDetailApi.getMaterialDetail(id);
            this.formData = res.data;
            this.title = "修改料号详细信息";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增料号详细信息";
              },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await MaterialDetailApi.updateMaterialDetail(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialDetailApi.createMaterialDetail(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
                      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            compid: undefined,
                            matrlno: undefined,
                            matrlIndexid: undefined,
                            invenTorytype: undefined,
                            purchaser: undefined,
                            predays: undefined,
                            daysofpur: undefined,
                            rop: undefined,
                            minstkqty: undefined,
                            maxstkqty: undefined,
                            minconsumpqty: undefined,
                            meuseqty: undefined,
                            avgdailyqty: undefined,
                            yearuseqty: undefined,
                            availabydays: undefined,
                            isidle: undefined,
                            lotchk: undefined,
                            pricechk: undefined,
                            expirechk: undefined,
                            fixed: undefined,
                            fixedassetstype: undefined,
                            selfproduce: undefined,
                            prodcode: undefined,
                            matrlGrade: undefined,
                            locno: undefined,
                            childlocno: undefined,
                            layer: undefined,
                            ownreplacement: undefined,
                            minorderqty: undefined,
                            mpschk: undefined,
                            overrecvrate: undefined,
                            stus: undefined,
                            mrppurdate: undefined,
                            longcno: undefined,
                            lastcno: undefined,
                            pchkstd: undefined,
                            cchkstd: undefined,
                            stardwaterrate: undefined,
                            pilestardwaterrate: undefined,
                            blenDrate: undefined,
                            dmblendrate: undefined,
                            stdunitcost: undefined,
                            avgunitcost: undefined,
                            stdunitcost2: undefined,
                            stdunitcostdate: undefined,
                            stdunitcostdate2: undefined,
                            specno: undefined,
                            qcsno: undefined,
                            remark: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
