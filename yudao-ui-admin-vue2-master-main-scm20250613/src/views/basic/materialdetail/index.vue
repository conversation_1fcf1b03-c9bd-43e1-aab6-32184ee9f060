<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="公司别" prop="compid">-->
<!--        <el-input v-model="queryParams.compid" placeholder="请输入公司别" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
      <el-form-item label="料号" prop="matrlno">
        <el-input v-model="queryParams.matrlno" placeholder="请输入料号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
<!--      <el-form-item label="料号分类" prop="matrlIndexid">-->
<!--        <el-input v-model="queryParams.matrlIndexid" placeholder="请输入料号分类" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="品别代码" prop="invenTorytype">-->
<!--        <el-select v-model="queryParams.invenTorytype" placeholder="请选择品别代码" clearable size="small">-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="采购员" prop="purchaser">-->
<!--        <el-input v-model="queryParams.purchaser" placeholder="请输入采购员" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="采购前置天数" prop="predays">-->
<!--        <el-input v-model="queryParams.predays" placeholder="请输入采购前置天数" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="物料采购天数" prop="daysofpur">-->
<!--        <el-input v-model="queryParams.daysofpur" placeholder="请输入物料采购天数" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="请购点" prop="rop">-->
<!--        <el-input v-model="queryParams.rop" placeholder="请输入请购点" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="安全存量" prop="minstkqty">-->
<!--        <el-input v-model="queryParams.minstkqty" placeholder="请输入安全存量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="最大库存量" prop="maxstkqty">-->
<!--        <el-input v-model="queryParams.maxstkqty" placeholder="请输入最大库存量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="消耗定额" prop="minconsumpqty">-->
<!--        <el-input v-model="queryParams.minconsumpqty" placeholder="请输入消耗定额" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="设备用量" prop="meuseqty">-->
<!--        <el-input v-model="queryParams.meuseqty" placeholder="请输入设备用量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="平均日用量" prop="avgdailyqty">-->
<!--        <el-input v-model="queryParams.avgdailyqty" placeholder="请输入平均日用量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="预计年用量" prop="yearuseqty">-->
<!--        <el-input v-model="queryParams.yearuseqty" placeholder="请输入预计年用量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="有效日数" prop="availabydays">-->
<!--        <el-input v-model="queryParams.availabydays" placeholder="请输入有效日数" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否呆料" prop="isidle">-->
<!--        <el-select v-model="queryParams.isidle" placeholder="请选择是否呆料" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否批号管理" prop="lotchk">-->
<!--        <el-select v-model="queryParams.lotchk" placeholder="请选择是否批号管理" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否批价管理" prop="pricechk">-->
<!--        <el-select v-model="queryParams.pricechk" placeholder="请选择是否批价管理" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否时效控管" prop="expirechk">-->
<!--        <el-select v-model="queryParams.expirechk" placeholder="请选择是否时效控管" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否固定资产" prop="fixed">-->
<!--        <el-select v-model="queryParams.fixed" placeholder="请选择是否固定资产" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="固定资产大类" prop="fixedassetstype">-->
<!--        <el-input v-model="queryParams.fixedassetstype" placeholder="请输入固定资产大类" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="供货渠道" prop="selfproduce">-->
<!--        <el-input v-model="queryParams.selfproduce" placeholder="请输入供货渠道" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="产品代码" prop="prodcode">-->
<!--        <el-input v-model="queryParams.prodcode" placeholder="请输入产品代码" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="预设品级" prop="matrlGrade">-->
<!--        <el-input v-model="queryParams.matrlGrade" placeholder="请输入预设品级" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="预设库栋代号" prop="locno">-->
<!--        <el-input v-model="queryParams.locno" placeholder="请输入预设库栋代号" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="预设储位代号" prop="childlocno">-->
<!--        <el-input v-model="queryParams.childlocno" placeholder="请输入预设储位代号" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="预设排层位代号" prop="layer">-->
<!--        <el-input v-model="queryParams.layer" placeholder="请输入预设排层位代号" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否有替代料" prop="ownreplacement">-->
<!--        <el-select v-model="queryParams.ownreplacement" placeholder="请选择是否有替代料" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="最小订购量" prop="minorderqty">-->
<!--        <el-input v-model="queryParams.minorderqty" placeholder="请输入最小订购量" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否MPS主件" prop="mpschk">-->
<!--        <el-select v-model="queryParams.mpschk" placeholder="请选择是否MPS主件" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="超收比例" prop="overrecvrate">-->
<!--        <el-input v-model="queryParams.overrecvrate" placeholder="请输入超收比例" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="状态码" prop="stus">-->
<!--        <el-input v-model="queryParams.stus" placeholder="请输入状态码" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="MRP采购交期提前天数" prop="mrppurdate">-->
<!--        <el-date-picker v-model="queryParams.mrppurdate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
<!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否为主营" prop="longcno">-->
<!--        <el-select v-model="queryParams.longcno" placeholder="请选择是否为主营" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否允许负帐交易" prop="lastcno">-->
<!--        <el-select v-model="queryParams.lastcno" placeholder="请选择是否允许负帐交易" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否允许负单价交易" prop="pchkstd">-->
<!--        <el-select v-model="queryParams.pchkstd" placeholder="请选择是否允许负单价交易" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否受开票日限制" prop="cchkstd">-->
<!--        <el-select v-model="queryParams.cchkstd" placeholder="请选择是否受开票日限制" clearable size="small">-->
<!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
<!--                       :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="标准水分" prop="stardwaterrate">-->
<!--        <el-input v-model="queryParams.stardwaterrate" placeholder="请输入标准水分" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="堆位标准水分" prop="pilestardwaterrate">-->
<!--        <el-input v-model="queryParams.pilestardwaterrate" placeholder="请输入堆位标准水分" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="国外运费率" prop="blenDrate">-->
<!--        <el-input v-model="queryParams.blenDrate" placeholder="请输入国外运费率" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="国内运杂费率" prop="dmblendrate">-->
<!--        <el-input v-model="queryParams.dmblendrate" placeholder="请输入国内运杂费率" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="标准成本单价" prop="stdunitcost">-->
<!--        <el-input v-model="queryParams.stdunitcost" placeholder="请输入标准成本单价" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="移动平均成本单价" prop="avgunitcost">-->
<!--        <el-input v-model="queryParams.avgunitcost" placeholder="请输入移动平均成本单价" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="标准成本单价2" prop="stdunitcost2">-->
<!--        <el-input v-model="queryParams.stdunitcost2" placeholder="请输入标准成本单价2" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="标准价生效日" prop="stdunitcostdate">-->
<!--        <el-date-picker v-model="queryParams.stdunitcostdate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
<!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="标准价生效日" prop="stdunitcostdate2">-->
<!--        <el-input v-model="queryParams.stdunitcostdate2" placeholder="请输入标准价生效日" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="产品规范" prop="specno">-->
<!--        <el-input v-model="queryParams.specno" placeholder="请输入产品规范" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="质量检测标准" prop="qcsno">-->
<!--        <el-input v-model="queryParams.qcsno" placeholder="请输入质量检测标准" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="备注" prop="remark">-->
<!--        <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
<!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['basic:material-detail:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['basic:material-detail:export']">导出</el-button>
      </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

            <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" highlight-current-row>
            <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="公司别" align="center" prop="compid" />
      <el-table-column label="料号" align="center" prop="matrlno" />
      <el-table-column label="料号分类" align="center" prop="matrlIndexid" />
      <el-table-column label="品别代码" align="center" prop="invenTorytype" />
      <el-table-column label="采购员" align="center" prop="purchaser" />
      <el-table-column label="采购前置天数" align="center" prop="predays" />
      <el-table-column label="物料采购天数" align="center" prop="daysofpur" />
      <el-table-column label="请购点" align="center" prop="rop" />
      <el-table-column label="安全存量" align="center" prop="minstkqty" />
      <el-table-column label="最大库存量" align="center" prop="maxstkqty" />
      <el-table-column label="消耗定额" align="center" prop="minconsumpqty" />
      <el-table-column label="设备用量" align="center" prop="meuseqty" />
      <el-table-column label="平均日用量" align="center" prop="avgdailyqty" />
      <el-table-column label="预计年用量" align="center" prop="yearuseqty" />
      <el-table-column label="有效日数" align="center" prop="availabydays" />
      <el-table-column label="是否呆料" align="center" prop="isidle">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.isidle" />
        </template>
      </el-table-column>
      <el-table-column label="是否批号管理" align="center" prop="lotchk">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.lotchk" />
        </template>
      </el-table-column>
      <el-table-column label="是否批价管理" align="center" prop="pricechk">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.pricechk" />
        </template>
      </el-table-column>
      <el-table-column label="是否时效控管" align="center" prop="expirechk">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.expirechk" />
        </template>
      </el-table-column>
      <el-table-column label="是否固定资产" align="center" prop="fixed">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.fixed" />
        </template>
      </el-table-column>
      <el-table-column label="固定资产大类" align="center" prop="fixedassetstype" />
      <el-table-column label="供货渠道" align="center" prop="selfproduce" />
      <el-table-column label="产品代码" align="center" prop="prodcode" />
      <el-table-column label="预设品级" align="center" prop="matrlGrade" />
      <el-table-column label="预设库栋代号" align="center" prop="locno" />
      <el-table-column label="预设储位代号" align="center" prop="childlocno" />
      <el-table-column label="预设排层位代号" align="center" prop="layer" />
      <el-table-column label="是否有替代料" align="center" prop="ownreplacement">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.ownreplacement" />
        </template>
      </el-table-column>
      <el-table-column label="最小订购量" align="center" prop="minorderqty" />
      <el-table-column label="是否MPS主件" align="center" prop="mpschk">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.mpschk" />
        </template>
      </el-table-column>
      <el-table-column label="超收比例" align="center" prop="overrecvrate" />
      <el-table-column label="状态码" align="center" prop="stus" />
      <el-table-column label="MRP采购交期提前天数" align="center" prop="mrppurdate" />
      <el-table-column label="是否为主营" align="center" prop="longcno">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.longcno" />
        </template>
      </el-table-column>
      <el-table-column label="是否允许负帐交易" align="center" prop="lastcno">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.lastcno" />
        </template>
      </el-table-column>
      <el-table-column label="是否允许负单价交易" align="center" prop="pchkstd">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.pchkstd" />
        </template>
      </el-table-column>
      <el-table-column label="是否受开票日限制" align="center" prop="cchkstd">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.cchkstd" />
        </template>
      </el-table-column>
      <el-table-column label="标准水分" align="center" prop="stardwaterrate" />
      <el-table-column label="堆位标准水分" align="center" prop="pilestardwaterrate" />
      <el-table-column label="国外运费率" align="center" prop="blenDrate" />
      <el-table-column label="国内运杂费率" align="center" prop="dmblendrate" />
      <el-table-column label="标准成本单价" align="center" prop="stdunitcost" />
      <el-table-column label="移动平均成本单价" align="center" prop="avgunitcost" />
      <el-table-column label="标准成本单价2" align="center" prop="stdunitcost2" />
      <el-table-column label="标准价生效日" align="center" prop="stdunitcostdate" />
      <el-table-column label="标准价生效日" align="center" prop="stdunitcostdate2" />
      <el-table-column label="产品规范" align="center" prop="specno" />
      <el-table-column label="质量检测标准" align="center" prop="qcsno" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id)"
                     v-hasPermi="['basic:material-detail:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['basic:material-detail:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <MaterialDetailForm ref="formRef" @success="getList" />
    </div>
</template>

<script>
import * as MaterialDetailApi from '@/api/basic/materialdetail';
import MaterialDetailForm from './MaterialDetailForm.vue';
export default {
  name: "MaterialDetail",
  components: {
          MaterialDetailForm,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
              // 总条数
        total: 0,
      // 料号详细信息列表
      list: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryParams: {
                    pageNo: 1,
            pageSize: 10,
        compid: null,
        matrlno: null,
        matrlIndexid: null,
        invenTorytype: null,
        purchaser: null,
        predays: null,
        daysofpur: null,
        rop: null,
        minstkqty: null,
        maxstkqty: null,
        minconsumpqty: null,
        meuseqty: null,
        avgdailyqty: null,
        yearuseqty: null,
        availabydays: null,
        isidle: null,
        lotchk: null,
        pricechk: null,
        expirechk: null,
        fixed: null,
        fixedassetstype: null,
        selfproduce: null,
        prodcode: null,
        matrlGrade: null,
        locno: null,
        childlocno: null,
        layer: null,
        ownreplacement: null,
        minorderqty: null,
        mpschk: null,
        overrecvrate: null,
        stus: null,
        mrppurdate: [],
        longcno: null,
        lastcno: null,
        pchkstd: null,
        cchkstd: null,
        stardwaterrate: null,
        pilestardwaterrate: null,
        blenDrate: null,
        dmblendrate: null,
        stdunitcost: null,
        avgunitcost: null,
        stdunitcost2: null,
        stdunitcostdate: [],
        stdunitcostdate2: null,
        specno: null,
        qcsno: null,
        remark: null,
        createTime: [],
      },
            };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
      this.loading = true;
              const res = await MaterialDetailApi.getMaterialDetailPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id) {
      this.$refs["formRef"].open(id);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除料号详细信息编号为"' + id + '"的数据项?')
      try {
       await MaterialDetailApi.deleteMaterialDetail(id);
       await this.getList();
       this.$modal.msgSuccess("删除成功");
      } catch {}
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有料号详细信息数据项?');
      try {
        this.exportLoading = true;
        const data = await MaterialDetailApi.exportMaterialDetailExcel(this.queryParams);
        this.$download.excel(data, '料号详细信息.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
              }
};
</script>
