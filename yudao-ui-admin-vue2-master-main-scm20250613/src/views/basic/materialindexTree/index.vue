<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="项目" prop="project">-->
<!--        <el-input v-model="queryParams.project" placeholder="请输入项目" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="公司别" prop="compid">-->
<!--        <el-input v-model="queryParams.compid" placeholder="请输入公司别" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
      <el-form-item label="料号索引" prop="matrlIndex">
        <el-input v-model="queryParams.matrlIndex" placeholder="请输入料号索引" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="料号索引关系建立" prop="matrlIndexrel">
        <el-input v-model="queryParams.matrlIndexrel" placeholder="请输入料号索引关系建立" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="料号分类阶层数" prop="matrlIndexnum">
        <el-input v-model="queryParams.matrlIndexnum" placeholder="请输入料号分类阶层数" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
<!--      <el-form-item label="备注" prop="remark">-->
<!--        <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建者" prop="creator">-->
<!--        <el-input v-model="queryParams.creator" placeholder="请输入创建者" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
<!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="更新者" prop="updater">-->
<!--        <el-input v-model="queryParams.updater" placeholder="请输入更新者" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="更新时间" prop="updateTime">-->
<!--        <el-date-picker v-model="queryParams.updateTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
<!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="父ID" prop="parentId">-->
<!--        <el-input v-model="queryParams.parentId" placeholder="请输入父ID" clearable @keyup.enter.native="handleQuery"/>-->
<!--      </el-form-item>-->
      <el-form-item label="料号分类代码" prop="matrlIndexid">
        <el-input v-model="queryParams.matrlIndexid" placeholder="请输入料号分类代码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="品别代码" prop="invenToryType">
        <el-select v-model="queryParams.invenToryType" placeholder="请选择品别代码" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="料号索引说明" prop="matrlIndexdesc">
        <el-input v-model="queryParams.matrlIndexdesc" placeholder="请输入料号索引说明" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['basic:material-index-tree:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['basic:material-index-tree:export']">导出</el-button>
      </el-col>
                  <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">
              展开/折叠
            </el-button>
          </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

            <el-table
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        v-if="refreshTable"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
            <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="项目" align="center" prop="project" />
      <el-table-column label="公司别" align="center" prop="compid" />
      <el-table-column label="料号索引" align="center" prop="matrlIndex" />
      <el-table-column label="料号索引关系建立" align="center" prop="matrlIndexrel" />
      <el-table-column label="料号分类阶层数" align="center" prop="matrlIndexnum" />
<!--      <el-table-column label="备注" align="center" prop="remark" />-->
<!--      <el-table-column label="创建者" align="center" prop="creator" />-->
<!--      <el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
<!--        <template v-slot="scope">-->
<!--          <span>{{ parseTime(scope.row.createTime) }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="更新者" align="center" prop="updater" />-->
<!--      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">-->
<!--        <template v-slot="scope">-->
<!--          <span>{{ parseTime(scope.row.updateTime) }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="父ID" align="center" prop="parentId" />
      <el-table-column label="料号分类代码" align="center" prop="matrlIndexid" />
      <el-table-column label="品别代码" align="center" prop="invenToryType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenToryType" />
        </template>
      </el-table-column>
      <el-table-column label="料号索引说明" align="center" prop="matrlIndexdesc" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id)"
                     v-hasPermi="['basic:material-index-tree:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['basic:material-index-tree:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 对话框(添加 / 修改) -->
    <MaterialIndexTreeForm ref="formRef" @success="getList" />
    </div>
</template>

<script>
import * as MaterialIndexTreeApi from '@/api/basic/materialindexTree';
import MaterialIndexTreeForm from './MaterialIndexTreeForm.vue';
export default {
  name: "MaterialIndexTree",
  components: {
          MaterialIndexTreeForm,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
            // 物料料号索引列表
      list: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryParams: {
                project: null,
        compid: null,
        matrlIndex: null,
        matrlIndexrel: null,
        matrlIndexnum: null,
        remark: null,
        creator: null,
        createTime: [],
        updater: null,
        updateTime: [],
        parentId: null,
        matrlIndexid: null,
        invenToryType: null,
        matrlIndexdesc: null,
      },
            };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      try {
      this.loading = true;
             const res = await MaterialIndexTreeApi.getMaterialIndexTreeList(this.queryParams);
        console.log('----res：', res)
       this.list = this.handleTree(res.data, 'id', 'parentId');
        console.log('----this.list：', this.list)
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id) {
      this.$refs["formRef"].open(id);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除物料料号索引编号为"' + id + '"的数据项?')
      try {
       await MaterialIndexTreeApi.deleteMaterialIndexTree(id);
       await this.getList();
       this.$modal.msgSuccess("删除成功");
      } catch {}
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有物料料号索引数据项?');
      try {
        this.exportLoading = true;
        const data = await MaterialIndexTreeApi.exportMaterialIndexTreeExcel(this.queryParams);
        this.$download.excel(data, '物料料号索引.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
                    /** 展开/折叠操作 */
        toggleExpandAll() {
          this.refreshTable = false
          this.isExpandAll = !this.isExpandAll
          this.$nextTick(function () {
            this.refreshTable = true
          })
        }
  }
};
</script>
