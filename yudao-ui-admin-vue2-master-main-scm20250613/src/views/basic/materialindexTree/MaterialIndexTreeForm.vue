<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
                    <el-form-item label="项目" prop="project">
                      <el-input v-model="formData.project" placeholder="请输入项目" />
                    </el-form-item>
                    <el-form-item label="公司别" prop="compid">
                      <el-input v-model="formData.compid" placeholder="请输入公司别" />
                    </el-form-item>
                    <el-form-item label="料号索引" prop="matrlIndex">
                      <el-input v-model="formData.matrlIndex" placeholder="请输入料号索引" />
                    </el-form-item>
                    <el-form-item label="料号索引关系建立" prop="matrlIndexrel">
                      <el-input v-model="formData.matrlIndexrel" placeholder="请输入料号索引关系建立" />
                    </el-form-item>
                    <el-form-item label="料号分类阶层数" prop="matrlIndexnum">
                      <el-input v-model="formData.matrlIndexnum" placeholder="请输入料号分类阶层数" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="formData.remark" placeholder="请输入备注" />
                    </el-form-item>
                    <el-form-item label="父ID" prop="parentId">
                      <TreeSelect
                        v-model="formData.parentId"
                        :options="materialIndexTreeTree"
                        :normalizer="normalizer"
                        placeholder="请选择父ID"
                      />
                    </el-form-item>
                    <el-form-item label="料号分类代码" prop="matrlIndexid">
                      <el-input v-model="formData.matrlIndexid" placeholder="请输入料号分类代码" />
                    </el-form-item>
                    <el-form-item label="品别代码" prop="invenToryType">
                      <el-select v-model="formData.invenToryType" placeholder="请选择品别代码">
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                                       :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="料号索引说明" prop="matrlIndexdesc">
                      <el-input v-model="formData.matrlIndexdesc" placeholder="请输入料号索引说明" />
                    </el-form-item>
      </el-form>
              <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialIndexTreeApi from '@/api/basic/materialindexTree';
    import TreeSelect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    export default {
    name: "MaterialIndexTreeForm",
    components: {
                  TreeSelect,
            },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            project: undefined,
                            compid: undefined,
                            matrlIndex: undefined,
                            matrlIndexrel: undefined,
                            matrlIndexnum: undefined,
                            remark: undefined,
                            parentId: undefined,
                            matrlIndexid: undefined,
                            invenToryType: undefined,
                            matrlIndexdesc: undefined,
        },
        // 表单校验
        formRules: {
        },
                       materialIndexTreeTree: [], // 树形结构
              };
    },
    methods: {
      /** 打开弹窗 */
     async open(id) {
        this.dialogVisible = true;
        this.reset();
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialIndexTreeApi.getMaterialIndexTree(id);
            this.formData = res.data;
            this.title = "修改物料料号索引";
          } finally {
            this.formLoading = false;
          }
        }
        this.title = "新增物料料号索引";
                await this.getMaterialIndexTreeTree();
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
                  this.formLoading = true;
        try {
          const data = this.formData;
                  // 修改的提交
          if (data.id) {
            await MaterialIndexTreeApi.updateMaterialIndexTree(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialIndexTreeApi.createMaterialIndexTree(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
                  /** 获得物料料号索引树 */
         async getMaterialIndexTreeTree() {
            this.materialIndexTreeTree = [];
            const res = await MaterialIndexTreeApi.getMaterialIndexTreeList();
            const root = { id: 0, matrlIndexid: '顶级物料料号索引', children: [] };
            root.children = this.handleTree(res.data, 'id', 'parentId')
            this.materialIndexTreeTree.push(root)
                    console.log('materialIndexTreeTree', this.materialIndexTreeTree)
          },
                  /** 转换物料料号索引数据结构 */
          normalizer(node) {
            if (node.children && !node.children.length) {
              delete node.children;
            }
                return {
                  id: node.id,
                  label: node['matrlIndexid'],
                  children: node.children
                };
          },
      /** 表单重置 */
      reset() {
        this.formData = {
                            project: undefined,
                            compid: undefined,
                            matrlIndex: undefined,
                            matrlIndexrel: undefined,
                            matrlIndexnum: undefined,
                            remark: undefined,
                            parentId: undefined,
                            matrlIndexid: undefined,
                            invenToryType: undefined,
                            matrlIndexdesc: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
