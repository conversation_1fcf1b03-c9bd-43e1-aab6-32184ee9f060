<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" @click="print">打印</el-button>
      </el-col>
    </el-row>
    <div id="print-section">
      <el-row :gutter="20">
        <div v-for="(field, index) in items" :key="field.qrName">
          <el-col :span="12" :xs="24">
            <!-- 二维码容器 -->
            <qrcode-vue
              :value="field.code"
              :size="256"
              tag="svg"
              ref="qrcode">
            </qrcode-vue>
            <h1>{{field.code}}</h1>
          </el-col>
        </div>
      </el-row>
    </div>
  </div>
</template>
<script>
  import QrcodeVue from 'qrcode.vue';

  export default {
    name: "qrStorage",
    components: {
      QrcodeVue
    },
    data() {
      return {
        items: [],
      };
    },
    created() {
      // console.log("data:", this.$route.query.data)
      const data = this.$route.query.data.split(",");
          this.items = data.map(str => {
            const [code, name] = str.split("|");
            return { code, name };
          });
    },
    methods: {
      async print(){
        window.print();
      }
    }
  };
</script>
<style scoped>
  /* 打印样式 */
  @media print {
    body * {
      visibility: hidden; /* 隐藏所有内容 */
    }
    #print-section, #print-section * {
      visibility: visible; /* 仅显示二维码容器 */
    }
    #print-section {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      text-align: center;
    }
  }
</style>
