<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <!--                    <el-form-item label="父ID" prop="parentId">-->
        <!--                      <el-input v-model="formData.parentId" placeholder="请输入父ID" />-->
        <!--                    </el-form-item>-->
        <!--                    <el-form-item label="项目" prop="project">-->
        <!--                      <el-input v-model="formData.project" placeholder="请输入项目" />-->
        <!--                    </el-form-item>-->
        <!--                    <el-form-item label="公司别" prop="compid">-->
        <!--                      <el-input v-model="formData.compid" placeholder="请输入公司别" />-->
        <!--                    </el-form-item>-->
        <!--                    <el-form-item label="系统编号" prop="systemid">-->
        <!--                      <el-input v-model="formData.systemid" placeholder="请输入系统编号" />-->
        <!--                    </el-form-item>-->
        <!--        <el-form-item label="储位代码" prop="layerid">-->
        <!--          <el-input v-model="formData.layerid" placeholder="请输入储位代码"/>-->
        <!--        </el-form-item>-->
        <el-form-item v-if="!this.layerindex" label="储位索引" prop="layerindex">
<!--          <el-input v-model="formData.layerindex" placeholder="请输入储位索引"/>-->
          <el-row :gutter="0">
            <el-col :span="8">
              <el-input v-if="this.layerid" v-model="this.layerid" :readonly="layerid"/>
            </el-col>
            <el-col :span="16">
              <el-input v-model="formData.layerindex" placeholder="请输入储位索引，例：01"/>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="!this.layerid" label="品别" prop="invenTorytype">
          <el-select v-model="formData.invenTorytype" placeholder="请选择品别">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="储位索引关系" prop="layerrel">-->
        <!--          <el-input v-model="formData.layerrel" placeholder="请输入储位索引关系"/>-->
        <!--        </el-form-item>-->
        <el-form-item label="储位说明" prop="layerdesc">
          <el-row :gutter="0">
            <el-col :span="8">
              <el-input v-if="this.layerdesc" v-model="this.layerdesc" :readonly="layerdesc"/>
            </el-col>
            <el-col :span="16">
              <el-input v-model="formData.layerdesc" placeholder="请输入储位说明"/>
            </el-col>
          </el-row>
        </el-form-item>
        <!--        <el-form-item label="计价厂别代码" prop="factoryno">-->
        <!--          <el-input v-model="formData.factoryno" placeholder="请输入计价厂别代码"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="储位阶层数" prop="layernum">-->
        <!--          <el-input v-model="formData.layernum" placeholder="请输入储位阶层数"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="料号" prop="matrlno">-->
        <!--          <el-input v-model="formData.matrlno" placeholder="请输入料号"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="保管员" prop="layerempno">-->
        <!--          <el-input v-model="formData.layerempno" placeholder="请输入保管员"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="保管单位" prop="ownerdeptno">-->
        <!--          <el-input v-model="formData.ownerdeptno" placeholder="请输入保管单位"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="物权公司" prop="ownercompid">-->
        <!--          <el-input v-model="formData.ownercompid" placeholder="请输入物权公司"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="备注" prop="remark">-->
        <!--          <el-input v-model="formData.remark" placeholder="请输入备注"/>-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as StorageApi from '@/api/basic/storage';
import * as MaterialIndexApi from "@/api/basic/materialindex";

export default {
  name: "StorageForm",
  components: {},
  data() {
    return {
      // 弹出层标题
      dialogTitle: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
      formLoading: false,
      // 表单参数
      formData: {
        id: undefined,
        parentId: undefined,
        project: undefined,
        compid: undefined,
        systemid: undefined,
        layerid: undefined,
        invenTorytype: undefined,
        layerindex: undefined,
        layerrel: undefined,
        layerdesc: undefined,
        factoryno: undefined,
        layernum: undefined,
        matrlno: undefined,
        layerempno: undefined,
        ownerdeptno: undefined,
        ownercompid: undefined,
        remark: undefined,
        remark1: undefined,
      },
      // 表单校验
      formRules: {
        invenTorytype: [
          {required: true, message: '请选择品别', trigger: 'blur'}
        ],
        layerindex: [
          {required: true, message: '储位索引不能为空', trigger: 'blur'},
          {pattern: /^[a-zA-Z0-9]{2}$/, message: '储位索引必须是两位数字或字母', trigger: 'blur'}
        ],
        layerdesc: [
          {required: true, message: '储位说明不能为空', trigger: 'blur'}
        ],
      },
      // 主表id
      id: undefined,
      layerid: undefined,
      layerdesc: undefined,
      layerindex: undefined,
    };
  },
  methods: {
    /** 打开弹窗 */
    async open(id, type) {
      console.log('id', id)
      console.log('type', type)
      this.id = id;
      this.layerid = "";
      this.layerdesc = "";
      this.layerindex = "";
      this.dialogVisible = true;
      this.reset();
      if (type == 2) {
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await StorageApi.getStorage(id);
            this.formData = res.data;
            this.dialogTitle = "修改储位信息";

            this.layerid = " ";
            this.layerdesc = "";
            this.layerindex = " ";
          } finally {
            this.formLoading = false;
          }
        }
      } else if (type == 1) {
        if (id) {
          const res = await StorageApi.getStorage(id);
          this.layerid = res.data.layerid;
          this.layerdesc = res.data.layerdesc;
          this.formData.invenTorytype = res.data.invenTorytype;
          this.formData.layerrel = res.data.layerid;
          this.formData.layernum = parseInt(res.data.layernum, 10) + 1;
          this.formData.parentId = id;
          this.formData.remark1 = res.data.remark1;

          this.layerindex = "";
        }
        this.dialogTitle = "新增储位信息";
      }
    },
    /** 提交按钮 */
    async submitForm() {
      // 校验主表
      await this.$refs["formRef"].validate();
      this.formLoading = true;
      try {
        const data = this.formData;
        // 修改的提交
        if (data.id) {
          await StorageApi.updateStorage(data);
          this.$modal.msgSuccess("修改成功");
          this.dialogVisible = false;
          this.$emit('success');
          return;
        }
        // 添加的提交
        if (this.id) {
          this.formData.layerid = this.layerid + data.layerindex;
          this.formData.layerdesc = this.layerdesc + data.layerdesc;
        } else {
          this.formData.parentId = 0;
          this.formData.layernum = 1;
          this.formData.layerid = data.layerindex;
          this.formData.matrlIndexrel = null;
          this.formData.remark1 = this.formData.layerid;
        }
        // 添加的提交
        await StorageApi.createStorage(data);
        this.$modal.msgSuccess("新增成功");
        this.dialogVisible = false;
        this.$emit('success');
      } finally {
        this.formLoading = false;
      }
    },
    /** 表单重置 */
    reset() {
      this.formData = {
        id: undefined,
        parentId: undefined,
        project: undefined,
        compid: undefined,
        systemid: undefined,
        layerid: undefined,
        invenTorytype: undefined,
        layerindex: undefined,
        layerrel: undefined,
        layerdesc: undefined,
        factoryno: undefined,
        layernum: undefined,
        matrlno: undefined,
        layerempno: undefined,
        ownerdeptno: undefined,
        ownercompid: undefined,
        remark: undefined,
      };
      this.resetForm("formRef");
    }
  }
};
</script>
