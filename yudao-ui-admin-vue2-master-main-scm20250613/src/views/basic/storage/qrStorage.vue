<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" @click="print">打印</el-button>
      </el-col>
    </el-row>
    <div id="print-section">
      <el-row :gutter="20">
        <el-col v-for="(field, index) in items" :key="field.code" :span="8" :xs="24">
          <div class="qr-card">
            <qrcode-vue
              :value="field.code"
              :size="200"
              tag="svg"
              ref="qrcode"
            />
            <p class="qr-code-text">{{ field.code }}</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import QrcodeVue from 'qrcode.vue';

  export default {
    name: "qrStorage",
    components: {
      QrcodeVue
    },
    data() {
      return {
        items: [],
      };
    },
    created() {
      // console.log("data:", this.$route.query.data)
      const data = this.$route.query.data.split(",");
          this.items = data.map(str => {
            const [code, name] = str.split("|");
            return { code, name };
          });
    },
    methods: {
      async print(){
        window.print();
      }
    }
  };
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.qr-card {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  padding: 16px;
  margin: 10px auto;
  max-width: 300px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.qr-code-text {
  font-size: 16px;
  font-weight: bold;
  margin-top: 12px;
  color: #333;
}

.qr-name-text {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 打印样式 */
@media print {
  body * {
    visibility: hidden; /* 隐藏所有内容 */
  }
  #print-section, #print-section * {
    visibility: visible; /* 仅显示二维码容器 */
  }
  #print-section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    text-align: center;
  }
  .qr-card {
    page-break-inside: avoid;
    border: none;
    box-shadow: none;
    margin: 20px auto;
  }
}
</style>

