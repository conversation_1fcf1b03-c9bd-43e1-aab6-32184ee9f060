<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="5" :xs="24" style="border-right: 1px solid #ebebeb; background-color: #f2f5f8; padding: 5px; ">
        <div class="head-container">
          <el-input
            v-model="layerid"
            placeholder="请输入储位代码"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-button type="info" plain icon="el-icon-sort" size="mini" style="margin-bottom: 10px;width: 100%;"
                     @click="toggleExpandAll">
            <!--            {{ isExpandAll ? '折叠所有' : '展开所有' }}-->
            展开/折叠
          </el-button>
        </div>
        <div v-if="refreshTable" class="head-container" style="max-height: 65vh; overflow: auto;">
          <el-tree
            :data="storageTree"
            :props="defaultProps"
            accordion
            :default-expand-all="isExpandAll"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
            :render-content="renderContent"
          />
        </div>
      </el-col>

      <el-col :span="19" :xs="24">
        <!-- 搜索工作栏 -->
        <el-form :model="queryTreeParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
          <!--      <el-form-item label="父ID" prop="parentId">-->
          <!--        <el-input v-model="queryParams.parentId" placeholder="请输入父ID" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="项目" prop="project">-->
          <!--        <el-input v-model="queryParams.project" placeholder="请输入项目" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="公司别" prop="compid">-->
          <!--        <el-input v-model="queryParams.compid" placeholder="请输入公司别" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="系统编号" prop="systemid">-->
          <!--        <el-input v-model="queryParams.systemid" placeholder="请输入系统编号" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="储位代码" prop="layerid">-->
          <!--        <el-input v-model="queryParams.layerid" placeholder="请输入储位代码" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <el-form-item label="品别" prop="invenTorytype">
            <el-select v-model="queryTreeParams.invenTorytype" placeholder="请选择品别" clearable size="small">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <!--      <el-form-item label="储位索引" prop="layerindex">-->
          <!--        <el-input v-model="queryParams.layerindex" placeholder="请输入储位索引" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="储位索引关系" prop="layerrel">-->
          <!--        <el-input v-model="queryParams.layerrel" placeholder="请输入储位索引关系" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="储位说明" prop="layerdesc">-->
          <!--        <el-input v-model="queryParams.layerdesc" placeholder="请输入储位说明" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="计价厂别代码" prop="factoryno">-->
          <!--        <el-input v-model="queryParams.factoryno" placeholder="请输入计价厂别代码" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="储位阶层数" prop="layernum">-->
          <!--        <el-input v-model="queryParams.layernum" placeholder="请输入储位阶层数" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="料号" prop="matrlno">-->
          <!--        <el-input v-model="queryParams.matrlno" placeholder="请输入料号" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="保管员" prop="layerempno">-->
          <!--        <el-input v-model="queryParams.layerempno" placeholder="请输入保管员" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="保管单位" prop="ownerdeptno">-->
          <!--        <el-input v-model="queryParams.ownerdeptno" placeholder="请输入保管单位" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="物权公司" prop="ownercompid">-->
          <!--        <el-input v-model="queryParams.ownercompid" placeholder="请输入物权公司" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="备注" prop="remark">-->
          <!--        <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery"/>-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="创建时间" prop="createTime">-->
          <!--        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"-->
          <!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />-->
          <!--      </el-form-item>-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-if="isEquipSelected" type="primary" plain icon="el-icon-plus" size="mini"
                       @click="openForm(treeId,1)"
                       v-hasPermi="['basic:storage:create']">新增
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport"
                       v-hasPermi="['basic:storage:export']">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                       :loading="exportLoading"
                       v-hasPermi="['basic:storage:export']">导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-if="showSelection" type="warning" plain icon="el-icon-printer" size="mini"
                       @click="openQrcodeForm"
                       v-hasPermi="['basic:storage:create']">二维码
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                  @current-change="handleCurrentChange"
                  @selection-change="handleSelectionChange">
          <el-table-column v-if="showSelection" align="center" type="selection" fixed="left"/>
          <!--          <el-table-column label="ID" align="center" prop="id"/>-->
          <!--          <el-table-column label="父ID" align="center" prop="parentId"/>-->
          <!--          <el-table-column label="项目" align="center" prop="project"/>-->
          <!--          <el-table-column label="公司别" align="center" prop="compid"/>-->
          <!--          <el-table-column label="系统编号" align="center" prop="systemid"/>-->
          <el-table-column label="储位代码" align="center" prop="layerid"/>
          <el-table-column label="储位说明" align="center" prop="layerdesc"/>
          <el-table-column label="品别" align="center" prop="invenTorytype">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenTorytype"/>
            </template>
          </el-table-column>
          <el-table-column label="储位索引" align="center" prop="layerindex"/>
          <el-table-column label="储位索引关系" align="center" prop="layerrel"/>
          <el-table-column label="储位阶层数" align="center" prop="layernum" width="100px"/>
          <!--          <el-table-column label="计价厂别代码" align="center" prop="factoryno"/>-->
          <!--          <el-table-column label="料号" align="center" prop="matrlno"/>-->
          <!--          <el-table-column label="保管员" align="center" prop="layerempno"/>-->
          <!--          <el-table-column label="保管单位" align="center" prop="ownerdeptno"/>-->
          <!--          <el-table-column label="物权公司" align="center" prop="ownercompid"/>-->
          <!--          <el-table-column label="备注" align="center" prop="remark"/>-->
          <!--          <el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
          <!--            <template v-slot="scope">-->
          <!--              <span>{{ parseTime(scope.row.createTime) }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,2)"
                         v-hasPermi="['basic:storage:update']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['basic:storage:delete']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="getList"/>

      </el-col>
    </el-row>

    <!-- 对话框(添加 / 修改) -->
    <StorageForm ref="formRef" @success="getList"/>
    <StorageForm ref="formRef" @success="getTreeList"/>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                 :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
                 :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as StorageApi from '@/api/basic/storage';
import StorageForm from './StorageForm.vue';
import MaterialIndexForm from "@/views/basic/materialindex/MaterialIndexForm.vue";
import {getBaseHeader} from "@/utils/request";

export default {
  name: "Storage",
  components: {
    MaterialIndexForm,
    StorageForm,
  },
  data() {
    return {
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "储位信息导入",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/basic/storage/import'
      },
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 储位层级管理列表
      list: [],
      // 选择的树
      treeId: null,
      // 是否选择了设备树
      isEquipSelected: true,
      // 储位代码
      layerid: undefined,
      // 物料树选项
      storageTree: undefined,
      defaultProps: {
        children: "children",
        label: "layerid"
      },
      // 是否展开，默认全部展开
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 多选中行
      idItems: [],
      // 查询树参数
      queryTreeParams: {
        parentId: null,
        invenTorytype: null
      },
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        parentId: null,
        project: null,
        compid: null,
        systemid: null,
        layerid: null,
        invenTorytype: null,
        layerindex: null,
        layerrel: null,
        layerdesc: null,
        factoryno: null,
        layernum: null,
        matrlno: null,
        layerempno: null,
        ownerdeptno: null,
        ownercompid: null,
        remark: null,
        createTime: [],
      },
      // 控制多选框列的显示
      showSelection: false,
    };
  },
  watch: {
    // 根据料号分类代码
    layerid(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getTreeList();
  },
  methods: {
    openQrcodeForm() {
      let errorStatus = "";
      let ids = "";
      this.idItems.forEach((el, index) => {
        if (el.layernum != 4) {
          return
        } else {
          ids = ids + "," + el.layerid + "|" + el.layerdesc;
        }
      });
      if (ids.length == 0) {
        this.$modal.msgWarning("请选择数据！");
        return;
      }
      ids = ids.slice(1);
      // 使用 Vue Router 的 resolve 方法生成带查询参数的 URL
      const {href} = this.$router.resolve({
        name: 'qrStorage',
        query: {data: ids} // 查询参数
      });
      // 打开新页面
      window.open(href, '_blank');
    },
    /** 选中行操作 */
    handleCurrentChange(row) {
      this.currentRow = row;
      console.log("handleCurrentChange", row);
    },
    /** 多选中行操作 */
    handleSelectionChange(val) {
      this.idItems = val;
      console.log("handleSelectionChange", val)
    },
    // 切换展开/折叠
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    // 树显示内容编码加标签
    renderContent(h, {node, data, store}) {
      return h('span', {class: 'custom-tree-node'}, [
        h('span', node.label),
        h('span', {style: 'margin-left: 1px; color: #888;'}, "_" + data.layerdesc)
      ]);
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.layerid.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.isEquipSelected = false;
      this.showSelection = false;
      if (data.layernum != '4') {
        // 显示新增按钮
        this.isEquipSelected = true;
        this.treeId = data.id;
        this.queryParams.parentId = data.id;
        this.getList();
      }
      if (data.layernum == '3' || data.layernum == '4') {
        this.showSelection = true;
      }
    },
    /** 查询树 */
    async getTreeList() {
      try {
        this.loading = true;

        const resTree = await StorageApi.getStorageList(this.queryTreeParams);
        this.storageTree = [];
        // const root = { id: 0, matrlIndexid: '料号分类', children: [] };
        // root.children = this.handleTree(res.data.list, 'id', 'parentId');
        // this.storageTree.push(root)
        this.storageTree.push(...this.handleTree(resTree.data, "id"));

        // 列表查询第一层
        this.queryParams.parentId = 0;
        const res = await StorageApi.getStoragePage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;

        // console.log('----res：', res)
        // console.log('----storageTree', this.storageTree)
      } finally {
        this.loading = false;
      }
    },

    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        this.queryParams.invenTorytype = this.queryTreeParams.invenTorytype;
        const res = await StorageApi.getStoragePage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
      this.getTreeList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.treeId = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id, type) {
      this.$refs["formRef"].open(id, type);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除储位层级管理编号为"' + id + '"的数据项?')
      try {
        await StorageApi.deleteStorage(id);
        await this.getList();
        await this.getTreeList();
        this.$modal.msgSuccess("删除成功");
      } catch {
      }
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "储位信息导入";
      this.upload.open = true;
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出储位信息?');
      try {
        this.exportLoading = true;
        const data = await StorageApi.exportStorageExcel(this.queryParams);
        this.$download.excel(data, '储位信息.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
    // 上传提交
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return;
      }
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // 拼接提示语
      let data = response.data;
      let text = '新增成功数量：' + data.createDetails.length;
      for (const matrlno of data.createDetails) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + matrlno;
      }
      text += '<br />更新成功数量：' + data.updateDetails.length;
      for (const matrlno of data.updateDetails) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + matrlno;
      }
      text += '<br />导入失败数量：' + Object.keys(data.failureDetails).length;
      for (const matrlno in data.failureDetails) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + matrlno + '：' + data.failureDetails[matrlno];
      }
      this.$alert(text, "导入结果", { dangerouslyUseHTMLString: true });
      this.handleQuery();

    },
    /** 下载模板操作 */
    importTemplate() {
      StorageApi.importTemplate().then(response => {
        this.$download.excel(response, '储位信息导入模板.xlsx');
      });
    },
  }
};
</script>
