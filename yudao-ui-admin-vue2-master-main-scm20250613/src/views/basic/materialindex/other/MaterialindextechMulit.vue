<template>
  <div class="app-container">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" top="40%" width="80%" v-dialogDrag append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="createTech"
                     :disabled="formType=='view'">新增
          </el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                @selection-change="handleSelectionChange"
                :highlight-current-row="true">
        <el-table-column align="center" type="selection" fixed="left"/>
        <el-table-column label="叶类代码" align="center" prop="leafTypeCode" width="130" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.leafTypeCode" style="width:100px" disabled/>
          </template>
        </el-table-column>
        <el-table-column label="叶类名称" align="center" prop="leafTypeName" width="180" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.leafTypeName" style="width:150px" disabled/>
          </template>
        </el-table-column>
        <el-table-column label="字段代码" align="center" prop="tecLabel" width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.tecLabel" placeholder="提交后系统赋值" style="width:130px" disabled/>
          </template>
        </el-table-column>
        <el-table-column label="字段名称" align="center" prop="tecName" width="180" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.tecName" style="width:150px" :disabled="formType=='view'"/>
          </template>
        </el-table-column>
        <el-table-column label="序号" align="center" prop="seqId" width="120" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.seqId" style="width:90px" placeholder="提交后系统赋值" disabled/>
          </template>
        </el-table-column>
        <el-table-column label="是否必填" align="center" prop="required" width="150" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-select v-model="scope.row.required" placeholder="请选择是否必填" style="width: 120px"
                       :disabled="formType=='view'">
              <el-option v-for="dict in dictList"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="110" fixed="right">
          <template v-slot="scope">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleItemDelete(scope.row)"
                       :disabled="formType=='view'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <!--<pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"-->
                  <!--@pagination="getList"/>-->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading" v-if="this.formType=='edit'">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialIndexTechApi from '@/api/basic/materialindextech';
  import {getDictDatas} from "@/utils/dict";

  export default {
    name: "MaterialindextechMulit",
    components: {},
    data() {
      return {
        dictList: [],
        fromId: undefined,
        matrlIndexid: undefined,
        matrlIndexdesc: undefined,
        formType: undefined,
        // 遮罩层
        loading: false,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        list: [],
        idItems: [],
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          parentid: null,
          leafTypeCode: null,
          leafTypeName: null,
          tecLabel: null,
          tecName: null,
          seqId: null,
          required: null,
          createTime: [],
        },
      };
    },
    created() {
    },
    methods: {
      /** 查询列表 */
      async getList() {
        try {
          this.loading = true;
          const res = await MaterialIndexTechApi.getMaterialIndexTechPage(this.queryParams);
          this.list = res.data.list;
          this.total = res.data.total;
        } finally {
          this.loading = false;
        }
      },
      createTech() {
        let totalList = [];
        let seqMax = 0;
        this.list.forEach((el, index) => {
          totalList.push(el);
          if (el.seqId) {
            let seqInt = Number(el.seqId);
            if (seqInt > seqMax) {
              seqMax = seqInt;
            }
          }
        });
        seqMax = seqMax + 1;
        let seqStr = seqMax + "";
        if (seqStr.length == 1) {
          seqStr = "0" + seqStr;
        }
        const map = {
          parentid: this.fromId,
          leafTypeCode: this.matrlIndexid,
          leafTypeName: this.matrlIndexdesc,
          tecLabel: undefined,
          tecName: undefined,
          seqId: seqStr,
          required: "N",
        };
        totalList.push(map);
        this.list = totalList;
      },
      async handleItemDelete(row) {
        if(row && row.id){
          await this.$modal.confirm('是否确认删除序号为"' + row.seqId + '"的数据项?')
          try {
            await MaterialIndexTechApi.deleteMaterialIndexTech(row.id);
            this.$modal.msgSuccess("删除成功");
          } catch {
          }
        }
        for (let i = 0; i <= this.list.length - 1; i++) {
          if (this.list[i].seqId == row.seqId) {
            this.list.splice(i, 1);
            break;
          }
        }
        for (let i = 0; i <= this.idItems.length - 1; i++) {
          if (this.idItems[i].seqId == row.seqId) {
            this.idItems.splice(i, 1);
            break;
          }
        }
      },
      async open(type, matrlIndexid, matrlIndexdesc, id) {
        this.dialogVisible = true;
        this.formType = type;
        this.matrlIndexid = matrlIndexid;
        this.matrlIndexdesc = matrlIndexdesc;
        this.fromId = id;
        this.list = [];
        // 获取字典
        this.dictList = await getDictDatas('yes_no');
        // 获取已有的技术属性
        let queryParams2 = {
          parentid : id,
        };
        const res = await MaterialIndexTechApi.getMaterialIndexTechPageAll(queryParams2);
        this.list = res.data.list;

        if (type == 'view') {
          this.dialogTitle = "查看技术属性详情";
        } else {
          this.dialogTitle = "批量新增/修改技术属性";
        }
        this.resetForm("queryForm");
      },
      handleSelectionChange(val) {
        this.idItems = val;
      },
      async submitForm() {
        const items = [];
        let errortecName = "";
        this.idItems.forEach((el, index) => {
          if (!el.tecName || el.tecName == undefined || el.tecName == null) {
            errortecName = errortecName + "," + el.seqId;
          }
          var map = {
            id: el.id,
            parentid: el.parentid,
            leafTypeCode: el.leafTypeCode,
            leafTypeName: el.leafTypeName,
            tecLabel: el.tecLabel,
            tecName: el.tecName,
            seqId: el.seqId,
            required: el.required,
          };
          items.push(map);
        });
        if (!items || items.length == 0) {
          this.$modal.msgWarning("请选择数据后再确认！");
          return;
        }
        if (errortecName.length > 0) {
          errortecName = errortecName.slice(1);
          this.$modal.msgError("序号为【" + errortecName + "】的项次，字段名称不能为空！");
          return;
        }
        let params = {
          items: items
        };
        try {
          this.loading = true;
          await MaterialIndexTechApi.batchOperateMaterialIndexTech(params);
          this.$modal.msgSuccess("批量操作成功");
          this.dialogVisible = false;
        } finally {
          this.loading = false;
        }
      },
    }
  };
</script>
