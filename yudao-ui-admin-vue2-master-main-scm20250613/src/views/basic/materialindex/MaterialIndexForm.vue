<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="40%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="140px">
        <el-form-item label="编码" prop="matrlIndex">
          <el-row :gutter="0">
            <el-col :span="8">
              <el-input v-model="this.matrlIndexid" disabled style="width: 100%;"/>
            </el-col>
            <el-col :span="16">
              <el-input v-model="formData.matrlIndex" placeholder="请输入料号索引，例：01" :disabled="viewFlag" @blur="writeOffMatrlPrefix"/>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="名称" prop="matrlIndexdesc">
          <el-input v-model="formData.matrlIndexdesc" placeholder="请输入名称" style="width:100%;" :disabled="viewFlag"/>
        </el-form-item>
        <el-form-item label="物料代码前缀(8位)" prop="matrlPrefix" v-if="showLeaf">
          <el-input v-model="formData.matrlPrefix" placeholder="请输入物料代码前缀(8位)" style="width:100%;"
                    :disabled="viewFlag"/>
        </el-form-item>
        <el-form-item label="物料类型" prop="invenToryType">
          <el-select v-model="formData.invenToryType" placeholder="请选择物料类型"
                     :disabled="viewFlag || this.matrlIndexnum == '2' || this.matrlIndexnum == '3'" style="width:100%;">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="物料类别" prop="matrlIndexnum">
          <el-select v-model="this.matrlIndexnum" placeholder="请选择物料类别" disabled style="width:100%;">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MATRL_INDEX_NUM)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="叶类详细描述" prop="remark" v-if="showLeaf">
          <el-input v-model="formData.remark" placeholder="请输入叶类详细描述" style="width:100%;" :disabled="viewFlag"/>
        </el-form-item>
        <el-form-item label="叶类技术属性" prop="matrlLeafTech" v-if="showLeaf">
          <el-button type="primary" @click="openTechMulitForm" size="mini" :disabled="formType != 'edit'">技术属性
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <MaterialindextechMulit ref="formRef2"/>
  </div>
</template>

<script>
  import * as MaterialIndexApi from '@/api/basic/materialindex';
  import MaterialindextechMulit from "@/views/basic/materialindex/other/MaterialindextechMulit.vue";

  export default {
    name: "MaterialIndexForm",
    components: {MaterialindextechMulit},
    data() {
      return {
        formType: undefined,
        parentId: undefined,
        matrlIndexid: undefined,
        matrlIndexnum: undefined,
        viewFlag: false,
        showLeaf: false,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          parentId: undefined,
          project: undefined,
          compid: undefined,
          matrlIndexid: undefined,
          invenToryType: undefined,
          matrlIndex: undefined,
          matrlIndexrel: undefined,
          matrlIndexdesc: undefined,
          matrlIndexnum: undefined,
          remark: undefined,
          matrlPrefix: undefined,
          matrlLeafTech: undefined,
        },
        // 表单校验
        formRules: {
          invenToryType: [
            {required: true, message: '请选择物料类型', trigger: 'blur'}
          ],
          matrlIndex: [
            {required: true, message: '请输入料号索引', trigger: 'blur'},
          ],
          matrlIndexdesc: [
            {required: true, message: '请输入名称', trigger: 'blur'}
          ],
          matrlPrefix: [
            {required: true, message: '请输入物料代码前缀', trigger: 'blur'},
            // {validator: this.validateOption, trigger: 'blur'}
          ],
        },
      };
    },
    methods: {
      validateOption(rule, value, callback) {
        if (this.formData.matrlPrefix && this.formData.matrlPrefix.length != 8) {
          callback(new Error('物料代码前缀必须为8位!'));
        } else {
          callback();
        }
      },
      writeOffMatrlPrefix(){
        let prefix = this.matrlIndexid + this.formData.matrlIndex;
        // if(prefix.length < 8){
        //   for(let i = 0; i <= 8 - prefix.length + 1 ; i++){
        //     prefix = prefix + "0";
        //   }
        // }
        this.formData.matrlPrefix = prefix;
      },
      openTechMulitForm() {
        this.$refs["formRef2"].open(this.formType, this.formData.matrlIndexid, this.formData.matrlIndexdesc, this.formData.id);
      },
      /** 打开弹窗 */
      async open(id, type) {
        this.reset();
        this.formType = type;
        this.matrlIndexid = "";
        this.showLeaf = false;
        this.dialogVisible = true;
        if (type == 'view') {
          this.viewFlag = true;
          this.dialogTitle = "查看料号索引";
        } else {
          this.viewFlag = false;
          if (type == 'add') {
            this.dialogTitle = "新增料号索引";
          } else {
            this.dialogTitle = "修改料号索引";
          }
        }
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialIndexApi.getMaterialIndex(id);
            if (type == 'add') {
              this.parentId = res.data.id;
              if (res.data.matrlIndexid === '0000') {
                this.matrlIndexid = undefined;
                this.matrlIndexnum = "1";
              } else {
                this.formData.invenToryType = res.data.invenToryType;
                this.matrlIndexid = res.data.matrlIndexid;
                this.matrlIndexnum = (Number(res.data.matrlIndexnum) + 1) + '';
                if (this.matrlIndexnum == '3') {
                  this.showLeaf = true;
                }
              }
            } else {
              this.formData = res.data;
              this.matrlIndexid = res.data.matrlIndexrel;
              this.matrlIndexnum = res.data.matrlIndexnum;
              if (res.data.matrlIndexnum == '3') {
                this.showLeaf = true;
              }
            }
          } finally {
            this.formLoading = false;
          }
        }
      },
      /** 提交按钮 */
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialIndexApi.updateMaterialIndex(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          this.formData.parentId = this.parentId;
          if (this.matrlIndexid) {
            this.formData.matrlIndexid = this.matrlIndexid + this.formData.matrlIndex;
            this.formData.matrlIndexrel = this.matrlIndexid;
          } else {
            this.formData.matrlIndexid = this.formData.matrlIndex;
          }
          this.formData.matrlIndexnum = this.matrlIndexnum;
          await MaterialIndexApi.createMaterialIndex(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          project: undefined,
          compid: undefined,
          matrlIndexid: undefined,
          invenToryType: undefined,
          matrlIndex: undefined,
          matrlIndexrel: undefined,
          matrlIndexdesc: undefined,
          matrlIndexnum: undefined,
          remark: undefined,
          matrlPrefix: undefined,
          matrlLeafTech: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
