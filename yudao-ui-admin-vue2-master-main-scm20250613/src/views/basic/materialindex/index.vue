<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="5" :xs="24" style="border-right: 1px solid #ebebeb; background-color: #f2f5f8; padding: 5px; ">
        <div class="head-container">
          <el-input
            v-model="matrlIndexid"
            placeholder="请输入料号分类代码"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px"
          />
        </div>
        <div class="head-container">
          <el-button type="info" plain icon="el-icon-sort" size="mini" style="margin-bottom: 10px;width: 100%;"
                     @click="toggleExpandAll">
            展开/折叠
          </el-button>
        </div>
        <div v-if="refreshTable" class="head-container" style="max-height: 65vh; overflow: auto;">
          <el-tree
            :data="matrlIndexTree"
            :props="defaultProps"
            accordion
            :default-expand-all="isExpandAll"
            :default-expanded-keys="defaultKey"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
            :render-content="renderContent"
          />
        </div>
      </el-col>

      <el-col :span="19" :xs="24">
        <!-- 搜索工作栏 -->
        <el-form :model="queryTreeParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px">
          <el-form-item label="物料类型" prop="invenToryType">
            <el-select v-model="queryTreeParams.invenToryType" placeholder="请选择物料类型" clearable size="small">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="来源" prop="remark10">
            <el-select v-model="queryTreeParams.remark10" placeholder="请选择来源" clearable size="small">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.IT_SOURCE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-if="isEquipSelected" type="primary" plain icon="el-icon-plus" size="mini"
                       @click="openForm(treeId,'add')"
                       v-hasPermi="['basic:material-index:create']">新增
            </el-button>
          </el-col>
          <!--<el-col :span="1.5">-->
          <!--<el-button v-if="isEquipSelected" type="info" plain icon="el-icon-upload2" size="mini"-->
          <!--@click="handleImport"-->
          <!--v-hasPermi="['basic:material-index:create']">导入-->
          <!--</el-button>-->
          <!--</el-col>-->
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                  v-if="currentMatrlIndexnum == '0' || currentMatrlIndexnum == '1'">
          <el-table-column label="编码" align="center" prop="matrlIndexid"/>
          <el-table-column label="名称" align="center" prop="matrlIndexdesc" width="120" :show-overflow-tooltip="true"/>
          <el-table-column label="物料类型" align="center" prop="invenToryType">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenToryType"/>
            </template>
          </el-table-column>
          <el-table-column label="物料类别" align="center" prop="matrlIndexnum">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.MATRL_INDEX_NUM" :value="scope.row.matrlIndexnum"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                           width="150px">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="openForm(scope.row.id,'view')"
                         v-hasPermi="['basic:material-index:query']">详情
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,'edit')"
                         v-hasPermi="['basic:material-index:update']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['basic:material-index:delete']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                  v-if="currentMatrlIndexnum == '2' || currentMatrlIndexnum == '3'">
          <el-table-column label="编码" align="center" prop="matrlIndexid"/>
          <el-table-column label="名称" align="center" prop="matrlIndexdesc" width="120" :show-overflow-tooltip="true"/>
          <el-table-column label="物料代码前缀(8位)	" align="center" prop="matrlPrefix" width="130"/>
          <el-table-column label="物料类型" align="center" prop="invenToryType">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenToryType"/>
            </template>
          </el-table-column>
          <el-table-column label="物料类别" align="center" prop="matrlIndexnum">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.MATRL_INDEX_NUM" :value="scope.row.matrlIndexnum"/>
            </template>
          </el-table-column>
          <el-table-column label="叶类详细描述" align="center" prop="remark" width="200" :show-overflow-tooltip="true"/>
          <el-table-column label="叶类技术属性" align="center" prop="matrlLeafTech" width="200"
                           :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-dropdown @command="handleCommand">
                <el-button type="primary" size="mini">
                  技术属性<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-view" :command="{type: 'view', row: scope.row }">详情
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-edit" :command="{type: 'edit', row: scope.row }">修改
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                           width="150px">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="openForm(scope.row.id,'view')"
                         v-hasPermi="['basic:material-index:query']">详情
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,'edit')"
                         v-hasPermi="['basic:material-index:update']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['basic:material-index:delete']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="getList"/>

      </el-col>
    </el-row>

    <!-- 对话框(添加 / 修改) -->
    <MaterialIndexForm ref="formRef" @success="getList"/>
    <MaterialIndexForm ref="formRef" @success="getTreeList"/>
    <MaterialindextechMulit ref="formRef2"/>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx" :headers="upload.headers"
                 :action="upload.url" :disabled="upload.isUploading"
                 :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as MaterialIndexApi from '@/api/basic/materialindex';
import MaterialIndexForm from './MaterialIndexForm.vue';
import {getMaterialIndexList} from "@/api/basic/materialindex";
import MaterialindextechMulit from "@/views/basic/materialindex/other/MaterialindextechMulit.vue";
import {getBaseHeader} from "@/utils/request";

export default {
  name: "MaterialIndex",
  components: {
    MaterialIndexForm, MaterialindextechMulit
  },
  data() {
    return {
      currentMatrlIndexnum: 0,
      defaultKey: [1],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料料号索引列表
      list: [],
      // 料号分类代码
      matrlIndexid: undefined,
      // 物料树选项
      matrlIndexTree: undefined,
      defaultProps: {
        children: "children",
        label: "matrlIndexid"
      },
      // 是否展开，默认全部展开
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryTreeParams: {
        parentId: null,
        invenToryType: null
      },
      // 选择的树
      treeId: 1,
      isEquipSelected: true, // 是否选择了设备树
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        parentId: null,
        project: null,
        compid: null,
        matrlIndexid: null,
        invenToryType: null,
        matrlIndex: null,
        matrlIndexrel: null,
        matrlIndexdesc: null,
        matrlIndexnum: null,
        remark: null,
        creator: null,
        createTime: [],
        updater: null,
        updateTime: [],
        remark10: null
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/basic/material-index/import'
      },
    };
  },
  watch: {
    // 根据料号分类代码
    matrlIndexid(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.queryParams.parentId = 1;
    this.getTreeList();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "料号索引导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      MaterialIndexApi.importTemplate().then(response => {
        this.$download.excel(response, '料号索引导入模板.xlsx');
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return;
      }
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // 拼接提示语
      let data = response.data;
      let text = '创建成功数量：' + data.createUsernames.length;
      for (const username of data.createUsernames) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + username;
      }
      text += '<br />更新成功数量：' + data.updateUsernames.length;
      for (const username of data.updateUsernames) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + username;
      }
      text += '<br />更新失败数量：' + Object.keys(data.failureUsernames).length;
      for (const username in data.failureUsernames) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + username + '：' + data.failureUsernames[username];
      }
      this.$alert(text, "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleCommand(commandObj) {
      const {type, row} = commandObj;
      this.$refs["formRef2"].open(type, row.matrlIndexid, row.matrlIndexdesc, row.id);
    },
    // 切换展开/折叠
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    // 树显示内容编码加标签
    renderContent(h, {node, data, store}) {
      if (node.label === '0000') {
        return h('span', {class: 'custom-tree-node'}, [
          h('span', {style: 'margin-left: 1px; color: #888;'}, data.matrlIndexdesc)
        ]);
      } else {
        return h('span', {class: 'custom-tree-node'}, [
          h('span', node.label),
          h('span', {style: 'margin-left: 1px; color: #888;'}, "-" + data.matrlIndexdesc)
        ]);
      }
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.matrlIndexid.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.isEquipSelected = false;
      if (data.matrlIndexnum != '3') {
        this.currentMatrlIndexnum = data.matrlIndexnum;
        // 显示新增按钮
        this.isEquipSelected = true;
        this.treeId = data.id;
        this.queryParams.parentId = data.id;
        this.queryParams.matrlIndexnum = null;
        this.getList();
      }
    },
    /** 查询列表 */
    async getTreeList() {
      try {
        this.loading = true;
        const res = await MaterialIndexApi.getMaterialIndexList(this.queryTreeParams);

        const res1 = await MaterialIndexApi.getMaterialIndexPage(this.queryParams);
        this.list = res1.data.list;
        this.total = res1.data.total;

        this.matrlIndexTree = [];
        this.matrlIndexTree.push(...this.handleTree(res.data, "id"));

      } finally {
        this.loading = false;
      }
    },
    async getList() {
      try {
        this.loading = true;
        this.queryParams.invenToryType = this.queryTreeParams.invenToryType;
        this.queryParams.remark10 = this.queryTreeParams.remark10;
        const res = await MaterialIndexApi.getMaterialIndexPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getTreeList();
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.treeId = 1;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id, type) {
      this.$refs["formRef"].open(id, type, this.currentMatrlIndexnum);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除物料料号索引编号为"' + id + '"的数据项?')
      try {
        await MaterialIndexApi.deleteMaterialIndex(id);
        await this.getList();
        await this.getTreeList();
        this.$modal.msgSuccess("删除成功");
      } catch {
      }
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出所有物料料号索引数据项?');
      try {
        this.exportLoading = true;
        const data = await MaterialIndexApi.exportMaterialIndexExcel(this.queryParams);
        this.$download.excel(data, '物料料号索引.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
  }
};
</script>
