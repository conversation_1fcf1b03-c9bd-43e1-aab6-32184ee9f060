<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog title="请选择工作流处理人员" :visible.sync="dialogVisible" width="35%" v-dialogDrag append-to-body>
      <el-form ref="formRef" size="small" :model="formData" :rules="formRules" v-loading="formLoading"
               label-width="120px">
        <el-form-item label="作业部专管员" prop="applyUserManagerId">
          <el-select v-model="formData.applyUserManagerId" placeholder="请选择" style="width: 250px;">
            <el-option
              v-for="item in applyUserManagerIdList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="作业部部长" prop="applyUserLeaderId">
          <el-select v-model="formData.applyUserLeaderId" placeholder="请选择" style="width: 250px;">
            <el-option
              v-for="item in applyUserLeaderIdList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确认发起</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as MaterialApi from '@/api/basic/material';
import store from "@/store";
import * as DeptApi from "@/api/system/dept";
import * as UserApi from "@/api/system/user";
import {listUser} from "@/api/system/user";
import {batchApplyMaterial} from "@/api/basic/material";

export default {
  name: "MaterialSubmitForm2",
  components: {},
  data() {
    return {
      dialogVisible: false,
      // 弹出层标题
      dialogTitle: "",
      // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
      formLoading: false,
      deptOptions: undefined,
      applyUserManagerIdList: [],
      applyUserLeaderIdList: [],
      // 表单参数
      formData: {
        applyUserManagerId: undefined,
        applyUserLeaderId: undefined,
      },
      // 表单校验
      formRules: {
        applyUserManagerId: [{required: true, message: "作业部专管员不能为空", trigger: "blur"}],
        applyUserLeaderId: [{required: true, message: "作业部部长不能为空", trigger: "blur"}],
      },
    };
  },
  methods: {
    /** 打开弹窗 */
    async open() {
      this.reset();
      const deptId = store.getters.deptId;
      const response = await DeptApi.getDept(deptId);
      const managerUserIds = response.data.managerUserIds;
      const leaderUserIds = response.data.leaderUserIds;
      if (!managerUserIds || response.data.managerUserIds.length == 0) {
        this.$modal.msgError("该账号对应机构未配置作业部专管员，请联系管理员处理！");
        return;
      }
      if (!leaderUserIds || response.data.leaderUserIds.length == 0) {
        this.$modal.msgError("该账号对应机构未配置作业部部长，请联系管理员处理！");
        return;
      }

      if (response.data.managerUserIds.length == 1 && response.data.leaderUserIds.length == 0) {
        this.submitForm();
      } else {
        const userResponse = await UserApi.listSimpleUsers();
        this.userList = userResponse.data;
        managerUserIds.forEach((id) => {
          this.applyUserManagerIdList.push({
            id: id,
            nickname: this.userList.find(item => item.id === id).nickname
          });
        });
        if(managerUserIds.length == 1){
          this.formData.applyUserManagerId = managerUserIds[0];
        }
        leaderUserIds.forEach((id) => {
          this.applyUserLeaderIdList.push({
            id: id,
            nickname: this.userList.find(item => item.id === id).nickname
          });
        });
        if(leaderUserIds.length == 1){
          this.formData.applyUserLeaderId = leaderUserIds[0];
        }
        this.dialogVisible = true;
      }
    },
    /** 提交按钮 */
    async submitForm() {
      await this.$refs["formRef"].validate();
      this.$emit('success',this.formData);
      this.dialogVisible = false;
    },
    /** 表单重置 */
    reset() {
      this.formData = {
        applyUserManagerId: undefined,
        applyUserLeaderId: undefined,
      };
      this.applyUserManagerIdList = [];
      this.applyUserLeaderIdList = [];
      this.resetForm("formRef");
    },
  }
};
</script>
