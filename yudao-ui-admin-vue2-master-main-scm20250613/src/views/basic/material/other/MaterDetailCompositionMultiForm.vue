<template>
  <div class="app-container">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" top="40%" width="76%" v-dialogDrag append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openTBMP00Form()"
                     :disabled="dialogTitle=='批量修改'">成分批量挑选
          </el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                @selection-change="handleSelectionChange"
                :highlight-current-row="true">
        <el-table-column align="center" type="selection" fixed="left"/>
        <el-table-column label="序号" type="index" width="50" fixed="left"/>
        <el-table-column label="成分代码" align="center" prop="compositionCode" width="170" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.compositionCode" style="width:130px" disabled/>
            <span class="mark">*</span>
          </template>
        </el-table-column>
        <el-table-column label="成分名称" align="center" prop="compositionName" width="200" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.compositionName" style="width:160px" disabled/>
            <span class="mark">*</span>
          </template>
        </el-table-column>
        <el-table-column label="小数位" align="center" prop="decimalPlace" width="165" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.decimalPlace" :precision="0" style="width:130px"/>
            <span class="mark">*</span>
          </template>
        </el-table-column>
        <el-table-column label="下限" align="center" prop="lowerLimit" width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lowerLimit" style="width:130px" @blur="checkNumber(scope.row)"/>
          </template>
        </el-table-column>
        <el-table-column label="上限" align="center" prop="upperLimit" width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.upperLimit" style="width:130px"  @blur="checkNumber(scope.row)"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="110" fixed="right">
          <template v-slot="scope">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleItemDelete(scope.row)"
                       :disabled="dialogTitle=='批量修改'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="confirmFlag || loading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
      <MaterDetailCompositionSelectMultiSearch ref="formRef2" @success="receiveList"/>
    </el-dialog>
  </div>
</template>

<script>
  import MaterDetailCompositionSelectMultiSearch from "./MaterDetailCompositionSelectMultiSearch.vue";
  import * as MaterialApi from '@/api/basic/material';

  export default {
    name: "MaterDetailCompositionMultiForm",
    components: {
      MaterDetailCompositionSelectMultiSearch
    },
    data() {
      return {
        formType: undefined,
        parentid: undefined,
        confirmFlag: true,
        // 遮罩层
        loading: false,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        list: [],
        idItems: [],
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          id: null,
          project: null,
        },
      };
    },
    created() {
    },
    methods: {
      checkNumber(row){
        if(row.lowerLimit !== '' && row.lowerLimit != undefined && row.lowerLimit != null){
          row.lowerLimit = Number.isNaN(Number(row.lowerLimit)) ? "0" : Number(row.lowerLimit) + "";
        }else{
          row.lowerLimit = "0";
        }
        if(row.upperLimit !== '' && row.upperLimit != undefined && row.upperLimit != null){
          row.upperLimit = Number.isNaN(Number(row.upperLimit)) ? "0" : Number(row.upperLimit) + "";
        }else{
          row.upperLimit = "0";
        }
      },
      handleItemDelete(row){
        this.loading= true;
        this.list.forEach((el, index) => {
          if(el.compositionCode == row.compositionCode){
            this.list.splice(index,1);
          }
        });
        for (let i = 0; i <= this.idItems.length - 1; i++) {
          if (this.idItems[i].compositionCode == row.compositionCode) {
            this.idItems.splice(i, 1);
            break;
          }
        }
        this.loading= false;
      },
      receiveList(items){
        console.info(items);
        let existssrlno = "";
        this.list.forEach((el, index) => {
          items.forEach((el2, index2) => {
            if(el.compositionCode == el2.compositionCode){
              existssrlno = existssrlno + "," + el.compositionCode;
              items.splice(index2,1);
            }
          });
        });
        if(existssrlno != ""){
          existssrlno = existssrlno.slice(1);
          this.$modal.msgWarning("已存在成分代码为【"+existssrlno+"】的明细，不允许挑选！");
        }

        let totalList = [];
        this.list.forEach((el, index) => {
          totalList.push(el);
        });
        items.forEach((el, index) => {
          totalList.push(el);
        });
        this.list = totalList;
      },
      openTBMP00Form(){
        this.$refs["formRef2"].open();
      },
      async open(type, upItems, parentid) {
        this.dialogVisible = true;
        this.confirmFlag = true;
        this.formType = type;
        this.parentid = parentid;
        this.list = [];
        if(type == 'add'){
          this.dialogTitle = "批量新增";
        }else{
          this.dialogTitle = "批量修改";
          this.list = JSON.parse(JSON.stringify(upItems));
        }
        this.resetForm("queryForm");
      },
      handleSelectionChange(val) {
        this.idItems = val;
        if(val && val.length > 0){
          this.confirmFlag = false;
        }else{
          this.confirmFlag = true;
        }
      },
      async submitForm() {
        const items = [];
        let errorDecimalPlace = "";
        let errorLimit = "";
        this.idItems.forEach((el, index) => {
          if(el.decimalPlace === '' || el.decimalPlace == undefined || el.decimalPlace == null){
            errorDecimalPlace = errorDecimalPlace + "," + (index+1);
          }
          //校验上限必须 >= 下限
          let lowerLimit = Number.isNaN(Number(el.lowerLimit)) ? 0 : Number(el.lowerLimit);
          let upperLimit = Number.isNaN(Number(el.upperLimit)) ? 0 : Number(el.upperLimit);
          if(upperLimit < lowerLimit){
            errorLimit = errorLimit + "," + (index+1);
          }
          var map = {
            id: el.id,
            parentid: this.parentid,
            compositionCode: el.compositionCode,
            compositionName: el.compositionName,
            decimalPlace: el.decimalPlace,
            lowerLimit: el.lowerLimit,
            upperLimit: el.upperLimit,
          };
          items.push(map);
        });

        if(errorDecimalPlace.length > 0){
          errorDecimalPlace = errorDecimalPlace.slice(1);
          this.$modal.msgError("序号为【"+errorDecimalPlace+"】的项次，小数位不能为空！");
          return;
        }
        if(errorLimit.length > 0){
          errorLimit = errorLimit.slice(1);
          this.$modal.msgError("序号为【"+errorLimit+"】的项次，下限不能大于上限！");
          return;
        }
        if (!items || items.length == 0) {
          this.$modal.msgWarning("请选择数据后再确认！");
          return;
        }
        let params = {
          parentid: this.parentid,
          type: this.formType,
          items: items
        };
        try{
          this.loading = true;
          await MaterialApi.batchOperateDetailComposition(params);
          this.$modal.msgSuccess("批量操作成功");
          this.dialogVisible = false;
          this.$emit('success');
        }finally {
          this.loading = false;
        }
      },
    }
  };
</script>
<style scoped>
  .mark{
    color: red;
    margin-left: 2px;
  }
</style>
