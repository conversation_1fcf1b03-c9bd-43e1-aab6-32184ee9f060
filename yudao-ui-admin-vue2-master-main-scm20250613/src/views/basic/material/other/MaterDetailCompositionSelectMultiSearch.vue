<template>
  <div class="app-container">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" v-dialogDrag append-to-body>
      <!-- 搜索工作栏 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="成分代号" prop="value">
          <el-input v-model="queryParams.value" placeholder="请输入成分代号" clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="成分名称" prop="label">
          <el-input v-model="queryParams.label" placeholder="请输入成分名称" clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
                @selection-change="handleSelectionChange"
                :highlight-current-row="true">
        <el-table-column align="center" type="selection" fixed="left" width="50"/>
        <el-table-column label="序号" align="center" prop="sort" width="100" :show-overflow-tooltip="true"/>
        <el-table-column label="成分代号" align="center" prop="value" width="200" :show-overflow-tooltip="true"/>
        <el-table-column label="成分名称" align="center" prop="label" width="300" :show-overflow-tooltip="true"/>
        <el-table-column label="状态" align="center" prop="status" width="180" :show-overflow-tooltip="true">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status"/>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="confirmFlag">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as DicDataApi from "@/api/system/dict/data.js";

  export default {
    name: "MaterDetailCompositionSelectMultiSearch",
    components: {},
    data() {
      return {
        confirmFlag: true,
        // 遮罩层
        loading: true,
        // 弹出层标题
        dialogTitle: "成分列表",
        // 是否显示弹出层
        dialogVisible: false,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 合同信息列表
        list: [],
        idItems: [],
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          label: undefined,
          value: undefined,
          dictType: "MRChkItem",
          status: "0",
        },
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询列表 */
      async getList() {
        try {
          this.loading = true;
          const res = await DicDataApi.listData2(this.queryParams);
          this.total = res.data.total;
          this.list = res.data.list;
        } finally {
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNo = 1;
        this.queryParams.pageSize = 10;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      async open() {
        this.dialogVisible = true;
        this.resetForm("queryForm");
        this.confirmFlag = true;
        this.handleQuery();
      },
      handleSelectionChange(val) {
        this.idItems = val;
        if(this.idItems.length > 0){
          this.confirmFlag = false;
        }else{
          this.confirmFlag = true;
        }
      },
      async submitForm() {
        const items = [];
        this.idItems.forEach((el, index) => {
          var map = {
            compositionCode: el.value,
            compositionName: el.label,
            decimalPlace: "0",
            lowerLimit: "0",
            upperLimit: "0",
          };
          items.push(map);
        });
        if (!items || items.length == 0) {
          this.$modal.msgWarning("请选择数据后再进行关联操作！");
          return;
        }
        this.$emit('success', items);
        this.dialogVisible = false;
      },
    }
  };
</script>
<style scoped>
  .description {
    font-size: 16px; /* 文字大小 */
    color: #333333; /* 文字颜色 */
    font-family: 'Arial', sans-serif; /* 字体 */
    text-align: left;
  }

  .red-bold-text {
    color: #FF3333;
    font-weight: bold;
  }
</style>
