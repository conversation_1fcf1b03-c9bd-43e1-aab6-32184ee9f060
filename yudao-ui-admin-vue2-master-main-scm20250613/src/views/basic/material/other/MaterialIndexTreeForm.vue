<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="25%" v-dialogDrag append-to-body>
      <div>
        <el-tree
          :data="matrlIndexTree"
          :props="defaultProps"
          accordion
          :default-expand-all="isExpandAll"
          :default-expanded-keys="defaultKey"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          ref="tree"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          :render-content="renderContent"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialIndexApi from '@/api/basic/materialindex';

  export default {
    name: "MaterialIndexTreeForm",
    components: {},
    data() {
      return {
        level: undefined,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        matrlIndexTree: undefined,
        defaultProps: {
          children: "children",
          label: "matrlIndexid"
        },
        isExpandAll: false,
        defaultKey: [1],
        treeId: 1,
        currentMatrlIndexnum: 0,
        currentdata: {},
      };
    },
    methods: {
      submitForm() {
        if(this.currentdata.matrlIndexnum != this.level){
          if(this.level == "1"){
            this.$modal.msgError("请选择大类层级的数据！");
            return;
          }else if(this.level == "2"){
            this.$modal.msgError("请选择中类层级的数据！");
            return;
          }else{
            this.$modal.msgError("请选择叶类层级的数据！");
            return;
          }
        }
        this.$emit('success', this.currentdata);
        this.dialogVisible = false;
      },
      /** 打开弹窗 */
      async open(level) {
        this.dialogVisible = true;
        this.level = level;
        if(level == "1"){
          this.dialogTitle = "挑选大类";
        }else if(level == "2"){
          this.dialogTitle = "挑选中类";
        }else{
          this.dialogTitle = "挑选叶类";
        }
        const res = await MaterialIndexApi.getTreeListByLevel(level);
        this.matrlIndexTree = [];
        this.matrlIndexTree.push(...this.handleTree(res.data, "id"));
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.matrlIndexid.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(data) {
        this.currentdata = data;
      },
      // 树显示内容编码加标签
      renderContent(h, {node, data, store}) {
        if (node.label === '0000') {
          return h('span', {class: 'custom-tree-node'}, [
            h('span', {style: 'margin-left: 1px; color: #888;'}, data.matrlIndexdesc)
          ]);
        } else {
          return h('span', {class: 'custom-tree-node'}, [
            h('span', node.label),
            h('span', {style: 'margin-left: 1px; color: #888;'}, "-" + data.matrlIndexdesc)
          ]);
        }
      },
    }
  };
</script>
