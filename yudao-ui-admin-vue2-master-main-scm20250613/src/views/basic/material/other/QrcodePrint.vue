<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" @click="print">打印</el-button>
      </el-col>
    </el-row>
    <div id="print-section">
      <el-row :gutter="20">
        <div v-for="(field, index) in items" :key="field.qrName">
          <el-col :span="12" :xs="24">
            <!-- 二维码容器 -->
            <qrcode-vue
              :value="field.qrValue"
              :size="256"
              tag="svg"
              ref="qrcode">
            </qrcode-vue>
            <h1>{{field.qrName}}</h1>
          </el-col>
        </div>
      </el-row>
    </div>
  </div>
</template>
<script>
  import QrcodeVue from 'qrcode.vue';
  import * as MaterialApi from '@/api/basic/material';

  export default {
    name: "QrcodeForm",
    components: {
      QrcodeVue
    },
    data() {
      return {
        items: [],
      };
    },
    created() {
      this.generateQrcode();
    },
    methods: {
      async generateQrcode(){
        let ids = this.$route.query.ids;
        const res = await MaterialApi.getMaterialByIds(ids);
        if (res && (res.code === 200 || res.code === 0) && res.data) {
          res.data.forEach((el, index) => {
            var json = {
              "料号": el.matrlno,
              "中文品名": el.cnmdesc,
              "物料短描述": el.shortDesc,
            };
            let jsonStr = JSON.stringify(json, null, 2);
            let item = {
              "qrName" : el.matrlno+"-"+el.cnmdesc,
              "qrValue" : jsonStr
            };
            this.items.push(item);
          });
        } else {
          this.$modal.msgError("无数据！");
        }
      },
      async print(){
        window.print();
      }
    }
  };
</script>
<style scoped>
  /* 打印样式 */
  @media print {
    body * {
      visibility: hidden; /* 隐藏所有内容 */
    }
    #print-section, #print-section * {
      visibility: visible; /* 仅显示二维码容器 */
    }
    #print-section {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      text-align: center;
    }
  }
</style>
