<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openMultiForm('add')"
                   v-hasPermi="['basic:material:create']">新增
<!--                   :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'"-->
        </el-button>
        <el-button type="warning" plain icon="el-icon-edit" size="mini" @click="openMultiForm('edit')"
                   v-hasPermi="['basic:material:update']">修改
        </el-button>
        <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleDelete"
                   v-hasPermi="['basic:material:delete']">删除
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
              @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" fixed="left"/>
      <el-table-column label="成分代码" align="center" prop="compositionCode"/>
      <el-table-column label="成分名称" align="center" prop="compositionName"/>
      <el-table-column label="小数位" align="center" prop="decimalPlace"/>
      <el-table-column label="下限" align="center" prop="lowerLimit"/>
      <el-table-column label="上限" align="center" prop="upperLimit"/>
      <el-table-column label="更新者" align="center" prop="updater"/>
      <el-table-column label="更新者姓名" align="center" prop="updateEmpNo"/>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <MaterialDetailCompositionForm ref="formRef" @success="getList"/>
    <MaterDetailCompositionMultiForm ref="formRef2" @success="getList"/>
  </div>
</template>

<script>
  import * as MaterialApi from '@/api/basic/material';
  import MaterialDetailCompositionForm from './MaterialDetailCompositionForm.vue';
  import MaterDetailCompositionMultiForm from '@/views/basic/material/other/MaterDetailCompositionMultiForm.vue';

  export default {
    name: "MaterialDetailCompositionList",
    components: {
      MaterialDetailCompositionForm,MaterDetailCompositionMultiForm
    },
    props: [
      'parentid', 'matrlStatus'
    ],// PARENTID（主表的关联字段）
    data() {
      return {
        idItems: [],
        // 遮罩层
        loading: true,
        // 列表的数据
        list: [],
        // 列表的总页数
        total: 0,
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          parentid: undefined
        }
      };
    },
    watch: {
      /** 监听主表的关联字段的变化，加载对应的子表数据 */
      parentid: {
        handler(val) {
          this.queryParams.parentid = val;
          if (val) {
            this.handleQuery();
          }
        },
        immediate: true
      }
    },
    methods: {
      handleSelectionChange(val) {
        this.idItems = val;
      },
      /** 查询列表 */
      async getList() {
        try {
          this.loading = true;
          const res = await MaterialApi.getMaterialDetailCompositionPage(this.queryParams);
          this.list = res.data.list;
          this.total = res.data.total;
        } finally {
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNo = 1;
        this.getList();
      },
      /** 添加/修改操作 */
      openMultiForm(type) {
        if (type == 'edit' && this.idItems.length == 0) {
          this.$modal.msgWarning("请选择数据后再修改！");
          return;
        }
        this.$refs["formRef2"].open(type, this.idItems, this.parentid);
      },
      /** 删除按钮操作 */
      async handleDelete() {
        if(this.idItems.length==0){
          this.$modal.msgWarning("请选择数据后再修改！");
          return;
        }
        await this.$modal.confirm('是否确认批量删除?');
        let items = [];
        this.idItems.forEach((el, index) => {
          var map = {
            id: el.id,
            parentid: this.parentid,
            compositionCode: el.compositionCode,
          };
          items.push(map);
        });
        let params = {
          parentid: this.parentid,
          type: "delete",
          items: items
        };
        try {
          this.loading = true;
          await MaterialApi.batchOperateDetailComposition(params);
          this.$modal.msgSuccess("批量删除成功");
          this.getList();
        } finally {
          this.loading = false;
        }
      },
    }
  };
</script>
