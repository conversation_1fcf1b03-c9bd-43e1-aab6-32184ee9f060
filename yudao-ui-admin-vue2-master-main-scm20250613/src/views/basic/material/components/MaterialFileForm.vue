<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <div>
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-edit" size="mini" @click="editForm"
                     v-hasPermi="['basic:material:update']" :disabled="(matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5') || editFlag">修改
          </el-button>
        </el-col>
      </div>
      <div class="right-cols-container">
        <el-col :span="1.5" v-show="!viewFlag" style="margin-left: 100px;">
          <el-button type="primary" plain round icon="el-icon-thumb" size="mini" @click="submitForm"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">提交
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag">
          <el-button type="danger" plain round icon="el-icon-close" size="mini" @click="cancelForm"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">取消
          </el-button>
        </el-col>
      </div>
    </el-row>
    <!-- 表单 -->
    <div class="form-container">
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading">
        <el-form-item label="技术附件">
          <FileUpload v-model="formData.techFile" :readonlyFlag="viewFlag" :prefixPath="prefixPath"/>
        </el-form-item>
        <el-form-item label="物料图档">
          <ImageUpload v-model="formData.picFile" :readonly="viewFlag"/>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
  import * as MaterialFileApi from '@/api/basic/materialfile';
  import ImageUpload from '@/components/ImageUpload';
  import FileUpload from '@/components/FileUpload';
  import {getCurrentDateTimeMill} from "@/utils/dateUtils";

  export default {
    name: "MaterialFileForm",
    components: {
      ImageUpload,
      FileUpload,
    },
    props: [
      'parentid', 'matrlStatus'
    ],
    watch: {
      /** 监听主表的关联字段的变化，加载对应的子表数据 */
      parentid: {
        handler(val) {
          this.editFlag = false;
          this.viewFlag = true;
          if (val) {
            this.prefixPath = "materialFile/" + getCurrentDateTimeMill() + '/' + this.parentid;
            console.info(this.prefixPath);
            this.queryMaterialFile(val);
          }
        },
        immediate: true
      },
    },
    data() {
      return {
        prefixPath: "",
        tempForm: {},
        editFlag: false,
        viewFlag: true,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {},
        // 表单校验
        formRules: {
          techFile: [{required: true, message: '技术附件不能为空', trigger: 'blur'}],
          picFile: [{required: true, message: '物料图档不能为空', trigger: 'blur'}],
        },
      };
    },
    methods: {
      async queryMaterialFile(parentid) {
        this.formLoading = true;
        try {
          const res = await MaterialFileApi.getMaterialFileByParentid(parentid);
          if (res && (res.code === 200 || res.code === 0) && res.data) {
            this.formData = res.data;
          } else {
            this.formData = {};
          }
        } finally {
          this.formLoading = false;
        }
      },
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialFileApi.updateMaterialFile(data);
          }
          // 添加的提交
          else {
            data.parentid = this.parentid;
            await MaterialFileApi.createMaterialFile(data);
          }
          this.$modal.msgSuccess("提交成功");
          this.editFlag = false;
          this.viewFlag = true;
        } finally {
          this.formLoading = false;
        }
      },
      cancelForm() {
        this.formData = {...this.tempForm};
        this.editFlag = false;
        this.viewFlag = true;
      },
      editForm() {
        this.tempForm = {...this.formData};
        this.editFlag = true;
        this.viewFlag = false;
      },
    }
  };
</script>
<style scoped>
  .form-container {
    border: 1px solid #dcdcdc; /* 设置边框 */
    padding: 20px; /* 设置内边距 */
    border-radius: 8px; /* 可选：设置圆角 */
    background-color: #f9f9f9; /* 可选：设置背景颜色 */
  }
  .right-cols-container {
    display: flex;
    justify-content: flex-end; /* 使子元素靠右对齐 */
  }
</style>
