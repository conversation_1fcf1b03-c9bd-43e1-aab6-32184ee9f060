<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <div>
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-edit" size="mini" @click="editForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="(matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5') || editFlag">
            修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-outline" size="mini" @click="editFormAudit"
                     v-hasPermi="['basic:material:update']" v-if="matrlStatus == '2'" :disabled="editFlag">管理属性变更
          </el-button>
        </el-col>
      </div>
      <div class="right-cols-container">
        <el-col :span="1.5" v-show="!viewFlag1">
          <el-button type="primary" plain round icon="el-icon-thumb" size="mini" @click="submitForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">提交
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag1">
          <el-button type="danger" plain round icon="el-icon-close" size="mini" @click="cancelForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">取消
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag2" style="margin-left: 100px;">
          <el-button type="primary" plain round icon="el-icon-thumb" size="mini" @click="openMaterialSubmitForm2"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '2'">提交审批
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag2">
          <el-button type="danger" plain round icon="el-icon-close" size="mini" @click="cancelForm"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '2'">取消
          </el-button>
        </el-col>
      </div>
    </el-row>
    <!-- 对话框(添加 / 修改) -->
    <div class="form-container">
      <el-form ref="formRef" size="small" :inline="true" :model="formData" :rules="formRules" v-loading="formLoading"
               label-width="140px">
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="预算价" prop="budgetPrice"
                          :class="{ 'is-modified': modifiedFields.includes('budgetPrice') }"
                          v-if="invenTorytype != 'R'">
              <el-input v-model="formData.budgetPrice" placeholder="请输入预算价" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('budgetPrice')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最大库存" prop="maxstkqty"
                          :class="{ 'is-modified': modifiedFields.includes('maxstkqty') }">
              <el-input v-model="formData.maxstkqty" placeholder="请输入最大库存" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('maxstkqty')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最小库存" prop="minstkqty" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('minstkqty') }">
              <el-input v-model="formData.minstkqty" placeholder="请输入最小库存" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('minstkqty')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="消耗定额" prop="consumeQty"
                          :class="{ 'is-modified': modifiedFields.includes('consumeQty') }">
              <el-input v-model="formData.consumeQty" placeholder="请输入消耗定额" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('consumeQty')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最近采购价" prop="lastPrice" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('lastPrice') }">
              <el-input v-model="formData.lastPrice" placeholder="请输入最近采购价" disabled style="width: 150px"
                        @change="changeWatch('lastPrice')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="计价方式" prop="priceType" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('priceType') }">
              <el-select v-model="formData.priceType" placeholder="请选择计价方式" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('priceType')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PRICE_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="管理类别" prop="spManageType" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('spManageType') }">
              <el-select v-model="formData.spManageType" placeholder="请选择管理类别" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('spManageType')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SP_MANAGE_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="采购标准" prop="purStandard" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('purStandard') }">
              <el-input v-model="formData.purStandard" placeholder="请输入采购标准" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('purStandard')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="采购提前期(天)" prop="predays" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('predays') }">
              <el-input v-model="formData.predays" placeholder="请输入采购提前期(天)" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('predays')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最小订单" prop="minorderqty" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('minorderqty') }">
              <el-input v-model="formData.minorderqty" placeholder="请输入最小订单" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('minorderqty')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="批量倍数" prop="qtyMultiple" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('qtyMultiple') }">
              <el-input v-model="formData.qtyMultiple" placeholder="请输入批量倍数" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('qtyMultiple')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="采购管理流程" prop="purProcess" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('purProcess') }">
              <el-select v-model="formData.purProcess" placeholder="请选择采购管理流程" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('purProcess')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PUR_PROCESS)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="特控类型" prop="specCtrlType" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('specCtrlType') }">
              <el-select v-model="formData.specCtrlType" placeholder="请选择特控类型" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('specCtrlType')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SPEC_CTRL_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否批次控制" prop="batchCtrl" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('batchCtrl') }">
              <el-select v-model="formData.batchCtrl" placeholder="请选择是否批次控制" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('batchCtrl')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否要检化验" prop="inspectFlag" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('inspectFlag') }">
              <el-select v-model="formData.inspectFlag" placeholder="请选择是否要检化验" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('inspectFlag')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否批号管理" prop="lotchk"
                          :class="{ 'is-modified': modifiedFields.includes('lotchk') }">
              <el-select v-model="formData.lotchk" placeholder="请选择是否批号管理" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('lotchk')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否要计量" prop="measureFlag" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('measureFlag') }">
              <el-select v-model="formData.measureFlag" placeholder="请选择是否要计量" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('measureFlag')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否非标件" prop="nonStandardFlag" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('nonStandardFlag') }">
              <el-select v-model="formData.nonStandardFlag" placeholder="请选择是否非标件" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('nonStandardFlag')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="验收类型" prop="acceptanceFlag" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('acceptanceFlag') }">
              <el-select v-model="formData.acceptanceFlag" placeholder="请选择验收类型" size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('acceptanceFlag')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ACCEPTANCE_FLAG)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="验收标准" prop="acceptanceStandard" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('acceptanceStandard') }">
              <el-input v-model="formData.acceptanceStandard" placeholder="请输入验收标准" :disabled="viewFlag"
                        style="width: 150px" @change="changeWatch('acceptanceStandard')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="验收备注" prop="acceptanceRemark" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('acceptanceRemark') }">
              <el-input v-model="formData.acceptanceRemark" placeholder="请输入验收备注" :disabled="viewFlag"
                        style="width: 150px" @change="changeWatch('acceptanceRemark')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="制造周期(天)" prop="manufacPeriod" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('manufacPeriod') }">
              <el-input v-model="formData.manufacPeriod" placeholder="请输入制造周期(天)" :disabled="viewFlag"
                        style="width: 150px" @change="changeWatch('manufacPeriod')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="保质期" prop="shelfLife" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': (modifiedFields.includes('shelfLife') || modifiedFields.includes('shelfLifeUnit')) }"
            >
              <el-input v-model="formData.shelfLife" placeholder="请输入保质期" :disabled="viewFlag" style="width: 70px"
                        @change="changeWatch('shelfLife')"/>
              <el-select v-model="formData.shelfLifeUnit" placeholder="请选择" clearable size="small"
                         :disabled="viewFlag" style="width: 80px" @change="changeWatch('shelfLifeUnit')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TIME_DIC)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="生命周期" prop="lifeCycle" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': (modifiedFields.includes('lifeCycle') || modifiedFields.includes('lifeCycleUnit')) }"
            >
              <el-input v-model="formData.lifeCycle" placeholder="请输入生命周期" :disabled="viewFlag" style="width: 70px"
                        @change="changeWatch('lifeCycle')"/>
              <el-select v-model="formData.lifeCycleUnit" placeholder="请选择" clearable size="small"
                         :disabled="viewFlag" style="width: 80px" @change="changeWatch('lifeCycleUnit')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TIME_DIC)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="库存损耗率" prop="invLossRate" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('invLossRate') }">
              <el-input v-model="formData.invLossRate" placeholder="库存损耗率" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('invLossRate')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="物料分级" prop="itemLevel" v-if="invenTorytype != 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('itemLevel') }">
              <el-select v-model="formData.itemLevel" placeholder="请选择物料分级" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('itemLevel')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ITEM_LEVEL)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="供货渠道" prop="selfproduce" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('selfproduce') }">
              <el-select v-model="formData.selfproduce" placeholder="请选择供货渠道" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('selfproduce')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SELF_PRODUCE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否允许负帐交易" prop="lastcno"
                          :class="{ 'is-modified': modifiedFields.includes('lastcno') }">
              <el-select v-model="formData.lastcno" placeholder="请选择是否允许负帐交易" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('lastcno')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否允许负单价交易" prop="pchkstd"
                          :class="{ 'is-modified': modifiedFields.includes('pchkstd') }">
              <el-select v-model="formData.pchkstd" placeholder="请选择是否允许负单价交易" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('pchkstd')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="标准水分" prop="stardwaterrate" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('stardwaterrate') }">
              <el-input v-model="formData.stardwaterrate" placeholder="请输入标准水分" :disabled="viewFlag"
                        style="width: 150px"
                        @change="changeWatch('stardwaterrate')"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否需要质检" prop="ifQualityCheck" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('ifQualityCheck') }">
              <el-select v-model="formData.ifQualityCheck" placeholder="请选择是否需要质检" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('ifQualityCheck')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.IF_QUALITY_CHECK)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="TP产品规范" prop="tpSpecno" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('tpSpecno') }">
              <el-select v-model="formData.tpSpecno" placeholder="请选择TP产品规范" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('tpSpecno')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TP_SPECNO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="IP系统产副品代码" prop="ipSystemSecondCode" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('ipSystemSecondCode') }">
              <el-select v-model="formData.ipSystemSecondCode" placeholder="请选择IP系统产副品代码" clearable size="small"
                         :disabled="viewFlag" style="width: 150px" @change="changeWatch('ipSystemSecondCode')">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.IP_SYSTEM_SECOND_CODE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="备注" prop="remark" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': modifiedFields.includes('remark') }">
              <el-input v-model="formData.remark" placeholder="请输入备注" :disabled="viewFlag" style="width: 150px"
                        @change="changeWatch('remark')"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16" :xs="24">
            <el-form-item label="标准单价1/生效日期" prop="stdunitcost" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': (modifiedFields.includes('stdunitcost') || modifiedFields.includes('stdunitcostdate')) }">
              <el-input v-model="formData.stdunitcost" placeholder="请输入标准单价1" :disabled="viewFlag"
                        style="width: 150px"
                        @change="changeWatch('stdunitcost')"/>
              <el-date-picker style="width: 180px" clearable v-model="formData.stdunitcostdate" type="date"
                              value-format="timestamp" placeholder="选择生效日期" :disabled="viewFlag"
                              @change="changeWatch('stdunitcostdate')"/>
            </el-form-item>
          </el-col>
          <el-col :span="16" :xs="24">
            <el-form-item label="标准单价2/生效日期" prop="stdunitcost2" v-if="invenTorytype == 'R'"
                          :class="{ 'is-modified': (modifiedFields.includes('stdunitcost2') || modifiedFields.includes('stdunitcostdate2')) }">
              <el-input v-model="formData.stdunitcost2" placeholder="请输入标准单价1" :disabled="viewFlag"
                        style="width: 150px"
                        @change="changeWatch('stdunitcost2')"/>
              <el-date-picker style="width: 180px" clearable v-model="formData.stdunitcostdate2" type="date"
                              value-format="timestamp" placeholder="选择生效日期" :disabled="viewFlag"
                              @change="changeWatch('stdunitcostdate2')"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="!viewFlag2">
          <el-divider></el-divider>
          <el-col :span="24" :xs="24">
            <el-form-item label="修改原因" :prop="editReason">
              <el-input v-model="editReason" type="textarea" :rows="2" placeholder="请输入修改原因"
                        style="width: 700px" :disabled="viewFlag"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" :xs="24">
            <el-form-item label="修改信息" :prop="editInfo">
              <el-input v-model="editInfo" type="textarea" :rows="2" disabled
                        style="width: 700px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <MaterialSubmitForm2 ref="formRef5" @success="submitFormTechAudit"/>
  </div>
</template>

<script>
  import * as MaterialApi from '@/api/basic/material';
  import * as MaterialAuditApi from '@/api/basic/materialaudit';
  import MaterialSubmitForm2 from "@/views/basic/material/other/MaterialSubmitForm2.vue";

  export default {
    name: "MaterialDetail",
    components: {MaterialSubmitForm2},
    props: [
      'matrlno', 'matrlStatus', 'parentid', 'invenTorytype'
    ],
    watch: {
      /** 监听主表的关联字段的变化，加载对应的子表数据 */
      matrlno: {
        handler(val) {
          this.editFlag = false;
          this.viewFlag = true;
          this.viewFlag1 = true;
          this.viewFlag2 = true;
          if (val) {
            this.queryMaterialDetail(val);
          }
        },
        immediate: true
      },
    },
    data() {
      return {
        dynamicFields: [{label: "预算价", prop: "budgetPrice"},
          {label: "最大库存", prop: "maxstkqty"},
          {label: "最小库存", prop: "minstkqty"},
          {label: "消耗定额", prop: "consumeQty"},
          {label: "最近采购价", prop: "lastPrice"},
          {label: "计价方式", prop: "priceType"},
          {label: "管理类别", prop: "spManageType"},
          {label: "采购标准", prop: "purStandard"},
          {label: "采购提前期(天)", prop: "predays"},
          {label: "最小订单", prop: "minorderqty"},
          {label: "批量倍数", prop: "qtyMultiple"},
          {label: "采购管理流程", prop: "purProcess"},
          {label: "特控类型", prop: "specCtrlType"},
          {label: "是否批次控制", prop: "batchCtrl"},
          {label: "是否要检化验", prop: "inspectFlag"},
          {label: "是否批号管理", prop: "lotchk"},
          {label: "是否要计量", prop: "measureFlag"},
          {label: "是否非标件", prop: "nonStandardFlag"},
          {label: "验收类型", prop: "acceptanceFlag"},
          {label: "验收标准", prop: "acceptanceStandard"},
          {label: "验收备注", prop: "acceptanceRemark"},
          {label: "制造周期(天)", prop: "manufacPeriod"},
          {label: "保质期", prop: "shelfLife"},
          {label: "保质期单位", prop: "shelfLifeUnit"},
          {label: "生命周期", prop: "lifeCycle"},
          {label: "生命周期单位", prop: "lifeCycleUnit"},
          {label: "库存损耗率", prop: "invLossRate"},
          {label: "物料分级", prop: "itemLevel"},
          {label: "供货渠道", prop: "selfproduce"},
          {label: "是否允许负帐交易", prop: "lastcno"},
          {label: "是否允许负单价交易", prop: "pchkstd"},
          {label: "标准水分", prop: "stardwaterrate"},
          {label: "备注", prop: "remark"},
          {label: "标准单价1", prop: "stdunitcost"},
          {label: "生效日期1", prop: "stdunitcostdate"},
          {label: "标准单价2", prop: "stdunitcost2"},
          {label: "生效日期2", prop: "stdunitcostdate2"},
          {label: "是否需要质检", prop: "ifQualityCheck"},
          {label: "TP产品规范", prop: "tpSpecno"},
          {label: "IP系统产副品代码", prop: "ipSystemSecondCode"},
        ],
        modifiedFields: [],
        editReason: "",
        editInfo: "",
        editList: [],
        tempForm: {},
        editFlag: false,
        viewFlag: true,
        viewFlag1: true,
        viewFlag2: true,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {},
        // 表单校验
        formRules: {
          budgetPrice: [{required: true, message: "预算价不能为空", trigger: "blur"}],
          spManageType: [{required: true, message: "管理类别不能为空", trigger: "blur"}],
          itemLevel: [{required: true, message: "物料分级不能为空", trigger: "blur"}],
          lotchk: [{required: true, message: "是否批号管理不能为空", trigger: "blur"}],
        },
      };
    },
    methods: {
      changeWatch(fieldName) {
        if (this.viewFlag2) {
          return;
        }
        let newValue = this.formData[fieldName];
        let oldValue = this.tempForm[fieldName];
        if (!newValue) {
          newValue = "";
        }
        if (!oldValue) {
          oldValue = "";
        }
        if (oldValue != newValue) {
          // 如果字段不在 modifiedFields 中，则添加
          if (!this.modifiedFields.includes(fieldName)) {
            this.modifiedFields.push(fieldName);
          }
        } else {
          // 如果字段不在 modifiedFields 中，则添加
          if (this.modifiedFields.includes(fieldName)) {
            // 找到要删除元素的索引
            const index = this.modifiedFields.indexOf(fieldName);
            // 如果找到了该元素，则删除
            if (index !== -1) {
              this.modifiedFields.splice(index, 1);
            }
          }
        }
        this.editInfo = "";
        this.editList = [];
        this.modifiedFields.forEach(itemFiledName => {
          let label = "";
          for (let i = 0; i < this.dynamicFields.length; i++) {
            let temp = this.dynamicFields[i];
            if (temp.prop == itemFiledName) {
              label = temp.label;
            }
          }
          let newValueTemp = this.formData[itemFiledName];
          let oldValueTemp = this.tempForm[itemFiledName];
          if (!newValueTemp) {
            newValueTemp = "";
          }
          if (!oldValueTemp) {
            oldValueTemp = "";
          }
          // 展示的字典的值需转换下
          let oldValueTempName = oldValueTemp;
          let newValueTempName = newValueTemp;
          if ('priceType' == itemFiledName) {
            const dics = this.getDictDatas('PRICE_TYPE');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('spManageType' == itemFiledName) {
            const dics = this.getDictDatas('SP_MANAGE_TYPE');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('purProcess' == itemFiledName) {
            const dics = this.getDictDatas('PUR_PROCESS');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('specCtrlType' == itemFiledName) {
            const dics = this.getDictDatas('SPEC_CTRL_TYPE');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('batchCtrl' == itemFiledName || 'inspectFlag' == itemFiledName || 'lotchk' == itemFiledName ||
            'measureFlag' == itemFiledName || 'nonStandardFlag' == itemFiledName || 'lastcno' == itemFiledName ||
            'pchkstd' == itemFiledName) {
            const dics = this.getDictDatas('yes_no');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('acceptanceFlag' == itemFiledName) {
            const dics = this.getDictDatas('ACCEPTANCE_FLAG');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('shelfLifeUnit' == itemFiledName || 'lifeCycleUnit' == itemFiledName) {
            const dics = this.getDictDatas('TIME_DIC');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('itemLevel' == itemFiledName) {
            const dics = this.getDictDatas('ITEM_LEVEL');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('selfproduce' == itemFiledName) {
            const dics = this.getDictDatas('SELF_PRODUCE');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('ifQualityCheck' == itemFiledName) {
            const dics = this.getDictDatas('IF_QUALITY_CHECK');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('tpSpecno' == itemFiledName) {
            const dics = this.getDictDatas('TP_SPECNO');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          } else if ('ipSystemSecondCode' == itemFiledName) {
            const dics = this.getDictDatas('IP_SYSTEM_SECOND_CODE');
            dics.forEach(itemDic => {
              if (oldValueTemp && itemDic.value == oldValueTemp) {
                oldValueTempName = itemDic.label;
              }
              if (newValueTemp && itemDic.value == newValueTemp) {
                newValueTempName = itemDic.label;
              }
            });
          }
          this.editInfo = this.editInfo + label + "：原值【" + oldValueTempName + "】修改为【" + newValueTempName + "】；";
          let map = {
            "label": label,
            "prop": itemFiledName,
            "oldValue": oldValueTemp,
            "newValue": newValueTemp,
          };
          this.editList.push(map);
        });
      },
      async openMaterialSubmitForm2() {
        // 校验主表
        await this.$refs["formRef"].validate();
        if (!this.editReason) {
          this.$modal.msgError("修改原因不能为空！");
          return;
        }
        if (!this.editInfo) {
          this.$modal.msgError("修改信息不能为空！");
          return;
        }
        this.$refs["formRef5"].open();
      },
      async submitFormTechAudit(formData) {
        this.formLoading = true;
        try {
          const data = {
            parentid: this.parentid,
            status: "1",
            type: "2",
            editReason: this.editReason,
            editInfo: this.editInfo,
            editListJson: JSON.stringify(this.editList),
            auditStatus: "2",
            applyUserManagerId: formData.applyUserManagerId,
            applyUserLeaderId: formData.applyUserLeaderId,
          };
          await MaterialAuditApi.createMaterialAudit(data);
          this.$modal.msgSuccess("发起管理属性变更审批流程成功！");
          this.editFlag = false;
          this.viewFlag = true;
          this.viewFlag1 = true;
          this.viewFlag2 = true;
          this.modifiedFields = [];
          this.editList = [];
          this.editReason = "";
          this.editInfo = "";
          this.formData = {...this.tempForm};
        } finally {
          this.formLoading = false;
        }
      },
      async editFormAudit() {
        const res = await MaterialAuditApi.getNoComplateByParentid(this.parentid, "2");
        if ((res.code === 200 || res.code === 0) && res.data == false) {
          this.tempForm = {...this.formData};
          this.editFlag = true;
          this.viewFlag = false;
          this.viewFlag2 = false;
        }
      },
      async queryMaterialDetail(matrlno) {
        this.formLoading = true;
        try {
          const res = await MaterialApi.getMaterialDetailByMatrlno(matrlno);
          if (res && (res.code === 200 || res.code === 0) && res.data) {
            this.formData = res.data;
          } else {
            this.formData = {};
            this.formData.matrlno = matrlno;
            this.formData.acceptanceFlag = "1";
          }
        } finally {
          this.formLoading = false;
        }
      },
      cancelForm() {
        console.info(this.tempForm);
        this.formData = {...this.tempForm};
        this.editFlag = false;
        this.viewFlag = true;
        this.viewFlag1 = true;
        this.viewFlag2 = true;
        this.modifiedFields = [];
        this.editList = [];
        this.editReason = "";
        this.editInfo = "";
      },
      editForm() {
        this.tempForm = {...this.formData};
        this.editFlag = true;
        this.viewFlag = false;
        this.viewFlag1 = false;
      },
      async submitForm() {
        // 校验主表
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialApi.updateMaterialDetail(data);
          }
          // 添加的提交
          else {
            data.parentid = this.parentid;
            await MaterialApi.createMaterialDetail(data);
          }
          await this.queryMaterialDetail(this.matrlno);
          this.$modal.msgSuccess("提交成功");
          this.editFlag = false;
          this.viewFlag = true;
          this.viewFlag1 = true;
          this.viewFlag2 = true;
        } finally {
          this.formLoading = false;
        }
      },
    }
  };
</script>
<style scoped>
  .form-container {
    border: 1px solid #dcdcdc; /* 设置边框 */
    padding: 20px; /* 设置内边距 */
    border-radius: 8px; /* 可选：设置圆角 */
    background-color: #f9f9f9; /* 可选：设置背景颜色 */
  }

  .right-cols-container {
    display: flex;
    justify-content: flex-end; /* 使子元素靠右对齐 */
  }

  /* 自定义修改后的样式 */
  .el-form-item {
    padding: 2px;
    border-radius: 4px;
  }

  .el-form-item.is-modified {
    background-color: #FFFF77;
    padding: 2px;
    border-radius: 4px;
  }
</style>
