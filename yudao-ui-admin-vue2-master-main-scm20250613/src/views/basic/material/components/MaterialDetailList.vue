<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined,'add')"
                   v-hasPermi="['basic:material:create']" :disabled="this.total != 0">新增
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="预算价" align="center" prop="budgetPrice" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="管理类别" align="center" prop="spManageType" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SP_MANAGE_TYPE" :value="scope.row.spManageType"/>
        </template>
      </el-table-column>
      <el-table-column label="是否批号管理" align="center" prop="lotchk" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.YES_NO" :value="scope.row.lotchk"/>
        </template>
      </el-table-column>
      <el-table-column label="物料分级" align="center" prop="itemLevel" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.ITEM_LEVEL" :value="scope.row.itemLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="最大库存" align="center" prop="maxstkqty" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="最小库存" align="center" prop="minstkqty" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="消耗定额" align="center" prop="consumeQty" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="最近采购价" align="center" prop="lastPrice" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="openForm(scope.row.id,'view')"
                     v-hasPermi="['basic:material:query']">详情
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,'edit')"
                     v-hasPermi="['basic:material:update']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['basic:material:delete']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 对话框(添加 / 修改) -->
    <MaterialDetailForm ref="formRef" @success="getList"/>
  </div>
</template>

<script>
  import * as MaterialApi from '@/api/basic/material';
  import MaterialDetailForm from './MaterialDetailForm.vue';

  export default {
    name: "MaterialDetailList",
    components: {
      MaterialDetailForm
    },
    props: [
      'matrlno'
    ],// 料号（主表的关联字段）
    data() {
      return {
        // 遮罩层
        loading: true,
        // 列表的数据
        list: [],
        // 列表的总页数
        total: 0,
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          matrlno: undefined
        }
      };
    },
    watch: {
      /** 监听主表的关联字段的变化，加载对应的子表数据 */
      matrlno: {
        handler(val) {
          this.queryParams.matrlno = val;
          if (val) {
            this.handleQuery();
          }
        },
        immediate: true
      }
    },
    methods: {
      /** 查询列表 */
      async getList() {
        try {
          this.loading = true;
          const res = await MaterialApi.getMaterialDetailPage(this.queryParams);
          this.list = res.data.list;
          this.total = res.data.total;
        } finally {
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNo = 1;
        this.getList();
      },
      /** 添加/修改操作 */
      openForm(id, type) {
        if (!this.matrlno) {
          this.$modal.msgError('请选择一个主档信息');
          return;
        }
        this.$refs["formRef"].open(id, this.matrlno, type);
      },
      /** 删除按钮操作 */
      async handleDelete(row) {
        const id = row.id;
        await this.$modal.confirm('是否确认删除管理属性编号为"' + id + '"的数据项?');
        try {
          await MaterialApi.deleteMaterialDetail(id);
          await this.getList();
          this.$modal.msgSuccess("删除成功");
        } catch {
        }
      },
    }
  };
</script>
