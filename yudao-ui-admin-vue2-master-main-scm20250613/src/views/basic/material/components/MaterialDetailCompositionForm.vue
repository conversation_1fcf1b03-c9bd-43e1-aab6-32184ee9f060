<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
        <el-form-item label="成分代码" prop="compositionCode">
          <el-input v-model="formData.compositionCode" placeholder="请输入成分代码"/>
        </el-form-item>
        <el-form-item label="成分名称" prop="compositionName">
          <el-input v-model="formData.compositionName" placeholder="请输入成分名称"/>
        </el-form-item>
        <el-form-item label="小数位" prop="decimalPlace">
          <el-input v-model="formData.decimalPlace" placeholder="请输入小数位"/>
        </el-form-item>
        <el-form-item label="下限" prop="lowerLimit">
          <el-input v-model="formData.lowerLimit" placeholder="请输入下限"/>
        </el-form-item>
        <el-form-item label="上限" prop="upperLimit">
          <el-input v-model="formData.upperLimit" placeholder="请输入上限"/>
        </el-form-item>
        <el-form-item label="创建者姓名" prop="createEmpNo">
          <el-input v-model="formData.createEmpNo" placeholder="请输入创建者姓名"/>
        </el-form-item>
        <el-form-item label="更新者者姓名" prop="updateEmpNo">
          <el-input v-model="formData.updateEmpNo" placeholder="请输入更新者者姓名"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialApi from '@/api/basic/material';

  export default {
    name: "MaterialDetailCompositionForm",
    components: {},
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          parentid: undefined,
          compositionCode: undefined,
          compositionName: undefined,
          decimalPlace: undefined,
          lowerLimit: undefined,
          upperLimit: undefined,
          createEmpNo: undefined,
          updateEmpNo: undefined,
        },
        // 表单校验
        formRules: {
          parentid: [{required: true, message: "PARENTID不能为空", trigger: "blur"}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id, parentid) {
        this.dialogVisible = true;
        this.reset();
        this.formData.parentid = parentid;
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialApi.getMaterialDetailComposition(id);
            this.formData = res.data;
            this.dialogTitle = "修改物料料号成分资料子";
          } finally {
            this.formLoading = false;
          }
        }
        this.dialogTitle = "新增物料料号成分资料子";
      },
      /** 提交按钮 */
      async submitForm() {
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialApi.updateMaterialDetailComposition(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialApi.createMaterialDetailComposition(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          parentid: undefined,
          compositionCode: undefined,
          compositionName: undefined,
          decimalPlace: undefined,
          lowerLimit: undefined,
          upperLimit: undefined,
          createEmpNo: undefined,
          updateEmpNo: undefined,
        };
        this.resetForm("formRef");
      },
    }
  };
</script>
