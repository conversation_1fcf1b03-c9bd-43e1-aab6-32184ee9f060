<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <div>
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-edit" size="mini" @click="editForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="(matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5')  || editFlag">
            修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-outline" size="mini" @click="editFormAudit"
                     v-hasPermi="['basic:material:update']" v-if="matrlStatus == '2'" :disabled="editFlag">技术属性变更
          </el-button>
        </el-col>
      </div>
      <div class="right-cols-container">
        <el-col :span="1.5" v-show="!viewFlag1" style="margin-left: 100px;">
          <el-button type="primary" plain round icon="el-icon-thumb" size="mini" @click="submitForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">提交
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag1">
          <el-button type="danger" plain round icon="el-icon-close" size="mini" @click="cancelForm"
                     v-hasPermi="['basic:material:update']"
                     :disabled="matrlStatus != '0' && matrlStatus != '3' && matrlStatus != '4' && matrlStatus != '5'">取消
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag2" style="margin-left: 100px;">
          <el-button type="primary" plain round icon="el-icon-thumb" size="mini" @click="openMaterialSubmitForm2"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '2'">提交审批
          </el-button>
        </el-col>
        <el-col :span="1.5" v-show="!viewFlag2">
          <el-button type="danger" plain round icon="el-icon-close" size="mini" @click="cancelForm"
                     v-hasPermi="['basic:material:update']" :disabled="matrlStatus != '2'">取消
          </el-button>
        </el-col>
      </div>
    </el-row>
    <!-- 表单 -->
    <div class="form-container">
      <el-form :model="form" ref="techForm" size="small" :inline="true" label-width="120px"
               :rules="formRules" v-loading="formLoading">
        <el-row :gutter="20">
          <div v-for="(field, index) in dynamicFields" :key="field.prop">
            <el-col :span="8" :xs="24">
              <el-form-item :label="field.label" :prop="field.prop"
                            :class="{ 'is-modified': modifiedFields.includes(field.prop) }">
                <el-input v-model="form[field.prop]" style="width: 190px" :disabled="viewFlag"
                          @change="changeWatch(field.prop)"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </el-row>
        <el-row :gutter="20" v-show="!viewFlag2">
          <el-divider></el-divider>
          <el-col :span="24" :xs="24">
            <el-form-item label="修改原因" :prop="editReason">
              <el-input v-model="editReason" type="textarea" :rows="2" placeholder="请输入修改原因"
                        style="width: 875px" :disabled="viewFlag"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" :xs="24">
            <el-form-item label="修改信息" :prop="editInfo">
              <el-input v-model="editInfo" type="textarea" :rows="2" disabled
                        style="width: 875px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <MaterialSubmitForm2 ref="formRef5" @success="submitFormTechAudit"/>
  </div>
</template>

<script>
  import * as MaterialIndexTechValueApi from '@/api/basic/materialindextechvalue';
  import * as MaterialAuditApi from '@/api/basic/materialaudit';
  import MaterialSubmitForm2 from "@/views/basic/material/other/MaterialSubmitForm2.vue";

  export default {
    name: "MaterialTech",
    components: {MaterialSubmitForm2},
    props: [
      'matrlno', 'matrlStatus', 'parentid', 'leafTypeCode', 'leafTypeName'
    ],
    watch: {
      /** 监听主表的关联字段的变化，加载对应的子表数据 */
      matrlno: {
        handler(val) {
          this.editFlag = false;
          this.viewFlag = true;
          if (val) {
            this.queryMaterialTechByLeafTypeCode(val);
          }
        },
        immediate: true
      },
    },
    data() {
      return {
        formLoading: false,
        tempForm: {},
        editFlag: false,
        viewFlag: true,
        viewFlag1: false,
        viewFlag2: false,
        dynamicFields: [],
        form: {},
        formRules: {},
        modifiedFields: [],
        editReason: "",
        editInfo: "",
        editList: [],
      };
    },
    created() {
    },
    methods: {
      async openMaterialSubmitForm2() {
        // 校验主表
        await this.$refs["techForm"].validate();
        if(!this.editReason){
          this.$modal.msgError("修改原因不能为空！");
          return;
        }
        if(!this.editInfo){
          this.$modal.msgError("修改信息不能为空！");
          return;
        }
        this.$refs["formRef5"].open();
      },
      async submitFormTechAudit(formData) {
        try {
          this.formLoading = true;
          const data = {
            parentid: this.parentid,
            status: "1",
            type: "1",
            editReason: this.editReason,
            editInfo: this.editInfo,
            editListJson: JSON.stringify(this.editList),
            auditStatus: "2",
            applyUserManagerId: formData.applyUserManagerId,
            applyUserLeaderId: formData.applyUserLeaderId,
          };
          await MaterialAuditApi.createMaterialAudit(data);
          this.$modal.msgSuccess("发起技术属性变更审批流程成功！");
          this.editFlag = false;
          this.viewFlag = true;
          this.viewFlag1 = true;
          this.viewFlag2 = true;
          this.modifiedFields = [];
          this.editList = [];
          this.editReason = "";
          this.editInfo = "";
          this.form = {...this.tempForm};
        } finally {
          this.formLoading = false;
        }
      },
      async editFormAudit() {
        const res = await MaterialAuditApi.getNoComplateByParentid(this.parentid,"1");
        if ((res.code === 200 || res.code === 0) && res.data == false) {
            this.tempForm = {...this.form};
            this.editFlag = true;
            this.viewFlag = false;
            this.viewFlag2 = false;
        }
      },
      changeWatch(fieldName) {
        if(this.viewFlag2){
          return;
        }
        let newValue = this.form[fieldName];
        let oldValue = this.tempForm[fieldName];
        if (!newValue) {
          newValue = "";
        }
        if (!oldValue) {
          oldValue = "";
        }
        if (oldValue != newValue) {
          // 如果字段不在 modifiedFields 中，则添加
          if (!this.modifiedFields.includes(fieldName)) {
            this.modifiedFields.push(fieldName);
          }
        } else {
          // 如果字段不在 modifiedFields 中，则添加
          if (this.modifiedFields.includes(fieldName)) {
            // 找到要删除元素的索引
            const index = this.modifiedFields.indexOf(fieldName);
            // 如果找到了该元素，则删除
            if (index !== -1) {
              this.modifiedFields.splice(index, 1);
            }
          }
        }
        this.editInfo = "";
        this.editList = [];
        this.modifiedFields.forEach(itemFiledName => {
          let label = "";
          for (let i = 0; i < this.dynamicFields.length; i++) {
            let temp = this.dynamicFields[i];
            if (temp.prop == itemFiledName) {
              label = temp.label;
            }
          }
          let newValueTemp = this.form[itemFiledName];
          let oldValueTemp = this.tempForm[itemFiledName];
          if (!newValueTemp) {
            newValueTemp = "";
          }
          if (!oldValueTemp) {
            oldValueTemp = "";
          }
          this.editInfo = this.editInfo + label + "：原值【" + oldValueTemp + "】修改为【" + newValueTemp + "】；";
          let map = {
            "label" : label,
            "prop" : itemFiledName,
            "oldValue" : oldValueTemp,
            "newValue" : newValueTemp,
          };
          this.editList.push(map);
        });
      },
      async submitForm() {
        // 校验主表
        await this.$refs["techForm"].validate();
        this.formLoading = true;
        try {
          const data = this.form;
          // 修改的提交
          if (data.id) {
            await MaterialIndexTechValueApi.updateMaterialIndexTechValue(data);
          }
          // 添加的提交
          else {
            data.parentid = this.parentid;
            data.leafTypeCode = this.leafTypeCode;
            data.leafTypeName = this.leafTypeName;
            data.matrlno = this.matrlno;
            await MaterialIndexTechValueApi.createMaterialIndexTechValue(data);
          }
          await this.queryMaterialTechByLeafTypeCode(this.matrlno);
          this.$modal.msgSuccess("提交成功");
          this.editFlag = false;
          this.viewFlag = true;
          this.viewFlag1 = true;
          this.viewFlag2 = true;
        } finally {
          this.formLoading = false;
        }
      },
      cancelForm() {
        this.form = {...this.tempForm};
        this.editFlag = false;
        this.viewFlag = true;
        this.viewFlag1 = true;
        this.viewFlag2 = true;
        this.modifiedFields = [];
        this.editList = [];
        this.editReason = "";
        this.editInfo = "";
      },
      editForm() {
        this.tempForm = {...this.form};
        this.editFlag = true;
        this.viewFlag = false;
        this.viewFlag1 = false;
      },
      async queryMaterialTechByLeafTypeCode(matrlno) {
        this.viewFlag = true;
        this.viewFlag1 = true;
        this.viewFlag2 = true;
        this.dynamicFields = [];
        this.modifiedFields = [];
        this.editList = [];
        this.editReason = "";
        this.editInfo = "";
        this.form = {};
        this.tempForm = {};
        this.formRules = {};
        const res = await MaterialIndexTechValueApi.getTechKeyAndValueByMaterialNo(matrlno);
        if (res && (res.code === 200 || res.code === 0) && res.data) {
          if (res.data.techFieldList) {
            if (res.data.techFieldList.length > 0) {
              for (let i = 0; i <= res.data.techFieldList.length; i++) {
                let fieldItem = res.data.techFieldList[i];
                if (fieldItem) {
                  let map = {label: fieldItem.tecName, prop: fieldItem.tecLabel};
                  this.dynamicFields.push(map);
                  if (fieldItem.required == "Y") {
                    this.$set(this.formRules, fieldItem.tecLabel, [{
                      required: true,
                      message: fieldItem.tecName + `是必填项`,
                      trigger: 'blur'
                    }]);
                  }
                }
              }
            }
            if (res.data.valueData) {
              this.form = res.data.valueData;
            }
          }
        }
        this.$forceUpdate();
      },
    }
  };
</script>
<style scoped>
  .form-container {
    border: 1px solid #dcdcdc; /* 设置边框 */
    padding: 20px; /* 设置内边距 */
    border-radius: 8px; /* 可选：设置圆角 */
    background-color: #f9f9f9; /* 可选：设置背景颜色 */
  }

  .right-cols-container {
    display: flex;
    justify-content: flex-end; /* 使子元素靠右对齐 */
  }

  /* 自定义修改后的样式 */
  .el-form-item {
    padding: 2px;
    border-radius: 4px;
  }

  .el-form-item.is-modified {
    background-color: #FFFF77;
    padding: 2px;
    border-radius: 4px;
  }
</style>
