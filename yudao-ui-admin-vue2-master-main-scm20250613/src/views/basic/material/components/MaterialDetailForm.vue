<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="75%" v-dialogDrag append-to-body>
      <el-form ref="formRef" size="small" :inline="true" :model="formData" :rules="formRules" v-loading="formLoading"
               label-width="110px">
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="预算价" prop="budgetPrice">
              <el-input v-model="formData.budgetPrice" placeholder="请输入预算价" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最大库存" prop="maxstkqty">
              <el-input v-model="formData.maxstkqty" placeholder="请输入最大库存" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最小库存" prop="minstkqty">
              <el-input v-model="formData.minstkqty" placeholder="请输入最小库存" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="消耗定额" prop="consumeQty">
              <el-input v-model="formData.consumeQty" placeholder="请输入消耗定额" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最近采购价" prop="lastPrice">
              <el-input v-model="formData.lastPrice" placeholder="请输入最近采购价" disabled style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="计价方式" prop="priceType">
              <el-select v-model="formData.priceType" placeholder="请选择计价方式" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PRICE_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="管理类别" prop="spManageType">
              <el-select v-model="formData.spManageType" placeholder="请选择管理类别" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SP_MANAGE_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="采购标准" prop="purStandard">
              <el-input v-model="formData.purStandard" placeholder="请输入采购标准" :disabled="viewFlag"  style="width: 190px"/>
            </el-form-item>
          </el-col><el-col :span="8" :xs="24">
          <el-form-item label="采购提前期(天)" prop="predays">
            <el-input v-model="formData.predays" placeholder="请输入采购提前期(天)" :disabled="viewFlag"  style="width: 190px"/>
          </el-form-item>
        </el-col><el-col :span="8" :xs="24">
          <el-form-item label="最小订单" prop="minorderqty">
            <el-input v-model="formData.minorderqty" placeholder="请输入最小订单" :disabled="viewFlag"  style="width: 190px"/>
          </el-form-item>
        </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="批量倍数" prop="qtyMultiple">
              <el-input v-model="formData.qtyMultiple" placeholder="请输入批量倍数" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="采购管理流程" prop="purProcess">
              <el-select v-model="formData.purProcess" placeholder="请选择采购管理流程" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PUR_PROCESS)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="特控类型" prop="specCtrlType">
              <el-select v-model="formData.specCtrlType" placeholder="请选择特控类型" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SPEC_CTRL_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="是否批次控制" prop="batchCtrl">
              <el-select v-model="formData.batchCtrl" placeholder="请选择是否批次控制" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否要检化验" prop="inspectFlag">
              <el-select v-model="formData.inspectFlag" placeholder="请选择是否要检化验" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否批号管理" prop="lotchk">
              <el-select v-model="formData.lotchk" placeholder="请选择是否批号管理" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="是否要计量" prop="measureFlag">
              <el-select v-model="formData.measureFlag" placeholder="请选择是否要计量" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="是否非标件" prop="nonStandardFlag">
              <el-select v-model="formData.nonStandardFlag" placeholder="请选择是否非标件" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="验收类型" prop="acceptanceFlag">
              <el-select v-model="formData.acceptanceFlag" placeholder="请选择验收类型" size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ACCEPTANCE_FLAG)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="验收标准" prop="acceptanceStandard">
              <el-input v-model="formData.acceptanceStandard" placeholder="请输入验收标准" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="验收备注" prop="acceptanceRemark">
              <el-input v-model="formData.acceptanceRemark" placeholder="请输入验收备注" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="制造周期(天)" prop="manufacPeriod">
              <el-input v-model="formData.manufacPeriod" placeholder="请输入制造周期(天)" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="保质期" prop="shelfLife">
              <el-input v-model="formData.shelfLife" placeholder="请输入保质期" :disabled="viewFlag" style="width: 110px"/>
              <el-select v-model="formData.shelfLifeUnit" placeholder="请选择" clearable size="small"
                         :disabled="viewFlag" style="width: 80px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TIME_DIC)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="生命周期" prop="lifeCycle">
              <el-input v-model="formData.lifeCycle" placeholder="请输入生命周期" :disabled="viewFlag" style="width: 110px"/>
              <el-select v-model="formData.lifeCycleUnit" placeholder="请选择" clearable size="small"
                         :disabled="viewFlag" style="width: 80px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TIME_DIC)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="库存损耗率" prop="invLossRate">
              <el-input v-model="formData.invLossRate" placeholder="库存损耗率" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="物料分级" prop="itemLevel">
              <el-select v-model="formData.itemLevel" placeholder="请选择物料分级" clearable size="small"
                         :disabled="viewFlag" style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ITEM_LEVEL)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24"></el-col>
          <el-col :span="8" :xs="24"></el-col>
        </el-row>
        <!--<el-form-item label="物料采购天数" prop="daysofpur">-->
          <!--<el-input v-model="formData.daysofpur" placeholder="请输入物料采购天数"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="请购点" prop="rop">-->
          <!--<el-input v-model="formData.rop" placeholder="请输入请购点"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="消耗定额" prop="minconsumpqty">-->
          <!--<el-input v-model="formData.minconsumpqty" placeholder="请输入消耗定额"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="设备用量" prop="meuseqty">-->
          <!--<el-input v-model="formData.meuseqty" placeholder="请输入设备用量"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="平均日用量" prop="avgdailyqty">-->
          <!--<el-input v-model="formData.avgdailyqty" placeholder="请输入平均日用量"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="预计年用量" prop="yearuseqty">-->
          <!--<el-input v-model="formData.yearuseqty" placeholder="请输入预计年用量"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="有效日数" prop="availabydays">-->
          <!--<el-input v-model="formData.availabydays" placeholder="请输入有效日数"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否呆料" prop="isidle">-->
          <!--<el-select v-model="formData.isidle" placeholder="请选择是否呆料">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否批价管理" prop="pricechk">-->
          <!--<el-select v-model="formData.pricechk" placeholder="请选择是否批价管理">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否时效控管" prop="expirechk">-->
          <!--<el-select v-model="formData.expirechk" placeholder="请选择是否时效控管">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否固定资产" prop="fixed">-->
          <!--<el-select v-model="formData.fixed" placeholder="请选择是否固定资产">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="固定资产大类" prop="fixedassetstype">-->
          <!--<el-input v-model="formData.fixedassetstype" placeholder="请输入固定资产大类"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="供货渠道" prop="selfproduce">-->
          <!--<el-input v-model="formData.selfproduce" placeholder="请输入供货渠道"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="产品代码" prop="prodcode">-->
          <!--<el-input v-model="formData.prodcode" placeholder="请输入产品代码"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="预设品级" prop="matrlGrade">-->
          <!--<el-input v-model="formData.matrlGrade" placeholder="请输入预设品级"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="预设库栋代号" prop="locno">-->
          <!--<el-input v-model="formData.locno" placeholder="请输入预设库栋代号"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="预设储位代号" prop="childlocno">-->
          <!--<el-input v-model="formData.childlocno" placeholder="请输入预设储位代号"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="预设排层位代号" prop="layer">-->
          <!--<el-input v-model="formData.layer" placeholder="请输入预设排层位代号"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否有替代料" prop="ownreplacement">-->
          <!--<el-select v-model="formData.ownreplacement" placeholder="请选择是否有替代料">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否MPS主件" prop="mpschk">-->
          <!--<el-select v-model="formData.mpschk" placeholder="请选择是否MPS主件">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="超收比例" prop="overrecvrate">-->
          <!--<el-input v-model="formData.overrecvrate" placeholder="请输入超收比例"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="状态码" prop="stus">-->
          <!--<el-input v-model="formData.stus" placeholder="请输入状态码"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="MRP采购交期提前天数" prop="mrppurdate">-->
          <!--<el-input v-model="formData.mrppurdate" placeholder="输入MRP采购交期提前天数"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否为主营" prop="longcno">-->
          <!--<el-select v-model="formData.longcno" placeholder="请选择是否为主营">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否允许负帐交易" prop="lastcno">-->
          <!--<el-select v-model="formData.lastcno" placeholder="请选择是否允许负帐交易">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否允许负单价交易" prop="pchkstd">-->
          <!--<el-select v-model="formData.pchkstd" placeholder="请选择是否允许负单价交易">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否受开票日限制" prop="cchkstd">-->
          <!--<el-select v-model="formData.cchkstd" placeholder="请选择是否受开票日限制">-->
            <!--<el-option v-for="dict in this.getDictDatas(DICT_TYPE.YES_NO)"-->
                       <!--:key="dict.value" :label="dict.label" :value="dict.value"/>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="标准水分" prop="stardwaterrate">-->
          <!--<el-input v-model="formData.stardwaterrate" placeholder="请输入标准水分"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="堆位标准水分" prop="pilestardwaterrate">-->
          <!--<el-input v-model="formData.pilestardwaterrate" placeholder="请输入堆位标准水分"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="国外运费率" prop="blenDrate">-->
          <!--<el-input v-model="formData.blenDrate" placeholder="请输入国外运费率"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="国内运杂费率" prop="dmblendrate">-->
          <!--<el-input v-model="formData.dmblendrate" placeholder="请输入国内运杂费率"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="标准成本单价" prop="stdunitcost">-->
          <!--<el-input v-model="formData.stdunitcost" placeholder="请输入标准成本单价"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="移动平均成本单价" prop="avgunitcost">-->
          <!--<el-input v-model="formData.avgunitcost" placeholder="请输入移动平均成本单价"/>-->
        <!--</el-form-item>-->
        <!--&lt;!&ndash;        <el-form-item label="标准成本单价2" prop="stdunitcost2">&ndash;&gt;-->
        <!--&lt;!&ndash;          <el-input v-model="formData.stdunitcost2" placeholder="请输入标准成本单价2"/>&ndash;&gt;-->
        <!--&lt;!&ndash;        </el-form-item>&ndash;&gt;-->
        <!--<el-form-item label="标准价生效日" prop="stdunitcostdate">-->
          <!--<el-date-picker clearable v-model="formData.stdunitcostdate" type="date" value-format="timestamp"-->
                          <!--placeholder="选择标准价生效日"/>-->
        <!--</el-form-item>-->
        <!--&lt;!&ndash;        <el-form-item label="标准价生效日2" prop="stdunitcostdate2">&ndash;&gt;-->
        <!--&lt;!&ndash;          <el-date-picker clearable v-model="formData.stdunitcostdate2" type="date" value-format="timestamp"&ndash;&gt;-->
        <!--&lt;!&ndash;                          placeholder="选择标准价生效日2"/>&ndash;&gt;-->
        <!--&lt;!&ndash;        </el-form-item>&ndash;&gt;-->
        <!--<el-form-item label="产品规范" prop="specno">-->
          <!--<el-input v-model="formData.specno" placeholder="请输入产品规范"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="质量检测标准" prop="qcsno">-->
          <!--<el-input v-model="formData.qcsno" placeholder="请输入质量检测标准"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="备注" prop="remark">-->
          <!--<el-input v-model="formData.remark" placeholder="请输入备注"/>-->
        <!--</el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as MaterialApi from '@/api/basic/material';

  export default {
    name: "MaterialDetailForm",
    components: {},
    data() {
      return {
        viewFlag: false,
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
          id: undefined,
          compid: undefined,
          matrlno: undefined,
          matrlIndexid: undefined,
          invenTorytype: undefined,
          purchaser: undefined,
          predays: undefined,
          daysofpur: undefined,
          rop: undefined,
          minstkqty: undefined,
          maxstkqty: undefined,
          minconsumpqty: undefined,
          meuseqty: undefined,
          avgdailyqty: undefined,
          yearuseqty: undefined,
          availabydays: undefined,
          isidle: undefined,
          lotchk: undefined,
          pricechk: undefined,
          expirechk: undefined,
          fixed: undefined,
          fixedassetstype: undefined,
          selfproduce: undefined,
          prodcode: undefined,
          matrlGrade: undefined,
          locno: undefined,
          childlocno: undefined,
          layer: undefined,
          ownreplacement: undefined,
          minorderqty: undefined,
          mpschk: undefined,
          overrecvrate: undefined,
          stus: undefined,
          mrppurdate: undefined,
          longcno: undefined,
          lastcno: undefined,
          pchkstd: undefined,
          cchkstd: undefined,
          stardwaterrate: undefined,
          pilestardwaterrate: undefined,
          blenDrate: undefined,
          dmblendrate: undefined,
          stdunitcost: undefined,
          avgunitcost: undefined,
          stdunitcost2: undefined,
          stdunitcostdate: undefined,
          stdunitcostdate2: undefined,
          specno: undefined,
          qcsno: undefined,
          remark: undefined,
          budgetPrice: undefined,
          consumeQty: undefined,
          lastPrice: undefined,
          priceType: undefined,
          spManageType: undefined,
          purStandard: undefined,
          qtyMultiple: undefined,
          purProcess: undefined,
          specCtrlType: undefined,
          batchCtrl: undefined,
          inspectFlag: undefined,
          measureFlag: undefined,
          nonStandardFlag: undefined,
          acceptanceFlag: undefined,
          acceptanceStandard: undefined,
          acceptanceRemark: undefined,
          manufacPeriod: undefined,
          shelfLife: undefined,
          lifeCycle: undefined,
          invLossRate: undefined,
          itemLevel: undefined,
          shelfLifeUnit: undefined,
          lifeCycleUnit: undefined,
        },
        // 表单校验
        formRules: {
          budgetPrice: [{required: true, message: "预算价不能为空", trigger: "blur"}],
          spManageType: [{required: true, message: "管理类别不能为空", trigger: "blur"}],
          itemLevel: [{required: true, message: "物料分级不能为空", trigger: "blur"}],
          lotchk: [{required: true, message: "是否批号管理不能为空", trigger: "blur"}],
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id, matrlno, type) {
        this.reset();
        this.formData.matrlno = matrlno;
        if (type == 'view') {
          this.viewFlag = true;
          this.dialogTitle = "查看管理属性";
        } else {
          this.viewFlag = false;
          if (type == 'add') {
            this.formData.acceptanceFlag = "1";
            this.dialogTitle = "新增管理属性";
          } else {
            this.dialogTitle = "修改管理属性";
          }
        }
        this.dialogVisible = true;

        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await MaterialApi.getMaterialDetail(id);
            this.formData = res.data;
          } finally {
            this.formLoading = false;
          }
        }
      },
      /** 提交按钮 */
      async submitForm() {
        await this.$refs["formRef"].validate();
        this.formLoading = true;
        try {
          const data = this.formData;
          // 修改的提交
          if (data.id) {
            await MaterialApi.updateMaterialDetail(data);
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.$emit('success');
            return;
          }
          // 添加的提交
          await MaterialApi.createMaterialDetail(data);
          this.$modal.msgSuccess("新增成功");
          this.dialogVisible = false;
          this.$emit('success');
        } finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
          id: undefined,
          compid: undefined,
          matrlno: undefined,
          matrlIndexid: undefined,
          invenTorytype: undefined,
          purchaser: undefined,
          predays: undefined,
          daysofpur: undefined,
          rop: undefined,
          minstkqty: undefined,
          maxstkqty: undefined,
          minconsumpqty: undefined,
          meuseqty: undefined,
          avgdailyqty: undefined,
          yearuseqty: undefined,
          availabydays: undefined,
          isidle: undefined,
          lotchk: undefined,
          pricechk: undefined,
          expirechk: undefined,
          fixed: undefined,
          fixedassetstype: undefined,
          selfproduce: undefined,
          prodcode: undefined,
          matrlGrade: undefined,
          locno: undefined,
          childlocno: undefined,
          layer: undefined,
          ownreplacement: undefined,
          minorderqty: undefined,
          mpschk: undefined,
          overrecvrate: undefined,
          stus: undefined,
          mrppurdate: undefined,
          longcno: undefined,
          lastcno: undefined,
          pchkstd: undefined,
          cchkstd: undefined,
          stardwaterrate: undefined,
          pilestardwaterrate: undefined,
          blenDrate: undefined,
          dmblendrate: undefined,
          stdunitcost: undefined,
          avgunitcost: undefined,
          stdunitcost2: undefined,
          stdunitcostdate: undefined,
          stdunitcostdate2: undefined,
          specno: undefined,
          qcsno: undefined,
          remark: undefined,
          budgetPrice: undefined,
          consumeQty: undefined,
          lastPrice: undefined,
          priceType: undefined,
          spManageType: undefined,
          purStandard: undefined,
          qtyMultiple: undefined,
          purProcess: undefined,
          specCtrlType: undefined,
          batchCtrl: undefined,
          inspectFlag: undefined,
          measureFlag: undefined,
          nonStandardFlag: undefined,
          acceptanceFlag: undefined,
          acceptanceStandard: undefined,
          acceptanceRemark: undefined,
          manufacPeriod: undefined,
          shelfLife: undefined,
          lifeCycle: undefined,
          invLossRate: undefined,
          itemLevel: undefined,
          shelfLifeUnit: undefined,
          lifeCycleUnit: undefined,
        };
        this.resetForm("formRef");
      },
    }
  };
</script>
