<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small" style="width: 140px">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BASIC_MATERIAL_AUDIT)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="matrlStatus">
        <el-select v-model="queryParams.matrlStatus" placeholder="请选择审批状态" clearable size="small"
                   style="width: 140px">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BPM_TASK_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="物料类型" prop="invenTorytype">-->
      <!--        <el-select v-model="queryParams.invenTorytype" placeholder="请选择物料类型" clearable size="small"-->
      <!--                   style="width: 140px">-->
      <!--          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"-->
      <!--                     :key="dict.value" :label="dict.label" :value="dict.value"/>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="料号" prop="matrlno">
        <el-input v-model="queryParams.matrlno" placeholder="请输入料号" clearable @keyup.enter.native="handleQuery"
                  style="width: 140px"/>
      </el-form-item>
      <el-form-item label="中文品名" prop="cnmdesc">
        <el-input v-model="queryParams.cnmdesc" placeholder="请输入中文品名" clearable style="width: 140px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="规格型号" prop="nmspec">
        <el-input v-model="queryParams.nmspec" placeholder="请输入规格型号" clearable style="width: 140px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="大类" prop="bigTypeCode">
        <el-input v-model="queryParams.bigTypeCode" placeholder="请输入/选择" style="width: 145px;"
                  @blur="chooseTypeName('1',queryParams.bigTypeCode)">
          <el-button slot="prepend" type="primary" plain icon="el-icon-search"
                     style="width: 40px" @click="openBigTypeForm"/>
        </el-input>
        <el-input v-model="bigTypeName" style="width: 120px;" disabled/>
      </el-form-item>
      <el-form-item label="中类" prop="midTypeCode">
        <el-input v-model="queryParams.midTypeCode" placeholder="请输入/选择" style="width: 145px;"
                  @blur="chooseTypeName('2',queryParams.midTypeCode)">
          <el-button slot="prepend" type="primary" plain icon="el-icon-search"
                     style="width: 40px" @click="openMidTypeForm"/>
        </el-input>
        <el-input v-model="midTypeName" style="width: 120px;" disabled/>
      </el-form-item>
      <el-form-item label="叶类" prop="leafTypeCode">
        <el-input v-model="queryParams.leafTypeCode" placeholder="请输入/选择" style="width: 145px;"
                  @blur="chooseTypeName('3',queryParams.leafTypeCode)">
          <el-button slot="prepend" type="primary" plain icon="el-icon-search"
                     style="width: 40px" @click="openLeafTypeForm"/>
        </el-input>
        <el-input v-model="leafTypeName" style="width: 120px;" disabled/>
      </el-form-item>
      <el-form-item label="申请人" prop="createEmpNo">
        <el-input v-model="queryParams.createEmpNo" placeholder="请输入申请人" clearable style="width: 140px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined,'add')"
                   v-hasPermi="['basic:material:create']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-printer" size="mini" @click="openQrcodeForm"
                   v-hasPermi="['basic:material:query']">二维码批量打印
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                   :loading="exportLoading"
                   v-hasPermi="['basic:material:export']">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :highlight-current-row="true"
      :show-overflow-tooltip="true"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" fixed="left"/>
      <el-table-column fixed="left" label="状态" align="center" prop="status" width="120"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.BASIC_MATERIAL_AUDIT" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column fixed="left" label="审批状态" align="center" prop="matrlStatus" width="120"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.matrlStatus"/>
        </template>
      </el-table-column>
      <el-table-column fixed="left" label="料号" align="center" prop="matrlno" width="130"
                       :show-overflow-tooltip="true"/>
      <el-table-column fixed="left" label="中文品名" align="center" prop="cnmdesc" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="物料类型" align="center" prop="invenTorytype" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.INVEN_TORY_TYPE" :value="scope.row.invenTorytype"/>
        </template>
      </el-table-column>
      <el-table-column label="计量单位" align="center" prop="unitinv" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.STOCK_UNIT" :value="scope.row.unitinv"/>
        </template>
      </el-table-column>
      <el-table-column label="规格型号" align="center" prop="nmspec" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="材质" align="center" prop="quality" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="图号" align="center" prop="picno" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="物料短描述" align="center" prop="shortDesc" width="250"
                       :show-overflow-tooltip="true"/>
<!--      <el-table-column label="创建人" align="center" prop="creator" width="100"-->
<!--                       :show-overflow-tooltip="true"/>-->
      <el-table-column label="创建人" align="center" prop="createEmpNo" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="150"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请原因" align="center" prop="applyReason" width="300"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="物料属性" align="center" prop="matrlType" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MATRL_TYPE" :value="scope.row.matrlType"/>
        </template>
      </el-table-column>
      <el-table-column label="物料重要程度" align="center" prop="matrlDegree" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.MATRL_DEGREE" :value="scope.row.matrlDegree"/>
        </template>
      </el-table-column>
      <el-table-column label="国产/进口" align="center" prop="isMadeInChina" width="100"
                       :show-overflow-tooltip="true">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.IS_MADEINCHINA" :value="scope.row.isMadeInChina"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="openForm(scope.row.id,'view')"
                     v-hasPermi="['basic:material:query']">详情
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.id,'edit')"
                     v-hasPermi="['basic:material:update']"
                     :disabled="scope.row.matrlStatus != '0' && scope.row.matrlStatus != '3' && scope.row.matrlStatus != '4' && scope.row.matrlStatus != '5'">
            修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['basic:material:delete']"
                     :disabled="scope.row.matrlStatus != '0' && scope.row.matrlStatus != '3' && scope.row.matrlStatus != '4' && scope.row.matrlStatus != '5'">
            删除
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-top-right" @click="openMaterialSubmitForm(scope.row)"
                     v-hasPermi="['basic:material:update']"
                     v-if="scope.row.matrlStatus == '0' || scope.row.matrlStatus == '3' || scope.row.matrlStatus == '4' || scope.row.matrlStatus == '5'">
            发起审批
          </el-button>
          <el-dropdown trigger="click" v-if="scope.row.matrlStatus == '2'" @command="openMaterialSubmitForm2">
            <span class="el-dropdown-link">
              状态变更<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{'type': '3','row': scope.row}">状态变有效</el-dropdown-item>
              <el-dropdown-item :command="{'type': '4','row': scope.row}">状态变无效</el-dropdown-item>
              <el-dropdown-item :command="{'type': '5','row': scope.row}">状态变删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 子表的列表 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="技术属性" name="materialTech" v-if="currentRow && currentRow.invenTorytype!='R'">
        <MaterialTech v-if="currentRow && currentRow.id" :matrlno="currentRow.matrlno"
                      :matrlStatus="currentRow.matrlStatus" :parentid="currentRow.id"
                      :leafTypeCode="currentRow.leafTypeCode" :leafTypeName="currentRow.leafTypeName"/>
      </el-tab-pane>
      <el-tab-pane label="管理属性" name="materialDetail" v-if="currentRow && currentRow.invenTorytype!='R'">
        <!--<MaterialDetailList v-if="currentRow && currentRow.id" :matrlno="currentRow.matrlno"/>-->
        <MaterialDetail v-if="currentRow && currentRow.id" :matrlno="currentRow.matrlno"
                        :matrlStatus="currentRow.matrlStatus" :parentid="currentRow.id"
                        :invenTorytype="currentRow.invenTorytype"/>
      </el-tab-pane>
      <el-tab-pane label="成分资料" name="materialDetailComposition"
                   v-if="currentRow && currentRow.invenTorytype == 'R'">
        <MaterialDetailCompositionList v-if="currentRow && currentRow.id" :parentid="currentRow.id"
                                       :matrlStatus="currentRow.matrlStatus"/>
      </el-tab-pane>
      <el-tab-pane label="附件" name="materialFileForm">
        <MaterialFileForm v-if="currentRow && currentRow.id" :parentid="currentRow.id"
                          :matrlStatus="currentRow.matrlStatus"/>
      </el-tab-pane>
    </el-tabs>
    <!-- 对话框(添加 / 修改) -->
    <MaterialForm ref="formRef" @success="getList"/>
    <MaterialIndexTreeForm ref="formRef2" @success="writebackBigTypeTree"/>
    <MaterialIndexTreeForm ref="formRef3" @success="writebackMidTypeTree"/>
    <MaterialIndexTreeForm ref="formRef4" @success="writebackLeafTypeTree"/>
    <MaterialSubmitForm ref="formRef5" @success="getList"/>
    <MaterialSubmitForm3 ref="formRef6" @success="handleCommand"/>
  </div>
</template>

<script>
import * as MaterialApi from '@/api/basic/material';
import * as MaterialIndexApi from '@/api/basic/materialindex';
import * as MaterialAuditApi from '@/api/basic/materialaudit';
import MaterialForm from './MaterialForm.vue';
import MaterialDetailList from './components/MaterialDetailList.vue';
import MaterialTech from './components/MaterialTech.vue';
import MaterialFileForm from './components/MaterialFileForm.vue';
import MaterialDetail from './components/MaterialDetail.vue';
import MaterialIndexTreeForm from './other/MaterialIndexTreeForm.vue';
import MaterialDetailCompositionList from './components/MaterialDetailCompositionList.vue';
import MaterialSubmitForm from '@/views/basic/material/other/MaterialSubmitForm.vue';
import MaterialSubmitForm3 from "@/views/basic/material/other/MaterialSubmitForm3.vue";

export default {
  name: "Material",
  components: {
    MaterialSubmitForm3,
    MaterialForm,
    MaterialDetailList,
    MaterialTech,
    MaterialFileForm,
    MaterialDetail,
    MaterialIndexTreeForm,
    MaterialDetailCompositionList,
    MaterialSubmitForm
  },
  data() {
    return {
      bigTypeName: undefined,
      midTypeName: undefined,
      leafTypeName: undefined,
      idItems: [],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料料号基础信息管理列表
      list: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 选中行
      currentRow: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 3,
        project: null,
        compid: null,
        matrlno: null,
        matrlIndexid: null,
        invenTorytype: null,
        keyacct: null,
        keyacct2: null,
        cortcode: null,
        enMdesc: null,
        cnmdesc: null,
        nmspec: null,
        nmspecmore: null,
        quality: null,
        picno: null,
        picfile: null,
        unitinv: null,
        requnitinv: null,
        countpre: null,
        watersymbol: null,
        stockway: null,
        unitwgtkg: null,
        unitvolumecc: null,
        unitthkmm: null,
        unitwthmm: null,
        unitlthmm: null,
        unighthmm: null,
        unitlth: null,
        abcClass: null,
        customalno: null,
        circulatchk: null,
        hpricechk: null,
        nationtype: null,
        mostlyexpenD: null,
        selfproduce: null,
        isaccidenTspar: null,
        iswaitscrap: null,
        isperformancejob: null,
        agmanage: null,
        buychk: null,
        iswarehouse: null,
        isnote: null,
        remark: null,
        createTime: [],
        meaway: null,
        bigTypeCode: undefined,
        midTypeCode: undefined,
        leafTypeCode: undefined,
        status: undefined,
        matrlStatus: undefined,
      },
      /** 子表的列表 */
      subTabsName: 'materialTech'
    };
  },
  activated() {
    if (this.$route.params.matrlno) {
      // console.log('activated matrlno:',this.$route.params.matrlno)
      this.queryParams.matrlno = this.$route.params.matrlno;
    }
    this.getList();
  },
  created() {
  },
  methods: {
    async openMaterialSubmitForm2(command) {
      let str = "";
      if (command.type == "3") {
        str = "状态变有效";
        if (command.row.status == "1") {
          this.$modal.msgError("只有无效状态或删除状态可以修改为有效状态!");
          return;
        }
      } else if (command.type == "4") {
        str = "状态变无效";
        if (command.row.status == "2") {
          this.$modal.msgError("只有有效状态或删除状态可以修改为无效状态!");
          return;
        }
      } else {
        str = "状态变删除";
        if (command.row.status == "X") {
          this.$modal.msgError("只有有效状态或无效状态可以修改为删除状态!");
          return;
        }
      }
      const res = await MaterialAuditApi.getNoComplateByParentid(command.row.id, command.type);
      if (!((res.code === 200 || res.code === 0) && res.data == false)) {
        this.$modal.msgError("发起时校验失败!");
        return;
      }
      this.$refs["formRef6"].open(str, command);
    },
    openMaterialSubmitForm(row) {
      let submitList = [];
      if (row) {
        submitList.push(row);
      }
      this.$refs["formRef5"].open(submitList);
    },
    async handleCommand(str, formData, command) {
      const data = {
        parentid: command.row.id,
        status: "1",
        type: command.type,
        auditStatus: "2",
        editReason: formData.changeReason,
        editInfo: str,
        applyUserManagerId: formData.applyUserManagerId,
        applyUserLeaderId: formData.applyUserLeaderId,
      };
      MaterialAuditApi.createMaterialAudit(data).then(response => {
        this.$modal.msgSuccess("发起状态属性变更审批流程成功！");
      });
    },
    async chooseTypeName(type, value) {
      if (value) {
        let res = await MaterialIndexApi.getMaterialIndexByMatrlIndexidAndType(type, value);
        if (res && res.data) {
          if (type == '1') {
            this.bigTypeName = res.data.matrlIndexdesc;
          } else if (type == '2') {
            this.midTypeName = res.data.matrlIndexdesc;
          } else {
            this.leafTypeName = res.data.matrlIndexdesc;
          }
        }
      }
    },
    writebackLeafTypeTree(args) {
      this.queryParams.leafTypeCode = args.matrlIndexid;
      this.leafTypeName = args.matrlIndexdesc;
    },
    openLeafTypeForm() {
      this.$refs["formRef4"].open("3");
    },
    writebackMidTypeTree(args) {
      this.queryParams.midTypeCode = args.matrlIndexid;
      this.midTypeName = args.matrlIndexdesc;
    },
    openMidTypeForm() {
      this.$refs["formRef3"].open("2");
    },
    writebackBigTypeTree(args) {
      this.queryParams.bigTypeCode = args.matrlIndexid;
      this.bigTypeName = args.matrlIndexdesc;
    },
    openBigTypeForm() {
      this.$refs["formRef2"].open("1");
    },
    handleSelectionChange(val) {
      this.idItems = val;
    },
    openQrcodeForm(row) {
      // const items = [];
      let errorStatus = "";
      let ids = "";
      this.idItems.forEach((el, index) => {
        if (el.matrlStatus != '2') {
          errorStatus = errorStatus + "," + el.matrlno;
        } else {
          ids = ids + "," + el.id;
        }
      });
      if (errorStatus.length > 0) {
        errorStatus = errorStatus.slice(1);
        this.$modal.msgError("必须选择状态是【审批通过】的数据！料号为【" + errorStatus + "】的数据不符合要求！");
        return;
      }
      if (ids.length == 0) {
        this.$modal.msgWarning("请选择数据后再打印！");
        return;
      }
      ids = ids.slice(1);
      // 使用 Vue Router 的 resolve 方法生成带查询参数的 URL
      const {href} = this.$router.resolve({
        name: 'BasicQrcodePrint',
        query: {ids: ids} // 查询参数
      });
      // 打开新页面
      window.open(href, '_blank');
    },
    async applyMaterial(row) {
      await this.$modal.confirm('是否确认发起申请?')
      const res = await MaterialApi.applyMaterial(row.id);
      await this.getList();
      this.$modal.msgSuccess("发起申请成功");
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        this.currentRow = {};
        const res = await MaterialApi.getMaterialPage(this.queryParams);
        this.list = res.data.list;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.bigTypeName = undefined;
      this.midTypeName = undefined;
      this.leafTypeName = undefined;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 添加/修改操作 */
    openForm(id, type) {
      this.$refs["formRef"].open(id, type);
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const id = row.id;
      await this.$modal.confirm('是否确认删除物料料号基础信息管理编号为"' + id + '"的数据项?')
      try {
        await MaterialApi.deleteMaterial(id);
        await this.getList();
        this.$modal.msgSuccess("删除成功");
      } catch {
      }
    },
    /** 导出按钮操作 */
    async handleExport() {
      await this.$modal.confirm('是否确认导出物料料号基础信息管理数据项?');
      try {
        this.exportLoading = true;
        const data = await MaterialApi.exportMaterialExcel(this.queryParams);
        this.$download.excel(data, '物料料号基础信息管理.xls');
      } catch {
      } finally {
        this.exportLoading = false;
      }
    },
    /** 选中行操作 */
    handleCurrentChange(row) {
      this.currentRow = row;
      /** 子表的列表 */
      this.subTabsName = 'materialTech';
    },
  }
};
</script>
<style>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
