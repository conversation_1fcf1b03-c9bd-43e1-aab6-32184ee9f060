<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="75%" v-dialogDrag append-to-body>
      <el-form ref="formRef" size="small" :inline="true" :model="formData" :rules="formRules" v-loading="formLoading"
               label-width="110px">
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="大类" prop="bigTypeCode">
              <el-select v-model="formData.bigTypeCode" placeholder="请选择大类" clearable size="small"
                         @change="queryMidTypeList" style="width: 190px" :disabled="viewFlag">
                <el-option v-for="dict in bigTypeList"
                           :key="dict.matrlIndexid" :label="dict.matrlIndexid+'-'+dict.matrlIndexdesc"
                           :value="dict.matrlIndexid"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="中类" prop="midTypeCode">
              <el-select v-model="formData.midTypeCode" placeholder="请选择中类" clearable size="small"
                         @change="queryLeafTypeList" style="width: 190px" :disabled="viewFlag">
                <el-option v-for="dict in midTypeList"
                           :key="dict.matrlIndexid" :label="dict.matrlIndexid+'-'+dict.matrlIndexdesc"
                           :value="dict.matrlIndexid"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="叶类" prop="leafTypeCode" v-if="this.formData.invenTorytype != 'R'">
              <el-select v-model="formData.leafTypeCode" placeholder="请选择叶类" clearable size="small"
                         @change="chooseLeafType" style="width: 190px" :disabled="viewFlag">
                <el-option v-for="dict in leafTypeList"
                           :key="dict.matrlIndexid" :label="dict.matrlIndexid+'-'+dict.matrlIndexdesc"
                           :value="dict.matrlIndexid"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="状态" prop="matrlStatus">
              <el-select v-model="formData.matrlStatus" placeholder="请选择状态" clearable size="small"
                         @change="queryMatrlIndex" disabled style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BPM_TASK_STATUS)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="物料类型" prop="invenTorytype">
              <el-select v-model="formData.invenTorytype" placeholder="请选择物料类型" clearable size="small"
                         @change="queryMatrlIndex" disabled style="width: 190px">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INVEN_TORY_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="料号" prop="matrlno">
              <el-input v-model="formData.matrlno" placeholder="不填写时，系统默认赋值" :disabled="viewFlag" style="width: 190px"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="中文品名" prop="cnmdesc">
              <el-input v-model="formData.cnmdesc" placeholder="请输入中文品名" style="width: 190px" :disabled="viewFlag"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="英文品名" prop="enMdesc">
              <el-input v-model="formData.enMdesc" placeholder="请输入英文品名" style="width: 190px" :disabled="viewFlag"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="计量单位" prop="unitinv">
              <el-select v-model="formData.unitinv" placeholder="请选择计量单位" clearable size="small"
                         style="width: 190px" :disabled="viewFlag">
                <el-option v-for="dict in this.getDictDatas(DICT_TYPE.STOCK_UNIT)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div>
          <el-row :gutter="20">
            <el-col :span="8" :xs="24">
              <el-form-item label="单重" prop="singleWgt" v-if="formData.invenTorytype != 'R'">
                <el-input v-model="formData.singleWgt" placeholder="请输入单重" style="width: 100px" :disabled="viewFlag"/>
                <el-select v-model="formData.singleWgtUnit" placeholder="请选择" clearable size="small"
                           style="width: 90px" :disabled="viewFlag">
                  <el-option v-for="dict in this.unitList"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="规格型号" prop="nmspec">
                <el-input v-model="formData.nmspec" placeholder="请输入规格型号" style="width: 190px"
                          :disabled="viewFlag" @blur="writeShortDesc"/>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="材质" prop="quality" v-if="formData.invenTorytype != 'R'">
                <el-input v-model="formData.quality" placeholder="请输入材质" style="width: 190px"
                          :disabled="viewFlag" @blur="writeShortDesc"/>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="图号" prop="picno" v-if="formData.invenTorytype != 'R'">
                <el-input v-model="formData.picno" placeholder="请输入图号" style="width: 190px"
                          :disabled="viewFlag" @blur="writeShortDesc"/>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="物料属性" prop="matrlType" v-if="formData.invenTorytype != 'R'">
                <el-select v-model="formData.matrlType" placeholder="请选择物料属性" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MATRL_TYPE)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
<!--            <el-col :span="8" :xs="24">-->
<!--              <el-form-item label="制造商或品牌" prop="brand" v-if="formData.invenTorytype != 'R'">-->
<!--                <el-input v-model="formData.brand" placeholder="请输入制造商或品牌" style="width: 190px" :disabled="viewFlag"/>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="8" :xs="24">-->
<!--              <el-form-item label="老物料代码" prop="oldMatrlno">-->
<!--                <el-input v-model="formData.oldMatrlno" placeholder="请输入老物料代码" style="width: 190px" :disabled="viewFlag"/>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="8" :xs="24">
              <el-form-item label="入储重量依据" prop="stockway" v-if="formData.invenTorytype == 'R'">
                <el-select v-model="formData.stockway" placeholder="请选择入储重量依据" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.STOCK_WAY)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" :xs="24">
              <el-form-item label="申请类别" prop="applyType">
                <el-select v-model="formData.applyType" placeholder="请选择申请类别" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.APPLY_TYPE)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16" :xs="24">
              <el-form-item label="申请原因" prop="applyReason">
                <el-input v-model="formData.applyReason" type="textarea" :rows="1" placeholder="请输入申请原因(简述）"
                          style="width: 450px" :disabled="viewFlag"/>
              </el-form-item>
            </el-col>
          </el-row>
<!--          <el-row :gutter="20">-->
<!--            <el-col :span="24" :xs="24">-->
<!--              <el-form-item label="原物料描述" prop="nmspecmore">-->
<!--                <el-input v-model="formData.nmspecmore" type="textarea" :rows="3" placeholder="请输入原物料描述"-->
<!--                          style="width: 800px" :disabled="viewFlag"/>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
          <el-row :gutter="20">
            <el-col :span="24" :xs="24">
              <el-form-item label="短描述" prop="shortDesc" v-if="formData.invenTorytype != 'R'">
                <el-input v-model="formData.shortDesc" type="textarea" :rows="3" placeholder="请输入短描述"
                          style="width: 800px" :disabled="viewFlag"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" :xs="24">
              <el-form-item label="长描述" prop="longDesc" v-if="formData.invenTorytype != 'R'">
                <el-input v-model="formData.longDesc" type="textarea" :rows="3" placeholder="请输入长描述"
                          style="width: 800px" :disabled="viewFlag"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" :xs="24">
              <el-form-item label="物料重要程度" prop="matrlDegree" v-if="formData.invenTorytype != 'R'">
                <el-select v-model="formData.matrlDegree" placeholder="请选择物料重要程度" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MATRL_DEGREE)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="国产/进口" prop="isMadeInChina" v-if="formData.invenTorytype != 'R'">
                <el-select v-model="formData.isMadeInChina" placeholder="请选择国产/进口" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.IS_MADEINCHINA)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="水分标识符" prop="waterFlag" v-if="formData.invenTorytype == 'R'">
                <el-select v-model="formData.waterFlag" placeholder="请选择水分标识符" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PMS_MATRLNO_WATER)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="品位" prop="grade" v-if="formData.invenTorytype == 'R'">
                <el-select v-model="formData.grade" placeholder="请选择品位" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.GRADE)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24">
              <el-form-item label="特性代码" prop="specialCode" v-if="formData.invenTorytype == 'R'">
                <el-select v-model="formData.specialCode" placeholder="请选择特性代码" clearable size="small"
                           style="width: 190px" :disabled="viewFlag">
                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MRKEYACCT)"
                             :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="创建者" prop="creator">
              <el-input v-model="formData.creator" placeholder="提交后系统赋值" style="width: 190px" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="创建者姓名" prop="createEmpNo">
              <el-input v-model="formData.createEmpNo" placeholder="提交后系统赋值" style="width: 190px" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="新增时间" prop="createTime">
              <el-date-picker clearable v-model="formData.createTime" type="datetime" value-format="timestamp"
                              placeholder="提交后系统赋值" style="width: 190px" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading || viewFlag">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as MaterialApi from '@/api/basic/material';
import FileUpload from '@/components/FileUpload';
import TreeSelect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as MaterialIndexApi from '@/api/basic/materialindex';

export default {
  name: "MaterialForm",
  components: {
    TreeSelect,
    FileUpload,
  },
  data() {
    return {
      viewFlag: false,
      unitList: [],
      bigTypeList: [],
      midTypeList: [],
      leafTypeList: [],
      // 弹出层标题
      dialogTitle: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
      formLoading: false,
      // 表单参数
      formData: {
        id: undefined,
        project: undefined,
        compid: undefined,
        matrlno: undefined,
        matrlIndexid: undefined,
        invenToryType: undefined,
        keyacct: undefined,
        keyacct2: undefined,
        cortcode: undefined,
        enMdesc: undefined,
        cnmdesc: undefined,
        nmspec: undefined,
        nmspecmore: undefined,
        quality: undefined,
        picno: undefined,
        picfile: undefined,
        unitinv: undefined,
        requnitinv: undefined,
        countpre: undefined,
        watersymbol: undefined,
        stockway: undefined,
        unitwgtkg: undefined,
        unitvolumecc: undefined,
        unitthkmm: undefined,
        unitwthmm: undefined,
        unitlthmm: undefined,
        unighthmm: undefined,
        unitlth: undefined,
        abcClass: undefined,
        customalno: undefined,
        circulatchk: undefined,
        hpricechk: undefined,
        nationtype: undefined,
        mostlyexpenD: undefined,
        selfproduce: undefined,
        isaccidenTspar: undefined,
        iswaitscrap: undefined,
        isperformancejob: undefined,
        agmanage: undefined,
        buychk: undefined,
        iswarehouse: undefined,
        isnote: undefined,
        remark: undefined,
        meaway: undefined,
        matrlIndexName: undefined,
        leafTypeCode: undefined,
        leafTypeName: undefined,
        midTypeCode: undefined,
        midTypeName: undefined,
        bigTypeCode: undefined,
        bigTypeName: undefined,
        singleWgt: undefined,
        singleWgtUnit: undefined,
        matrlType: undefined,
        brand: undefined,
        oldMatrlno: undefined,
        applyType: undefined,
        applyReason: undefined,
        shortDesc: undefined,
        longDesc: undefined,
        matrlDegree: undefined,
        isMadeInChina: undefined,
        matrlStatus: undefined,
        waterFlag: undefined,
        grade: undefined,
        specialCode: undefined,
      },
      // 表单校验
      formRules: {
        invenTorytype: [
          {required: true, message: '请选择物料类型', trigger: 'blur'},
        ],
        matrlIndexid: [
          {required: true, message: '请选择物料分类', trigger: 'blur'},
        ],
        cnmdesc: [
          {required: true, message: '请输入中文品名', trigger: 'blur'},
        ],
        nmspec: [
          {required: true, message: '请输入规格型号', trigger: 'blur'},
        ],
        picno: [
          {required: true, message: '请输入图号', trigger: 'blur'},
        ],
        quality: [
          {required: true, message: '请输入材质', trigger: 'blur'},
        ],
        unitinv: [
          {required: true, message: '请选择计量单位', trigger: 'blur'},
        ],
        meaway: [
          {required: true, message: '请选择计重方式', trigger: 'blur'},
        ],
        leafTypeCode: [
          {required: true, message: '请选择大类', trigger: 'blur'},
        ],
        midTypeCode: [
          {required: true, message: '请选择中类', trigger: 'blur'},
        ],
        bigTypeCode: [
          {required: true, message: '请选择叶类', trigger: 'blur'},
        ],
        singleWgt: [
          {required: true, message: '请输入单重', trigger: 'blur'},
        ],
        singleWgtUnit: [
          {required: true, message: '请选择单重单位', trigger: 'blur'},
        ],
        applyType: [
          {required: true, message: '请选择申请类别', trigger: 'blur'},
        ],
        applyReason: [
          {required: true, message: '请输入申请原因', trigger: 'blur'},
        ],
        matrlDegree: [
          {required: true, message: '请选择物料重要程度', trigger: 'blur'},
        ],
        isMadeInChina: [
          {required: true, message: '请选择国产/进口', trigger: 'blur'},
        ],
        matrlStatus: [
          {required: true, message: '请选择状态', trigger: 'blur'},
        ],
        matrlType: [
          {required: true, message: '请选择物料属性', trigger: 'blur'},
        ],
      },
      materialIndexTree: [], // 树形结构
      // 树形查询参数
      queryTreeParams: {
        matrlIndexnum: "1"
      },
    };
  },
  methods: {
    writeShortDesc() {
      let str = "";
      if (this.formData.leafTypeCode) {
        str = str + "叶类：" + this.formData.leafTypeCode + "-" + this.formData.leafTypeName + "；";
      }
      if (this.formData.nmspec) {
        str = str + "规格型号：" + this.formData.nmspec + "；";
      }
      if (this.formData.quality) {
        str = str + "材质：" + this.formData.quality + "；";
      }
      if (this.formData.picno) {
        str = str + "图号：" + this.formData.picno + "；";
      }
      this.formData.shortDesc = str;
    },
    async chooseLeafType(args) {
      for (let i = 0; i <= this.leafTypeList.length; i++) {
        if (this.leafTypeList[i].matrlIndexid == args) {
          this.formData.leafTypeName = this.leafTypeList[i].matrlIndexdesc;
          break;
        }
      }
      const leafItem = this.leafTypeList.find(item => item.matrlIndexid == args);
      this.formData.matrlno = leafItem.matrlPrefix;
      this.writeShortDesc();
    },
    async queryLeafTypeList(args) {
      for (let i = 0; i <= this.midTypeList.length; i++) {
        if (this.midTypeList[i].matrlIndexid == args) {
          this.formData.midTypeName = this.midTypeList[i].matrlIndexdesc;
          break;
        }
      }
      this.formData.leafTypeCode = undefined;
      this.formData.leafTypeName = undefined;
      this.formData.matrlno = undefined;

      if(this.formData.invenTorytype == "R"){
        const midItem = this.midTypeList.find(item => item.matrlIndexid === args);
        this.formData.matrlno = midItem.matrlPrefix;
      }

      const res = await MaterialIndexApi.listByParentMatrlIndexid(args);
      this.leafTypeList = res.data;
    },
    async queryMidTypeList(args) {
      for (let i = 0; i <= this.bigTypeList.length; i++) {
        if (this.bigTypeList[i].matrlIndexid == args) {
          this.formData.bigTypeName = this.bigTypeList[i].matrlIndexdesc;
          break;
        }
      }
      this.formData.midTypeCode = undefined;
      this.formData.midTypeName = undefined;
      this.formData.leafTypeCode = undefined;
      this.formData.leafTypeName = undefined;
      this.formData.matrlno = undefined;

      const bigItem = this.bigTypeList.find(item => item.matrlIndexid === args);
      this.formData.invenTorytype = bigItem.invenToryType;
      console.info(this.formData.invenTorytype);

      const res = await MaterialIndexApi.listByParentMatrlIndexid(args);
      this.midTypeList = res.data;
    },
    // 查询物料分类
    async queryMatrlIndex(value) {
      await this.getMaterialIndexTree(value);
    },
    /** 转换物料料号索引数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.matrlIndexid,
        label: node.matrlIndexid + '-' + node.matrlIndexdesc,
        children: node.children
      };
    },
    /** 打开弹窗 */
    async open(id, type) {
      this.reset();
      // 物料架构 大类
      this.bigTypeList = [];
      // 单重单位
      let unitListTemp = this.getDictDatas("stock_unit");
      this.unitList = [];
      for (let i = 0; i <= unitListTemp.length; i++) {
        if (unitListTemp[i] && (unitListTemp[i].value == "1" || unitListTemp[i].value == "2"
          || unitListTemp[i].value == "3" || unitListTemp[i].value == "4")) {
          this.unitList.push(unitListTemp[i]);
        }
      }
      if (type == 'view') {
        this.viewFlag = true;
        this.dialogTitle = "查看料号基础信息";
      } else {
        this.viewFlag = false;
        const resIndex = await MaterialIndexApi.getMaterialIndexList(this.queryTreeParams);
        this.bigTypeList = resIndex.data;
        if (type == 'add') {
          this.dialogTitle = "新增料号基础信息";
        } else {
          this.dialogTitle = "修改料号基础信息";
        }
      }
      this.dialogVisible = true;
      // 修改时，设置数据
      if (id) {
        this.formLoading = true;
        try {
          const res = await MaterialApi.getMaterial(id);
          this.formData = res.data;
          if (type == 'edit') {
            const res1 = await MaterialIndexApi.listByParentMatrlIndexid(this.formData.bigTypeCode);
            this.midTypeList = res1.data;

            const res2 = await MaterialIndexApi.listByParentMatrlIndexid(this.formData.midTypeCode);
            this.leafTypeList = res2.data;
          }
        } finally {
          this.formLoading = false;
        }
      } else {
        this.formData.matrlStatus = "0";
      }
    },
    /** 获得物料料号索引树 */
    async getMaterialIndexTree(invenToryType) {
      this.queryTreeParams.invenToryType = invenToryType;
      const res = await MaterialIndexApi.getMaterialIndexList(this.queryTreeParams);
      this.materialIndexTree = [];
      this.materialIndexTree.push(...this.handleTree(res.data, "id"));
      console.log('materialIndexTree', this.materialIndexTree)
    },
    /** 提交按钮 */
    async submitForm() {
      // 校验主表
      await this.$refs["formRef"].validate();
      this.formLoading = true;
      try {
        const data = this.formData;
        // 修改的提交
        if (data.id) {
          await MaterialApi.updateMaterial(data);
          this.$modal.msgSuccess("修改成功");
          this.dialogVisible = false;
          this.$emit('success');
          return;
        }
        // 添加的提交
        await MaterialApi.createMaterial(data);
        this.$modal.msgSuccess("新增成功");
        this.dialogVisible = false;
        this.$emit('success');
      } finally {
        this.formLoading = false;
      }
    },
    /** 表单重置 */
    reset() {
      this.formData = {
        id: undefined,
        project: undefined,
        compid: undefined,
        matrlno: undefined,
        matrlIndexid: undefined,
        invenTorytype: undefined,
        keyacct: undefined,
        keyacct2: undefined,
        cortcode: undefined,
        enMdesc: undefined,
        cnmdesc: undefined,
        nmspec: undefined,
        nmspecmore: undefined,
        quality: undefined,
        picno: undefined,
        picfile: undefined,
        unitinv: undefined,
        requnitinv: undefined,
        countpre: undefined,
        watersymbol: undefined,
        stockway: undefined,
        unitwgtkg: undefined,
        unitvolumecc: undefined,
        unitthkmm: undefined,
        unitwthmm: undefined,
        unitlthmm: undefined,
        unighthmm: undefined,
        unitlth: undefined,
        abcClass: undefined,
        customalno: undefined,
        circulatchk: undefined,
        hpricechk: undefined,
        nationtype: undefined,
        mostlyexpenD: undefined,
        selfproduce: undefined,
        isaccidenTspar: undefined,
        iswaitscrap: undefined,
        isperformancejob: undefined,
        agmanage: undefined,
        buychk: undefined,
        iswarehouse: undefined,
        isnote: undefined,
        remark: undefined,
        meaway: undefined,
        matrlIndexName: undefined,
        leafTypeCode: undefined,
        leafTypeName: undefined,
        midTypeCode: undefined,
        midTypeName: undefined,
        bigTypeCode: undefined,
        bigTypeName: undefined,
        singleWgt: undefined,
        singleWgtUnit: undefined,
        matrlType: undefined,
        brand: undefined,
        oldMatrlno: undefined,
        applyType: undefined,
        applyReason: undefined,
        shortDesc: undefined,
        longDesc: undefined,
        matrlDegree: undefined,
        isMadeInChina: undefined,
        matrlStatus: undefined,
        waterFlag: undefined,
        grade: undefined,
        specialCode: undefined,
      };
      this.resetForm("formRef");
    }
  }
};
</script>
