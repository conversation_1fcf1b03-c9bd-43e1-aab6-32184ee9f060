<template>
  <el-dialog class="user-choose" title="选择人员" :visible.sync="visible" :close-on-click-modal="false"
             width="800px" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
      <el-form-item label="部门" prop="deptId">
        <treeselect style="width: 250px" v-model="queryParams.deptId" :options="deptOptions" :show-count="true" clearable
                    placeholder="请输入单位" :normalizer="normalizer" />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item s>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table ref="table" v-loading="loading" row-key="userId" :data="dataList"
              @row-click="handleSelectRow">
      <el-table-column label="用户名称" align="center" prop="username" min-width="100" show-overflow-tooltip/>
      <el-table-column label="用户昵称" align="center" prop="nickname" min-width="150" show-overflow-tooltip/>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import {listUser} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SelectedArea from "@/components/UserChooseMul/components/SelectedArea.vue";
import {hiddenPartPhone} from "@/utils/common";
import {listSimpleDepts} from "@/api/system/dept";

export default {
  name: "UserChoose",
  components: {Treeselect, SelectedArea},
  props: {
  },
  data() {
    return {
      visible: false,
      loading: false,
      deptOptions :[],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      dataList: [],
      deptOptionsControl: [],
    }
  },
  computed: {
    params() {
      return {
        ...this.queryParams
      }
    }
  },
  methods: {
    hiddenPartPhone,
    reset() {
      this.deptOptionsControl = []
    },
    show() {
      this.visible = true
      this.reset()
      this.getTreeselect();
      this.resetQuery();

    },
    getList() {
      this.loading = true;
      listUser(this.params).then(response => {
        this.dataList = response.data.list
        console.log(response.data)

        this.total = response.data.total
        this.loading = false
      }).catch(e=>{
        console.log(e,'11111')
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = Object.assign({}, this.queryParams)
      this.handleQuery()
    },
    //点击当前行，复选框选中。
    handleSelectRow(rowData) {
      console.log("------11",rowData)
      this.$emit('selected', rowData)
      this.visible = false
    },
    // 格式化部门的下拉框
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    getTreeselect() {
      listSimpleDepts().then(response => {
        // 处理 deptOptions 参数
        this.deptOptions = [];
        this.deptOptions.push(...this.handleTree(response.data, "id"));
      });
      // listSimplePosts().then(response => {
      //   // 处理 postOptions 参数
      //   this.postOptions = [];
      //   this.postOptions.push(...response.data);
      // });
    },
  }
}
</script>
<style scoped lang="less">
  .user-choose {
    /deep/ .el-table {
      .el-table__row {
        cursor: pointer;
      }
    }
  }
</style>
