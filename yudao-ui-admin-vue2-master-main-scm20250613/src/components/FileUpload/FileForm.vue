<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog title="新增文件" :visible.sync="dialogFileVisible" width="30%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="125px">
        <el-form-item label="文件所属表" prop="filetable">
          <el-input v-model="formData.filetable" style="width: 180px" :disabled="filetableReadOnly"/>
        </el-form-item>
        <el-form-item label="文件所属表主键" prop="fileparentid">
          <el-input v-model="formData.fileparentid" style="width: 180px" :disabled="fileparentidReadOnly"/>
        </el-form-item>
        <el-form-item label="文件上传类型" prop="fileuploadtype">
          <el-select v-model="formData.fileuploadtype" placeholder="请选择" style="width: 180px">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.FILE_UPLOAD_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" style="width: 180px" plain icon="el-icon-plus" size="mini" @click="handleAdd">上传文件</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFileVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="上传文件" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="5" drag
                 :headers="upload.headers" :action="upload.url" :data="upload.data" :disabled="upload.isUploading"
                 :on-progress="handleFileUploadProgress"
                 :on-success="handleFileSuccess"
                 :on-error="handleUploadError"
                 :before-upload="handleBeforeUpload">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或 <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> 的文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as FileApi from '@/api/infra/file';
  import {getAccessToken} from "@/utils/auth";
  export default {
    name: "FileForm",
    components: {
                    },
    data() {
      return {
        // 大小限制(MB)
        fileSize: 5,
        filetableReadOnly:false,
        fileparentidReadOnly:false,
        // 是否显示弹出层
        dialogFileVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
                            id: undefined,
                            configId: undefined,
                            name: undefined,
                            path: undefined,
                            url: undefined,
                            type: undefined,
                            size: undefined,
                            filetable: undefined,
                            fileparentid: undefined,
                            fileuploadtype: undefined,
        },
        // 表单校验
        formRules: {
          filetable: [{ required: true, message: '文件所属表不能为空', trigger: 'blur' }],
          fileparentid: [{ required: true, message: '文件所属表主键不能为空，请先新增后再添加附件', trigger: 'blur' }],
          fileuploadtype: [{ required: true, message: '文件上传类型不能为空', trigger: 'blur' }],
        },
        upload: {
          open: false, // 是否显示弹出层
          isUploading: false, // 是否禁用上传
          url: process.env.VUE_APP_BASE_API + "/admin-api/infra/file/upload", // 请求地址
          headers: {Authorization: "Bearer " + getAccessToken()}, // 设置上传的请求头部
          data: {} // 上传的额外数据，用于文件名
        },
      };
    },
    methods: {
      // 上传失败
      handleUploadError(err) {
        this.$modal.msgError("上传失败，请重试");
        this.$modal.closeLoading();
      },
      handleBeforeUpload(file) {
        // 校检文件大小
        if (this.fileSize) {
          const isLt = file.size / 1024 / 1024 < this.fileSize;
          if (!isLt) {
            this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
            return false;
          }
        }
        this.$modal.loading("正在上传文件，请稍候...");
        return true;
      },
      handleFileSuccess(response, file, fileList) {
        // 清理
        this.upload.open = false;
        this.upload.isUploading = false;
        this.dialogFileVisible = false;
        this.$refs.upload.clearFiles();
        // 提示成功，并刷新
        this.$modal.msgSuccess("上传成功");
        this.$modal.closeLoading();
        this.$emit('success');
      },
      /** 处理文件上传中 */
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true; // 禁止修改
      },
      async handleAdd() {
        await this.$refs["formRef"].validate();
        this.upload.open = true;
        this.upload.data={
          "filetable":this.formData.filetable,
          "fileparentid":this.formData.fileparentid,
          "fileuploadtype":this.formData.fileuploadtype,
        };
      },
      /** 打开弹窗 */
     async open(inputFileTable,inputFileTableId) {
        this.reset();
        this.filetableReadOnly=true;
        this.fileparentidReadOnly=true;
        if(inputFileTable && inputFileTable != null && inputFileTable != "undefined" && inputFileTable != ""){
          this.formData.filetable=inputFileTable;
          // this.filetableReadOnly=true;
        }
        if(inputFileTableId && inputFileTableId != null && inputFileTableId != "undefined" && inputFileTableId != ""){
          this.formData.fileparentid=inputFileTableId;
          // this.fileparentidReadOnly=true;
        }
        this.dialogFileVisible = true;
      },
      /** 表单重置 */
      reset() {
        this.formData = {
                            id: undefined,
                            configId: undefined,
                            name: undefined,
                            path: undefined,
                            url: undefined,
                            type: undefined,
                            size: undefined,
                            filetable: undefined,
                            fileparentid: undefined,
                            fileuploadtype: undefined,
        };
        this.resetForm("formRef");
      }
    }
  };
</script>
