/**
 * 物料选择功能测试脚本
 * 用于验证物料选择功能的基本逻辑
 */

// 模拟物料数据
const mockMaterialData = {
  matrlno: 'M001',
  bigTypeCode: 'A',
  midTypeCode: 'B',
  leafTypeCode: 'C',
  cnmdesc: '测试物料中文品名',
  nmspec: '规格型号123',
  quality: '不锈钢',
  picno: 'PIC001',
  unitinv: 'PCS'
};

// 模拟表单数据结构
const mockForm = {
  materialNo: null,
  categoryCode: null,
  materialNameCn: null,
  modelSpec: null,
  materialDesc: null,
  materialType: null,
  drawingNo: null,
  drawingFile: null,
  stockUnit: null,
  statusCode: 'NORMAL',
  sortOrder: 0
};

/**
 * 模拟物料选择处理函数
 * @param {Object} material - 选中的物料对象
 * @param {Object} form - 表单对象
 */
function handleMaterialSelect(material, form) {
  if (material) {
    // 将选中的物料信息填充到表单中
    form.materialNo = material.matrlno;
    form.categoryCode = material.bigTypeCode + material.midTypeCode + material.leafTypeCode;
    form.materialNameCn = material.cnmdesc;
    form.modelSpec = material.nmspec;
    form.materialDesc = material.cnmdesc;
    form.materialType = material.quality;
    form.drawingNo = material.picno;
    form.drawingFile = material.picno;
    form.stockUnit = material.unitinv;
    
    console.log('物料信息填充完成:', form);
    return true;
  }
  return false;
}

/**
 * 验证必填字段是否已填充
 * @param {Object} form - 表单对象
 */
function validateRequiredFields(form) {
  const requiredFields = ['materialNo', 'categoryCode', 'materialNameCn', 'stockUnit'];
  const missingFields = [];
  
  requiredFields.forEach(field => {
    if (!form[field]) {
      missingFields.push(field);
    }
  });
  
  if (missingFields.length > 0) {
    console.error('缺少必填字段:', missingFields);
    return false;
  }
  
  console.log('所有必填字段验证通过');
  return true;
}

/**
 * 运行测试
 */
function runTests() {
  console.log('=== 物料选择功能测试开始 ===');
  
  // 测试1: 正常物料选择流程
  console.log('\n测试1: 正常物料选择流程');
  const testForm1 = { ...mockForm };
  const result1 = handleMaterialSelect(mockMaterialData, testForm1);
  console.log('选择结果:', result1);
  console.log('表单数据:', testForm1);
  
  // 测试2: 必填字段验证
  console.log('\n测试2: 必填字段验证');
  const validationResult = validateRequiredFields(testForm1);
  console.log('验证结果:', validationResult);
  
  // 测试3: 空物料处理
  console.log('\n测试3: 空物料处理');
  const testForm2 = { ...mockForm };
  const result2 = handleMaterialSelect(null, testForm2);
  console.log('空物料选择结果:', result2);
  console.log('表单数据应保持不变:', testForm2);
  
  // 测试4: 品别代码组合
  console.log('\n测试4: 品别代码组合测试');
  const expectedCategoryCode = mockMaterialData.bigTypeCode + 
                               mockMaterialData.midTypeCode + 
                               mockMaterialData.leafTypeCode;
  console.log('期望的品别代码:', expectedCategoryCode);
  console.log('实际的品别代码:', testForm1.categoryCode);
  console.log('品别代码匹配:', expectedCategoryCode === testForm1.categoryCode);
  
  console.log('\n=== 物料选择功能测试完成 ===');
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runTests();
} else {
  // 浏览器环境
  console.log('在浏览器控制台中运行测试...');
  window.runMaterialSelectTests = runTests;
  window.mockMaterialData = mockMaterialData;
  window.handleMaterialSelect = handleMaterialSelect;
  window.validateRequiredFields = validateRequiredFields;
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    handleMaterialSelect,
    validateRequiredFields,
    mockMaterialData,
    runTests
  };
}
