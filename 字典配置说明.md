# 集采生效状态字典配置说明

## 问题描述
集采信息主表中的生效状态字段 `effectiveStatus` 返回数据为 0，但在前端页面中不显示对应的状态文本。

## 问题原因
系统中缺少 `PROCUREMENT_EFFECTIVE_STATUS` 字典的数据配置。虽然前端代码中已经定义了字典类型，但后台系统中没有配置相应的字典数据。

## 解决方案

### 1. 前端代码修复（已完成）
- 修复了 `ProcurementCollectionForm.vue` 中选择器的值类型问题
- 在 `index.vue` 中添加了 `DICT_TYPE` 的导入和配置

### 2. 后台字典配置（需要手动操作）

请按以下步骤在系统管理中配置字典：

#### 步骤1：添加字典类型
1. 登录系统管理后台
2. 进入 `系统管理` -> `字典管理`
3. 点击 `新增` 按钮
4. 填写以下信息：
   - **字典名称**: `集采生效状态`
   - **字典类型**: `PROCUREMENT_EFFECTIVE_STATUS`
   - **状态**: `正常`
   - **备注**: `集采信息主表生效状态字典`

#### 步骤2：添加字典数据
点击刚创建的字典类型，进入字典数据管理页面，添加以下三条数据：

**数据1：**
- **数据标签**: `未生效`
- **数据键值**: `0`
- **显示排序**: `1`
- **状态**: `正常`
- **颜色类型**: `info`
- **备注**: `未生效状态`

**数据2：**
- **数据标签**: `已生效`
- **数据键值**: `1`
- **显示排序**: `2`
- **状态**: `正常`
- **颜色类型**: `success`
- **备注**: `已生效状态`

**数据3：**
- **数据标签**: `已失效`
- **数据键值**: `2`
- **显示排序**: `3`
- **状态**: `正常`
- **颜色类型**: `danger`
- **备注**: `已失效状态`

### 3. 验证配置
配置完成后：
1. 刷新前端页面
2. 查看集采信息主表列表，生效状态列应该正常显示对应的状态文本
3. 在编辑表单中，生效状态下拉框也应该正常显示选项

## 技术说明

### 字典工作原理
1. 前端通过 `dict-tag` 组件显示字典值
2. 组件根据 `DICT_TYPE.PROCUREMENT_EFFECTIVE_STATUS` 和 `value` 查找对应的标签文本
3. 字典数据通过 `listSimpleDictDatas` API 从后台加载到前端缓存中

### 相关文件
- `src/utils/dict.js` - 字典类型定义
- `src/views/pms/procurementcollection/index.vue` - 列表页面
- `src/views/pms/procurementcollection/ProcurementCollectionForm.vue` - 表单页面
- `src/store/modules/dict.js` - 字典数据缓存管理

## 注意事项
1. 字典类型 `PROCUREMENT_EFFECTIVE_STATUS` 必须与前端代码中定义的完全一致
2. 数据键值必须与后端返回的数值类型一致（0、1、2）
3. 配置完成后可能需要清除浏览器缓存或重新登录以刷新字典缓存
