<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料明细弹窗选择物料信息功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409EFF;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        .feature-section {
            margin-bottom: 30px;
        }
        .feature-title {
            color: #303133;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #409EFF;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 10px;
            line-height: 1.6;
            color: #606266;
        }
        .code-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .code-title {
            color: #68d391;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .code-content {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .highlight {
            background: #4a5568;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .demo-image {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 2px dashed #d0d7de;
        }
        .demo-image p {
            color: #666;
            font-style: italic;
        }
        .implementation-steps {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #fa8c16;
        }
        .step-number {
            color: #fa8c16;
            font-weight: bold;
            margin-right: 10px;
        }
        .file-path {
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>物料明细弹窗选择物料信息功能演示</h1>
        
        <div class="feature-section">
            <div class="feature-title">功能概述</div>
            <div class="feature-list">
                <p>本功能为采购收集系统的物料明细表单添加了物料选择功能，用户可以通过弹窗选择物料，系统会自动填充相关信息。</p>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">主要特性</div>
            <div class="feature-list">
                <ul>
                    <li><strong>物料选择弹窗：</strong>集成了现有的MaterialSearch组件，提供物料搜索和选择功能</li>
                    <li><strong>自动填充：</strong>选择物料后，自动填充料号、品别代码、中文品名、型号规格等信息</li>
                    <li><strong>只读字段：</strong>物料相关字段设为只读，确保数据一致性和准确性</li>
                    <li><strong>表单验证：</strong>选择物料后自动触发相关字段的表单验证</li>
                    <li><strong>用户友好：</strong>在料号输入框右侧添加"选择物料"按钮，操作直观</li>
                </ul>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">实现步骤</div>
            <div class="implementation-steps">
                <div class="step">
                    <span class="step-number">步骤1:</span>
                    修改物料明细表单模板，在料号输入框添加选择按钮
                    <br><span class="file-path">src/views/pms/procurementcollection/components/ProcurementCollectionItemForm.vue</span>
                </div>
                <div class="step">
                    <span class="step-number">步骤2:</span>
                    将物料相关字段设置为只读状态，防止手动修改
                </div>
                <div class="step">
                    <span class="step-number">步骤3:</span>
                    导入MaterialSearch组件并注册
                </div>
                <div class="step">
                    <span class="step-number">步骤4:</span>
                    添加物料选择方法和处理选择结果的方法
                </div>
                <div class="step">
                    <span class="step-number">步骤5:</span>
                    实现物料信息的自动填充和表单验证
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">核心代码实现</div>
            
            <div class="code-section">
                <div class="code-title">模板部分 - 添加选择按钮</div>
                <div class="code-content">
&lt;el-form-item label="料号" prop="materialNo"&gt;
  &lt;el-input v-model="form.materialNo" placeholder="请输入料号" <span class="highlight">readonly</span>&gt;
    &lt;el-button slot="append" icon="el-icon-search" <span class="highlight">@click="openMaterialSelect"</span>&gt;选择物料&lt;/el-button&gt;
  &lt;/el-input&gt;
&lt;/el-form-item&gt;
                </div>
            </div>

            <div class="code-section">
                <div class="code-title">脚本部分 - 组件导入和方法实现</div>
                <div class="code-content">
import MaterialSearch from "@/views/pms/pomain/components/MaterialSearch";

export default {
  components: {
    <span class="highlight">MaterialSearch</span>
  },
  methods: {
    /** 打开物料选择弹窗 */
    <span class="highlight">openMaterialSelect()</span> {
      this.$refs.materialSearch.open('M'); // 'M' 表示物料类型
    },
    /** 处理物料选择结果 */
    <span class="highlight">handleMaterialSelect(material)</span> {
      if (material) {
        // 自动填充物料信息
        this.form.materialNo = material.matrlno;
        this.form.categoryCode = material.bigTypeCode + material.midTypeCode + material.leafTypeCode;
        this.form.materialNameCn = material.cnmdesc;
        // ... 其他字段填充
      }
    }
  }
}
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">使用流程</div>
            <div class="feature-list">
                <ol>
                    <li>用户点击"新增物料明细"按钮，打开物料明细表单弹窗</li>
                    <li>在料号输入框右侧点击"选择物料"按钮</li>
                    <li>系统打开物料选择弹窗，显示物料列表</li>
                    <li>用户可以通过料号、中文品名等条件搜索物料</li>
                    <li>选择目标物料后，点击确定</li>
                    <li>系统自动将物料信息填充到表单的各个字段中</li>
                    <li>用户确认信息无误后，点击确定保存</li>
                </ol>
            </div>
        </div>

        <div class="demo-image">
            <p>📋 物料选择弹窗界面演示</p>
            <p>（实际使用时会显示物料搜索和选择界面）</p>
        </div>

        <div class="feature-section">
            <div class="feature-title">技术优势</div>
            <div class="feature-list">
                <ul>
                    <li><strong>复用性：</strong>利用现有的MaterialSearch组件，无需重复开发</li>
                    <li><strong>一致性：</strong>确保物料信息的准确性和一致性</li>
                    <li><strong>用户体验：</strong>简化用户操作，减少手动输入错误</li>
                    <li><strong>维护性：</strong>代码结构清晰，易于维护和扩展</li>
                </ul>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">访问方式</div>
            <div class="feature-list">
                <p>在开发环境中，可以通过以下URL访问演示页面：</p>
                <div class="code-section">
                    <div class="code-content">
http://localhost:端口号/pms/procurement-collection/demo
                    </div>
                </div>
                <p>或者直接访问采购收集管理页面，点击新增物料明细按钮体验完整功能。</p>
            </div>
        </div>
    </div>
</body>
</html>
